import './css/style.css';

import { Inter } from 'next/font/google';
import TopBanner from '@/components/TopBanner';
import { I18nProvider } from '@/lib/i18n';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

export const metadata = {
  title: 'Mysta - Let AI Run the Web for You',
  description:
    'Ask anything. Automate everything. From insight to execution, AI navigates the web for you with precision and speed.',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html className="scroll-smooth">
      <body
        className={`${inter.variable} bg-gray-50 font-inter tracking-tight text-gray-900 antialiased`}
      >
        <I18nProvider>
          <div className="flex min-h-screen flex-col overflow-hidden supports-[overflow:clip]:overflow-clip">
            {children}
          </div>
          <TopBanner />
        </I18nProvider>
      </body>
    </html>
  );
}
