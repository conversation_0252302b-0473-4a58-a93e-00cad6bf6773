@import 'tailwindcss';

/* Additional styles */
@import './additional-styles/utility-patterns.css' layer(components);
@import './additional-styles/theme.css';

@plugin "@tailwindcss/forms" {
  strategy: base;
}

@theme {
  --font-inter: Inter, sans-serif;
  --font-mono: Roboto Mono, monospace;

  --text-xs: 0.75rem;
  --text-xs--line-height: 1.5;
  --text-sm: 0.875rem;
  --text-sm--line-height: 1.5715;
  --text-base: 1rem;
  --text-base--line-height: 1.5;
  --text-base--letter-spacing: -0.017em;
  --text-lg: 1.125rem;
  --text-lg--line-height: 1.5;
  --text-lg--letter-spacing: -0.017em;
  --text-xl: 1.25rem;
  --text-xl--line-height: 1.5;
  --text-xl--letter-spacing: -0.017em;
  --text-2xl: 1.5rem;
  --text-2xl--line-height: 1.415;
  --text-2xl--letter-spacing: -0.037em;
  --text-3xl: 1.875rem;
  --text-3xl--line-height: 1.3333;
  --text-3xl--letter-spacing: -0.037em;
  --text-4xl: 2.25rem;
  --text-4xl--line-height: 1.2777;
  --text-4xl--letter-spacing: -0.037em;
  --text-5xl: 3rem;
  --text-5xl--line-height: 1;
  --text-5xl--letter-spacing: -0.037em;
  --text-6xl: 4rem;
  --text-6xl--line-height: 1;
  --text-6xl--letter-spacing: -0.037em;
  --text-7xl: 4.5rem;
  --text-7xl--line-height: 1;
  --text-7xl--letter-spacing: -0.037em;

  --animate-code-1: code-1;
  --animate-code-2: code-2;
  --animate-code-3: code-3;
  --animate-code-4: code-4;
  --animate-code-5: code-5;
  --animate-code-6: code-6;
  --animate-breath: breath;
  --animate-float: float;
  --animate-line: line;
  --animate-infinite-scroll: infinite-scroll;  

  @keyframes code-1 {
    0% {
      opacity: 0;
    }
    2.5% {
      opacity: 1;
    }
    97.5% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes code-2 {
    16.2% {
      opacity: 0;
    }
    18.75% {
      opacity: 1;
    }
    97.5% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes code-3 {
    32.5% {
      opacity: 0;
    }
    35% {
      opacity: 1;
    }
    97.5% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes code-4 {
    48.75% {
      opacity: 0;
    }
    51.25% {
      opacity: 1;
    }
    97.5% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes code-5 {
    65% {
      opacity: 0;
    }
    72.5% {
      opacity: 1;
    }
    97.5% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes code-6 {
    81.25% {
      opacity: 0;
    }
    83.75% {
      opacity: 1;
    }
    97.5% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes breath {
    0%,
    100% {
      transform: scale(0.95);
    }
    50% {
      transform: scale(1.1);
    }
  }
  @keyframes float {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5%);
    }
  }
  @keyframes line {
    0%,
    100% {
      left: 0;
      opacity: 0;
    }
    50% {
      left: 100%;
      transform: translateX(-100%);
    }
    10%,
    40%,
    60%,
    90% {
      opacity: 0;
    }
    25%,
    75% {
      opacity: 1;
    }
  }
  @keyframes infinite-scroll {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(-100%);
    }
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}