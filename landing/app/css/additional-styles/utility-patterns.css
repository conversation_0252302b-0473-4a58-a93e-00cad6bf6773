/* Buttons */
.btn,
.btn-sm {
    @apply text-sm font-medium inline-flex items-center justify-center rounded-lg whitespace-nowrap transition-all;
}

.btn {
    @apply px-4 py-[11px] shadow-lg;
}

.btn-sm {
    @apply px-3 py-[5px] shadow-sm;
}

/* Forms */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

.form-input,
.form-textarea,
.form-multiselect,
.form-select,
.form-checkbox,
.form-radio {
    @apply border border-gray-200 shadow-xs rounded-lg focus:ring-0 focus:ring-offset-0;
}

.form-input,
.form-textarea,
.form-multiselect,
.form-select {
    @apply bg-white text-sm py-2.5 px-4 focus:border-blue-300;
}

.form-input,
.form-textarea {
    @apply placeholder-gray-400;
}

.form-select {
    @apply pr-10;
}

.form-checkbox,
.form-radio {
    @apply text-gray-800 checked:bg-blue-500 checked:border-transparent focus-visible:not-checked:border-blue-300;
}

.form-checkbox {
    @apply rounded-xs;
}