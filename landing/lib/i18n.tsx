'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

import enTranslations from '@/locales/en.json';
import zhTranslations from '@/locales/zh.json';

type Locale = 'en' | 'zh';

interface TranslationKeys {
  common: {
    signIn: string;
    addToChrome: string;
    language: string;
    english: string;
    chinese: string;
  };
  hero: {
    title: string;
    subtitle: string;
    description: string;
  };
  features: {
    sectionTitle: string;
    sectionSubtitle: string;
    feature1: {
      title: string;
      description: string;
    };
    feature2: {
      title: string;
      description: string;
    };
    feature3: {
      title: string;
      description: string;
    };
    showcaseTitle: string;
    showcaseSubtitle: string;
    showcase1: {
      title: string;
      description: string;
    };
    showcase2: {
      title: string;
      description: string;
    };
  };
  cta: {
    title: string;
  };
  topBanner: {
    text: string;
    clickToJoin: string;
    close: string;
  };
  footer: {
    allRightsReserved: string;
    legal: string;
    terms: string;
    privacy: string;
  };
}

interface I18nContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: TranslationKeys;
}

const translations: Record<Locale, TranslationKeys> = {
  en: enTranslations,
  zh: zhTranslations,
};

const I18nContext = createContext<I18nContextType | undefined>(undefined);

// Storage key for persisting locale preference
const LOCALE_STORAGE_KEY = 'mysta-locale';

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>('en');
  const [mounted, setMounted] = useState(false);

  // Initialize locale from localStorage or browser language
  useEffect(() => {
    setMounted(true);

    // Try to get locale from localStorage first
    const savedLocale = localStorage.getItem(LOCALE_STORAGE_KEY) as Locale;
    if (savedLocale && (savedLocale === 'en' || savedLocale === 'zh')) {
      setLocaleState(savedLocale);
      return;
    }

    // Fallback to browser language detection
    const browserLang = navigator.language.toLowerCase();
    if (browserLang.startsWith('zh')) {
      setLocaleState('zh');
    } else {
      setLocaleState('en');
    }
  }, []);

  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale);
    localStorage.setItem(LOCALE_STORAGE_KEY, newLocale);

    // Update document language
    if (typeof document !== 'undefined') {
      document.documentElement.lang = newLocale;
    }
  };

  // Use default translations during SSR or before mount
  const t = mounted ? translations[locale] : translations['en'];

  return <I18nContext.Provider value={{ locale, setLocale, t }}>{children}</I18nContext.Provider>;
}

export function useI18n() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}

// Utility function to get nested translation with dot notation
export function useTranslation() {
  const { t } = useI18n();

  const translate = (key: string): string => {
    const keys = key.split('.');
    let value: unknown = t;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = (value as Record<string, unknown>)[k];
      } else {
        return key; // Return key if translation not found
      }
    }

    return typeof value === 'string' ? value : key;
  };

  return { t: translate };
}
