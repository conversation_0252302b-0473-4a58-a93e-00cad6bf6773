'use client';

import Image from 'next/image';
import PageIllustration from '@/components/page-illustration';
import { useI18n } from '@/lib/i18n';

export default function HeroHome() {
  const { t } = useI18n();
  const chromeStoreUrl = process.env.NEXT_PUBLIC_CHROME_STORE_URL;

  return (
    <section className="relative">
      <PageIllustration />
      <div className="mx-auto max-w-7xl px-4 sm:px-6">
        {/* Hero content */}
        <div className="pt-32 md:pt-40">
          {/* Section header */}
          <div className="pb-12 text-center md:pb-16">
            <h1
              className="mb-6 border-y text-3xl md:text-5xl font-bold [border-image:linear-gradient(to_right,transparent,--theme(--color-slate-300/.8),transparent)1]"
              data-aos="zoom-y-out"
              data-aos-delay={150}
            >
              {t.hero.title.split('\n').map((line, index) => (
                <span key={index}>
                  {line}
                  {index < t.hero.title.split('\n').length - 1 && (
                    <br className="block md:hidden" />
                  )}
                </span>
              ))}
            </h1>
            <div className="mx-auto max-w-3xl">
              <p className="mb-8 text-lg text-gray-700" data-aos="zoom-y-out" data-aos-delay={300}>
                {t.hero.subtitle}
              </p>
              <div className="relative before:absolute before:inset-0 before:border-y before:[border-image:linear-gradient(to_right,transparent,--theme(--color-slate-300/.8),transparent)1]">
                <div
                  className="mx-auto max-w-xs sm:flex sm:max-w-none sm:justify-center"
                  data-aos="zoom-y-out"
                  data-aos-delay={450}
                >
                  <a
                    className={`btn group mb-4 w-full bg-[#0F1115] text-white shadow-sm sm:mb-0 sm:w-auto ${
                      chromeStoreUrl
                        ? 'hover:bg-[#1A1D23] cursor-pointer'
                        : 'opacity-50 cursor-not-allowed'
                    }`}
                    href={chromeStoreUrl || '#'}
                    target="_blank"
                    onClick={e => !chromeStoreUrl && e.preventDefault()}
                    title={!chromeStoreUrl ? 'Chrome store URL not configured' : ''}
                  >
                    <span className="relative inline-flex items-center">
                      <Image
                        src="/images/logo-chrome.svg"
                        alt="Chrome logo"
                        width={24}
                        height={24}
                        className="mr-2"
                      />
                      {t.common.addToChrome}
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
          {/* Hero image/video */}
          <div
            className="mx-auto max-w-5xl px-4 sm:px-6"
            data-aos="zoom-y-out"
            data-aos-delay={600}
          >
            <div className="relative">
              <div className="relative mx-auto max-w-[900px]">
                <div
                  className="relative overflow-hidden rounded-lg shadow-xl"
                  style={{
                    paddingBottom: '56.25%',
                    height: 0,
                    backgroundColor: '#000',
                  }}
                >
                  <iframe
                    src="https://www.youtube.com/embed/b3Xgf9ZYQFs?autoplay=0&rel=0&modestbranding=1&controls=1&showinfo=0"
                    title="Hero video"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowFullScreen
                    className="absolute top-0 left-0 w-full h-full border-0 transition-opacity duration-300"
                    style={{
                      borderRadius: '0.5rem',
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
