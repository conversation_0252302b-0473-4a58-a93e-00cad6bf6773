'use client';

import { MousePointerClick, Globe, BookLock } from 'lucide-react';
import Image from 'next/image';
import { useI18n } from '@/lib/i18n';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="text-center">
      <div className="mb-4 flex justify-center">{icon}</div>
      <h3 className="mb-2 text-lg font-semibold text-gray-900">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}

function ShowcaseGrid() {
  const { t } = useI18n();

  // AI Solutions Showcase data
  const showcaseItems = [
    {
      img: '/images/grid-3.png',
      title: t.features.showcase1.title,
      desc: t.features.showcase1.description,
    },
    {
      img: '/images/grid-2.png',
      title: t.features.showcase2.title,
      desc: t.features.showcase2.description,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 grid-rows-1 gap-8">
      {showcaseItems.map(item => (
        <div
          key={item.title}
          className="relative flex flex-col items-center text-center bg-[#f4f4f4] rounded-2xl overflow-hidden py-10 px-6"
        >
          {/* Grid background */}
          <div className="absolute inset-0 pointer-events-none" aria-hidden="true">
            <svg
              width="100%"
              height="100%"
              className="w-full h-full"
              xmlns="http://www.w3.org/2000/svg"
            >
              <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e6e6e6" strokeWidth="1" />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>
          <div
            className="w-full flex justify-center items-center my-4 z-10"
            style={{ height: 200, marginBottom: 32 }}
          >
            <Image
              src={item.img}
              alt={item.title}
              width={400}
              height={200}
              className="rounded-lg object-contain"
            />
          </div>
          <h3 className="text-[#0C0A09] text-xl font-medium mb-2 z-10">{item.title}</h3>
          <p className="text-[#78716C] mb-0 z-10 whitespace-pre-line">{item.desc}</p>
        </div>
      ))}
    </div>
  );
}

export default function Features() {
  const { t } = useI18n();

  const features = [
    {
      icon: <MousePointerClick className="w-6 h-6 text-gray-600" />,
      title: t.features.feature1.title,
      description: t.features.feature1.description,
    },
    {
      icon: <BookLock className="w-6 h-6 text-gray-600" />,
      title: t.features.feature2.title,
      description: t.features.feature2.description,
    },
    {
      icon: <Globe className="w-6 h-6 text-gray-600" />,
      title: t.features.feature3.title,
      description: t.features.feature3.description,
    },
  ];

  return (
    <section className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6">
        <div className="text-center mb-12">
          <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">
            {t.features.sectionTitle.split('\n').map((line, index) => (
              <span key={index}>
                {line}
                {index < t.features.sectionTitle.split('\n').length - 1 && <br />}
              </span>
            ))}
          </h2>
          <p className="text-md text-gray-600">
            {t.features.sectionSubtitle.split('\n').map((line, index) => (
              <span key={index}>
                {line}
                {index < t.features.sectionSubtitle.split('\n').length - 1 && <br />}
              </span>
            ))}
          </p>
        </div>
        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard key={index} {...feature} />
          ))}
        </div>

        {/* AI Solutions Showcase */}
        <div className="mt-16">
          <div className="text-center mb-12">
            <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">
              {t.features.showcaseTitle}
            </h2>
            <p className="text-md text-gray-600">
              {t.features.showcaseSubtitle.split('\n').map((line, index) => (
                <span key={index}>
                  {line}
                  {index < t.features.showcaseSubtitle.split('\n').length - 1 && <br />}
                </span>
              ))}
            </p>
          </div>
          <ShowcaseGrid />
        </div>
      </div>
    </section>
  );
}
