'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Logo from './logo';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { useI18n } from '@/lib/i18n';

export default function Header() {
  const { t } = useI18n();
  const signInUrl = process.env.NEXT_PUBLIC_SIGNIN_URL;
  const [hasTopBanner, setHasTopBanner] = useState(false);

  useEffect(() => {
    const checkTopBanner = () => {
      setHasTopBanner(document.body.classList.contains('has-top-banner'));
    };

    checkTopBanner();

    const observer = new MutationObserver(checkTopBanner);
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['class'],
    });

    return () => observer.disconnect();
  }, []);

  const headerClass = hasTopBanner
    ? 'fixed top-[54px] z-30 w-full md:top-[58px]'
    : 'fixed top-2 z-30 w-full md:top-6';

  return (
    <header className={headerClass}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6">
        <div className="relative flex h-14 items-center justify-between gap-3 rounded-2xl bg-white/90 px-3 shadow-lg shadow-black/[0.03] backdrop-blur-xs before:pointer-events-none before:absolute before:inset-0 before:rounded-[inherit] before:border before:border-transparent before:[background:linear-gradient(var(--color-gray-100),var(--color-gray-200))_border-box] before:[mask-composite:exclude_!important] before:[mask:linear-gradient(white_0_0)_padding-box,_linear-gradient(white_0_0)]">
          {/* Site branding */}
          <div className="flex flex-1 items-center">
            <Logo />
          </div>

          {/* Desktop navigation */}
          <ul className="flex flex-1 items-center justify-end gap-3">
            <li>
              <LanguageSwitcher />
            </li>
            <li>
              <Link
                href={signInUrl || '#'}
                onClick={e => !signInUrl && e.preventDefault()}
                className={`btn-sm text-gray-800 shadow-sm ${
                  signInUrl
                    ? 'bg-white hover:bg-gray-50 cursor-pointer'
                    : 'bg-gray-100 opacity-50 cursor-not-allowed'
                }`}
                title={!signInUrl ? 'Sign in URL not configured' : ''}
              >
                {t.common.signIn}
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </header>
  );
}
