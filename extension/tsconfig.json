{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"~/*": ["./src/*"]}}, "include": [".plasmo/index.d.ts", "src", "../packages/shared/src/agent/agent.ts", "../packages/shared/src/types/agent.ts"], "exclude": ["src/tg/**/*"]}