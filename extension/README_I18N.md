# Internationalization (i18n) Implementation

This document describes the dynamic internationalization system implemented for the Plasmo Chrome extension using React Context API.

## Overview

The extension supports real-time language switching between English and Chinese (Simplified) without requiring page reloads. The implementation uses React Context for state management and TypeScript files for translations, ensuring type safety and optimal performance.

## Architecture

### Core Components

1. **I18n Context System** (`src/utils/i18n.tsx`)

   - React Context API for global state management
   - Real-time language switching
   - Automatic component re-rendering on language change
   - Storage persistence using Plasmo Storage API

2. **Translation Files** (`src/locales/`)

   - TypeScript-based translation files for type safety
   - `en.ts` - English translations
   - `zh_CN.ts` - Chinese (Simplified) translations

3. **Language Selector** (`src/sidepanel/components/LanguageSelector.tsx`)
   - Dropdown interface for language selection
   - Integrated with header dropdown menu
   - Visual feedback for current selection

## Supported Languages

- **English (`en`)** - Default fallback language
- **Chinese Simplified (`zh_CN`)** - Primary language

## Implementation Details

### 1. Context Provider Structure

```typescript
// Main i18n context that provides language state and functions
interface I18nContextType {
  currentLanguage: SupportedLanguage;
  changeLanguage: (language: SupportedLanguage) => Promise<void>;
  getMessage: (key: string, substitutions?: string | string[]) => string;
  getMessageWithFallback: (
    key: string,
    fallback: string,
    substitutions?: string | string[]
  ) => string;
}
```

### 2. Translation File Format

```typescript
// Example: src/locales/en.ts
export const enMessages = {
  viewProfile: 'View Profile',
  newChat: 'New chat',
  language: 'Language',
  // ... more translations
} as const;
```

### 3. Language Detection Logic

The system determines the initial language based on:

1. Previously saved user preference (stored in extension storage)
2. Browser language detection (`chrome.i18n.getUILanguage()`)
3. Falls back to English if no match is found

## Usage

### Basic Setup

1. **Wrap your app with I18nProvider:**

```typescript
import { I18nProvider } from '../utils/i18n';

const App = () => {
  return (
    <I18nProvider>
      <YourMainComponent />
    </I18nProvider>
  );
};
```

2. **Use the useLanguage hook in components:**

```typescript
import { useLanguage } from '~/utils/i18n';

const MyComponent = () => {
  const { currentLanguage, changeLanguage, getMessage } = useLanguage();

  return (
    <div>
      <h1>{getMessage('viewProfile')}</h1>
      <button onClick={() => changeLanguage('zh_CN')}>
        Switch to Chinese
      </button>
    </div>
  );
};
```

### Advanced Usage

#### Message Substitution

For dynamic content, use parameter substitution:

```typescript
// Translation file
export const messages = {
  enterApiKey: 'Enter $1 API Key', // $1 will be replaced
};

// Component usage
const placeholder = getMessage('enterApiKey', 'OpenAI');
// Result: "Enter OpenAI API Key"
```

#### Fallback Messages

```typescript
const message = getMessageWithFallback('someKey', 'Default Text');
```

#### Language Selection Component

```typescript
import LanguageSelector from './LanguageSelector';

// Integrate into your UI
<LanguageSelector onLanguageChange={(lang) => console.log('Changed to:', lang)} />
```

## Integration in Extension Components

### Header Component Integration

The language selector is integrated into the user dropdown menu in the header:

```typescript
// In Header.tsx
const Header = ({ user }) => {
  const { getMessage } = useLanguage();

  const dropdownMenu = (
    <div>
      <div>{user?.email || getMessage('user')}</div>
      <LanguageSelector />
      <button>{getMessage('viewProfile')}</button>
    </div>
  );

  return (
    <div>
      <Tooltip title={getMessage('conversations')}>
        <button onClick={showConversations}>
          <AlignLeft />
        </button>
      </Tooltip>
      {/* ... other header elements */}
    </div>
  );
};
```

### Input Components

```typescript
// In InputArea.tsx
const InputArea = () => {
  const { getMessage } = useLanguage();

  return (
    <textarea
      placeholder={getMessage('typeMessage')}
      aria-label={getMessage('send')}
    />
  );
};
```

## Current Translation Keys

The system includes translations for:

### UI Elements

- `viewProfile` - Profile button text
- `newChat` - New chat button
- `conversations` - Conversations tooltip
- `user` - User label

### Language Interface

- `language` - Language selector label
- `english` - English language option
- `chinese` - Chinese language option

### Form Elements

- `save` - Save button
- `cancel` - Cancel button
- `setApiKey` - API key modal title
- `enterApiKey` - API key input placeholder

### Messages & Feedback

- `thinking` - Loading state text
- `typeMessage` - Input placeholder
- `send` - Send button
- `apiKeyDisabled` - Error message
- `insufficientCredits` - Credit error
- `errorOccurred` - Generic error

### Prompt Templates

- `summarizeElonTweet` - Example prompt
- `postTweet` - Social media prompt
- `searchLinkedIn` - Search prompt

## Technical Features

### Real-time Updates

- Language changes immediately update all text across the extension
- No page reload required
- Persistent language preference across sessions

### Type Safety

- Full TypeScript support for translation keys
- Compile-time validation of translation keys
- IntelliSense support for available translations

### Performance Optimization

- Minimal re-renders using React Context
- Efficient message lookup with direct object access
- Lazy loading of translation data

### Browser Compatibility

- Works with Chrome extension manifest v3
- Compatible with Plasmo framework
- Uses standard Web APIs for storage and messaging

## Adding New Languages

1. **Create translation file:**

```typescript
// src/locales/fr.ts
export const frMessages = {
  viewProfile: 'Voir le profil',
  // ... all other translations
} as const;
```

2. **Update language registry:**

```typescript
// In src/utils/i18n.tsx
import { frMessages } from '../locales/fr';

const messageRegistry: Record<SupportedLanguage, Record<string, string>> = {
  en: enMessages,
  zh_CN: zhCNMessages,
  fr: frMessages, // Add new language
};

export const SUPPORTED_LANGUAGES = {
  en: 'English',
  zh_CN: '中文',
  fr: 'Français', // Add display name
} as const;
```

3. **Update type definitions:**

```typescript
export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;
```

## Best Practices

1. **Always use translation keys** instead of hardcoded strings
2. **Provide meaningful key names** that describe the content
3. **Use substitutions** for dynamic content instead of string concatenation
4. **Test language switching** across all components
5. **Keep translations consistent** in tone and style
6. **Validate all keys exist** in all language files

## Troubleshooting

### Common Issues

1. **"useLanguage must be used within an I18nProvider" Error**

   - Ensure your component is wrapped with `<I18nProvider>`
   - Check that the provider is at the correct level in your component tree

2. **Missing Translation Key Warnings**

   - Add the missing key to all translation files
   - Check for typos in key names

3. **Language Not Persisting**

   - Verify Plasmo storage permissions are configured
   - Check browser storage settings

4. **Components Not Re-rendering**
   - Ensure you're using the `useLanguage()` hook, not direct imports
   - Verify the component is within the I18nProvider scope

### Development Tips

- Use browser DevTools to check storage values: `chrome.storage.local.get('extension_language')`
- Enable console warnings to catch missing translation keys
- Test language switching with different browser language settings

## Migration Guide

If migrating from Chrome's built-in i18n system:

1. Remove `_locales` directories and `default_locale` from manifest
2. Convert JSON message files to TypeScript exports
3. Replace `chrome.i18n.getMessage()` calls with `useLanguage()` hook
4. Wrap your app with `I18nProvider`
5. Update all components to use the new hook-based approach

This implementation provides a robust, type-safe, and performant internationalization solution specifically designed for modern Chrome extensions built with React and TypeScript.
