import { useEffect, useRef } from 'react';

const useDebouncedEffect = (effect: () => void | (() => void), deps: any[], delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Clear the previous timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set a new timeout
    timeoutRef.current = setTimeout(() => {
      effect();
    }, delay);

    // Cleanup function to clear timeout on unmount or when deps change
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, deps);
};

export default useDebouncedEffect;
