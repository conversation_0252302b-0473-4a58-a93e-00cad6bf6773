import { useEffect, useRef } from 'react';

interface UseIntervalOptions {
  immediate?: boolean; // Whether to run the callback immediately on mount
  enabled?: boolean; // Whether the interval should be active
}

const useInterval = (
  callback: () => void,
  delay: number | null,
  options: UseIntervalOptions = {}
) => {
  const { immediate = false, enabled = true } = options;
  const savedCallback = useRef<() => void>();
  const intervalRef = useRef<NodeJS.Timeout>();

  // Remember the latest callback
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  // Set up the interval
  useEffect(() => {
    // Don't schedule if no delay is specified or if disabled
    if (delay === null || !enabled) {
      return;
    }

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Run immediately if requested
    if (immediate) {
      savedCallback.current?.();
    }

    // Set up the interval
    intervalRef.current = setInterval(() => {
      savedCallback.current?.();
    }, delay);

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [delay, enabled, immediate]);

  // Return a function to manually clear the interval
  const clearIntervalManually = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = undefined;
    }
  };

  return { clearInterval: clearIntervalManually };
};

export default useInterval;
