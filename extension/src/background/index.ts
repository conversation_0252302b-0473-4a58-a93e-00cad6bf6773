import {
  DeltaEvent,
  DomChangeEvent,
  TabChangeEvent,
  WebContext,
  WebContextInput,
  WebInteractionResult,
} from '~/types/tools';
import { WebToolkit } from './web-toolkit';
import { RuntimeAction, RuntimeActionResponse } from '../types/messages';

import { db } from '~/storages/indexdb';
import { TabToolKitArguments, WebToolKitArguments } from '../types/tools';
import { TabToolkit } from '~/background/tab-toolkit';
import { Developing } from '@the-agent/shared';

const webToolkit = new WebToolkit();
const contexts = new Map<number, WebContext>();

if (typeof chrome !== 'undefined' && chrome.sidePanel && chrome.sidePanel.setPanelBehavior) {
  chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true }).catch(console.error);
} else {
  console.error('sidePanel API is not available');
}

if (typeof chrome !== 'undefined' && chrome.action) {
  chrome.action.onClicked.addListener(async tab => {
    await openSidePanel(tab);
  });
} else {
  console.error('chrome.action API is not available');
}

if (typeof chrome !== 'undefined' && chrome.webNavigation) {
  // Track navigation events to avoid double-bumping for SPA navigations
  const recentCommitted = new Map<number, number>(); // tabId -> timestamp
  const NAVIGATION_WINDOW_MS = 100; // Short window to detect related events

  chrome.webNavigation.onCommitted.addListener(e => {
    if (e.frameId !== 0) return;
    bump(e.tabId);
    recentCommitted.set(e.tabId, Date.now());
    setTimeout(() => recentCommitted.delete(e.tabId), NAVIGATION_WINDOW_MS * 2);
  });

  chrome.webNavigation.onHistoryStateUpdated.addListener(e => {
    if (e.frameId !== 0) return;

    // Check if this is a follow-up to a recent onCommitted (indicating potential double-trigger)
    const recentCommit = recentCommitted.get(e.tabId);
    if (recentCommit && Date.now() - recentCommit < NAVIGATION_WINDOW_MS) {
      return;
    }

    bump(e.tabId);
  });

  // When a click/spawn on a page opens a *new* tab or window for a specific URL
  // and you want to know the relationship (who opened whom).
  chrome.webNavigation.onCreatedNavigationTarget.addListener(e => {
    const existing = contexts.get(e.tabId);
    if (!existing) {
      contexts.set(e.tabId, {
        tabId: e.tabId,
        url: e.url,
        title: '',
        pageId: 0,
        sourceTabId: e.sourceTabId,
        createdAt: Date.now(),
      });
    } else {
      existing.sourceTabId = e.sourceTabId;
    }
  });
} else {
  console.error('webNavigation API is not available');
}

if (typeof chrome !== 'undefined' && chrome.tabs) {
  chrome.tabs.onRemoved.addListener(tabId => {
    contexts.delete(tabId);
  });

  // the tab could be created by onCreatedNavigationTarget
  chrome.tabs.onCreated.addListener(tab => {
    if (tab.id) {
      const existing = contexts.get(tab.id);
      if (existing) {
        existing.url = tab.url ?? '';
        return;
      }
      contexts.set(tab.id, {
        tabId: tab.id,
        url: tab.url ?? '',
        title: tab.title ?? '',
        pageId: 0,
        createdAt: Date.now(),
      });
    }
  });
} else {
  console.error('tabs API is not available');
}

chrome.runtime.onMessage.addListener(
  (
    message: RuntimeAction,
    _sender,
    sendResponse: (response: WebInteractionResult<unknown> | RuntimeActionResponse) => void
  ) => {
    // Update frontend to support action
    const action = message;
    if (action.name === 'ping') {
      sendResponse({ success: true, message: 'ping' });
      return true;
    }

    if (action.name === 'inject-script') {
      (async () => {
        try {
          const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
          if (!tabs || tabs.length !== 1) {
            sendResponse({ success: false, error: 'no active tab found' });
            return;
          }
          const currentTab = tabs[0];
          const { code } = action.body as { code: string };

          // Use chrome.scripting.executeScript with world: 'MAIN' to bypass CSP restrictions
          await chrome.scripting.executeScript({
            target: { tabId: currentTab.id! },
            world: 'MAIN', // Execute in the main world to bypass CSP
            func: (codeToExecute: string) => {
              // Use eval in the main world context - this bypasses CSP restrictions
              // because the extension has scripting permissions
              eval(codeToExecute);
            },
            args: [code],
          });

          sendResponse({ success: true, message: 'Script injected successfully' });
        } catch (error) {
          console.error('Error injecting script:', error);
          sendResponse({ success: false, error: String(error) });
        }
      })();
      return true;
    }

    if (action.name === 'load-scripts') {
      (async () => {
        try {
          const installations = await db.getAllInstallations();
          const scripts = installations.map(i => i.code);
          sendResponse({ success: true, data: scripts });
        } catch (err) {
          console.error('Error loading scripts:', err);
          sendResponse({ success: false, error: String(err) });
        }
      })();
      return true;
    }

    if (action.name === 'execute-tool') {
      const { name, arguments: params } =
        (action.body as {
          name: string;
          arguments: TabToolKitArguments | WebToolKitArguments;
        }) || {};
      if (!name || !params) {
        sendResponse({ success: false, error: 'Invalid tool execution request' });
        return true;
      }

      (async () => {
        try {
          const activeBefore = await getCurrentActiveTabContext();
          const { context, ...rest } = params;
          validateContext(activeBefore, context, false);
          if (name.startsWith('TabToolkit_')) {
            const toolName = name.replace('TabToolkit_', '');
            const result = await TabToolkit.run(toolName, rest as TabToolKitArguments);
            await waitForPossibleNavChange(activeBefore);
            const activeAfter = await getCurrentActiveTabContext();
            sendResponse({
              ...result,
              context: activeAfter,
              delta: buildContextDeltaEvents(context, activeAfter),
              action: {
                tool: toolName,
                args: params,
              },
            });
          } else if (name.startsWith('WebToolkit_')) {
            const toolName = name.replace('WebToolkit_', '');
            await webToolkit.run('refreshDomTree', { generateDiff: false });
            const result = await webToolkit.run(toolName, rest as WebToolKitArguments);
            await waitForPossibleNavChange(activeBefore);
            const activeAfter = await getCurrentActiveTabContext();
            const events = buildContextDeltaEvents(context, activeAfter);
            if (events.length === 0 && context && activeAfter) {
              const domDiffResult = await webToolkit.run('refreshDomTree', { generateDiff: true });
              if (domDiffResult.success && domDiffResult.data) {
                events.push(domDiffResult.data as DomChangeEvent);
              }
            }
            sendResponse({
              ...result,
              context: activeAfter,
              delta: events,
              action: {
                tool: toolName,
                args: params,
              },
            });
          } else {
            sendResponse({ success: false, error: `Unsupported command: ${name}` });
          }
        } catch (error: unknown) {
          console.error('Error executing tool in background:', error);
          const message = error instanceof Error ? error.message : JSON.stringify(error);
          sendResponse({ success: false, error: message });
        }
      })();
      return true;
    }

    if (message.name === 'save-script') {
      (async () => {
        try {
          const { miniappId, code, version } = message.body as {
            miniappId: number;
            code: string;
            version: number;
          };

          const developing: Developing = {
            code,
            updated_at: Date.now(),
            version,
          };

          await db.updateMiniapp(miniappId, { developing });

          // Broadcast sync event to all content scripts
          broadcastScriptSyncEvent({
            type: 'script-updated',
            miniappId,
            data: {
              code,
              updated_at: developing.updated_at,
              version,
            },
          });

          sendResponse({ success: true });
        } catch (error: unknown) {
          console.error('Error saving script:', error);
          const message = error instanceof Error ? error.message : JSON.stringify(error);
          sendResponse({
            success: false,
            error: message,
          });
        }
      })();

      return true;
    }

    if (message.name === 'get-script') {
      (async () => {
        try {
          const { miniappId } = message.body as { miniappId: number };
          const miniapp = await db.getMiniapp(miniappId);

          if (miniapp?.developing) {
            sendResponse({
              success: true,
              data: {
                code: miniapp.developing.code,
                version: miniapp.developing.version,
                updated_at: miniapp.developing.updated_at,
              },
            });
          } else {
            sendResponse({ success: true, data: null });
          }
        } catch (error: unknown) {
          console.error('Error getting script:', error);
          const message = error instanceof Error ? error.message : JSON.stringify(error);
          sendResponse({
            success: false,
            error: message,
          });
        }
      })();

      return true;
    }

    if (action.name === 'trigger-script-sync') {
      const syncEvent = action.body as ScriptSyncEvent;
      broadcastScriptSyncEvent(syncEvent);
      sendResponse({ success: true });
      return true;
    }

    return true;
  }
);

async function openSidePanel(tab: chrome.tabs.Tab) {
  if (!tab?.id) {
    console.error('Invalid tab id');
    return;
  }

  try {
    await chrome.sidePanel.setOptions({
      tabId: tab.id,
      enabled: true,
      path: 'sidepanel.html',
    });
    // Do NOT call chrome.sidePanel.open here!
  } catch (error) {
    console.error('Failed to enable side panel:', error);
  }
}

async function getCurrentActiveTabContext(): Promise<WebContext | null> {
  const tab = await TabToolkit.getCurrentActiveTab();
  if (!tab.id) {
    return null;
  }

  const existing = contexts.get(tab.id);
  if (existing) {
    existing.url = tab.url ?? existing.url;
    existing.title = tab.title ?? existing.title;
    return existing;
  }

  // this should not happen, but let's add it if it's missing
  const result: WebContext = {
    tabId: tab.id!,
    url: tab.url ?? '',
    title: tab.title ?? '',
    pageId: 0,
    createdAt: Date.now(),
  };
  contexts.set(tab.id!, result);
  return result;
}

function validateContext(
  activeBefore: WebContext | null | undefined,
  context: WebContextInput | null | undefined,
  strict: boolean = true
) {
  if (!context) {
    return;
  }
  if (activeBefore?.tabId !== context.tabId) {
    throw new Error(`Tab mismatch, expecting ${context.tabId}, got ${activeBefore?.tabId}`);
  }
  if (strict && activeBefore?.pageId !== context.pageId) {
    throw new Error(`Page ID mismatch, expecting ${context.pageId}, got ${activeBefore?.pageId}`);
  }
}

export function buildContextDeltaEvents(
  activeBefore: WebContextInput | null | undefined,
  activeAfter: WebContext | null | undefined
): DeltaEvent[] {
  if (!activeBefore) {
    if (!activeAfter) {
      return [];
    } else {
      return [
        {
          type: 'tabChange',
          before: null,
          after: activeAfter.tabId,
        } as TabChangeEvent,
      ];
    }
  }

  // active before is valid
  const events: DeltaEvent[] = [];
  const reloaded = contexts.get(activeBefore.tabId);
  if (!reloaded) {
    events.push({
      type: 'tabClosed',
      tabId: activeBefore.tabId,
    });
  }
  if (!activeAfter) {
    // tab closed and no active tab now
    return events;
  }

  if (activeBefore.tabId !== activeAfter.tabId) {
    events.push({
      type: 'tabChange',
      before: activeBefore.tabId,
      after: activeAfter.tabId,
    });
  } else if (activeBefore.pageId !== activeAfter.pageId) {
    events.push({
      type: 'pageChange',
      before: activeBefore.pageId,
      after: activeAfter.pageId,
    });
  }
  // do not include dom change since it should be returned with web command result
  return events;
}

async function waitForPossibleNavChange(
  before: WebContext | null | undefined,
  { windowMs = 1500, everyMs = 100 } = {}
) {
  const start = Date.now();
  let current = await getCurrentActiveTabContext();
  if (!before && current) {
    return;
  }
  while (Date.now() - start < windowMs) {
    if (current?.tabId !== before?.tabId || current?.pageId !== before?.pageId) {
      return;
    }
    current = await getCurrentActiveTabContext();
    await new Promise(r => setTimeout(r, everyMs));
  }
}

async function bump(tabId: number) {
  const tab = await TabToolkit.getTab(tabId);
  if (!tab) {
    contexts.delete(tabId);
    return;
  }
  const context = contexts.get(tabId);
  const next = (context?.pageId ?? 0) + 1;
  contexts.set(tabId, {
    tabId,
    url: tab.url ?? '',
    title: tab.title ?? '',
    pageId: next,
    createdAt: Date.now(),
  });
}

// Script sync event interface
interface ScriptSyncEvent {
  type: 'script-updated';
  miniappId: number;
  data: {
    code: string;
    version: number;
    updated_at: number;
  };
}

// Broadcast script sync events to all content scripts
async function broadcastScriptSyncEvent(event: ScriptSyncEvent) {
  try {
    // Get all tabs with our content script
    const tabs = await chrome.tabs.query({
      url: ['https://*.mysta.ai/*', 'http://localhost/*'],
    });

    // Send message to each tab's content script
    for (const tab of tabs) {
      if (tab.id) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            type: 'script-sync-event',
            event,
          });
        } catch (error) {
          // Ignore errors for tabs that don't have our content script
          console.debug('Failed to send sync event to tab:', tab.id, error);
        }
      }
    }
  } catch (error) {
    console.error('Error broadcasting sync event:', error);
  }
}
