import { TemplateRegisterRequest } from '@the-agent/shared';
import { SummarizeResult } from '~/contents/dom';
import {
  DebuggerState,
  DomChangeEvent,
  ScreenshotResult,
  WebInteractionResult,
  WebToolKitArguments,
} from '~/types/tools';
import { parseKeySequence } from '~/utils/key';
import { sendKey<PERSON>ombo, sendSingle<PERSON>ey } from '~/utils/toolkit';
import { TabToolkit } from './tab-toolkit';

export class WebToolkit {
  private debuggerState: DebuggerState = {
    attached: false,
    tabId: null,
    targetId: null,
  };

  private hasDebuggerSupport = typeof chrome.debugger?.attach === 'function';

  async run(toolName: string, args: WebToolKitArguments): Promise<WebInteractionResult<unknown>> {
    const tab = await TabToolkit.getCurrentActiveTab();
    if (!tab?.id) {
      return { success: false, error: 'No active tab found' };
    }

    switch (toolName) {
      case 'extractText': {
        return await this.summarize(tab.id);
      }
      case 'screenshot': {
        return await this.screenshot();
      }
      case 'refreshPage': {
        const { timeout } = args as { timeout?: number };
        return await this.refreshPage(tab.id, timeout);
      }
      case 'analyzePageDOM': {
        const { selector, generateDiff } = args as {
          selector?: string;
          generateDiff?: boolean;
        };
        return await this.buildDomTree(tab.id, selector, generateDiff);
      }
      case 'refreshDomTree': {
        const { generateDiff } = args as { generateDiff?: boolean };
        return await this.refreshDomTree(generateDiff);
      }
      case 'buildDomTemplate': {
        return await this.buildDomTemplate(tab.id);
      }
      case 'click': {
        const { selectorOrIndex } = args as {
          selectorOrIndex: string;
        };
        return await this.click(tab.id, selectorOrIndex);
      }
      case 'input': {
        const { selectorOrIndex, value, clearFirst, pressEnterAfterInput } = args as {
          selectorOrIndex: string;
          value: string;
          clearFirst: boolean;
          pressEnterAfterInput: boolean;
        };
        return await this.input(tab.id, selectorOrIndex, value, clearFirst, pressEnterAfterInput);
      }
      case 'scroll': {
        const { selectorOrIndex } = args as {
          selectorOrIndex: string;
        };
        return await this.scroll(tab.id, selectorOrIndex);
      }
      case 'sendKeys': {
        const { keys } = args as { keys: string };
        return await this.sendKeys(tab.id, keys);
      }
      default: {
        throw new Error(`Unknown WebToolkit operation: ${toolName}`);
      }
    }
  }

  private async attachDebugger(tabId: number): Promise<void> {
    if (!this.hasDebuggerSupport) {
      return;
    }

    if (this.debuggerState.attached && this.debuggerState.tabId === tabId) {
      return;
    }

    try {
      await chrome.debugger.attach({ tabId }, '1.3');
      this.debuggerState.attached = true;
      this.debuggerState.tabId = tabId;
    } catch (error) {
      console.error('Failed to attach debugger:', error);
      this.hasDebuggerSupport = false; // Disable debugger support
    }
  }

  private async detachDebugger(): Promise<void> {
    if (!this.hasDebuggerSupport || !this.debuggerState.attached || !this.debuggerState.tabId) {
      return;
    }

    try {
      await chrome.debugger.detach({ tabId: this.debuggerState.tabId });
      this.debuggerState.attached = false;
      this.debuggerState.tabId = null;
    } catch (error) {
      console.error('Failed to detach debugger:', error);
    }
  }

  private async sendCommand(method: string, params: object = {}): Promise<object> {
    if (!this.hasDebuggerSupport || !this.debuggerState.attached || !this.debuggerState.tabId) {
      throw new Error('Debugger not available');
    }

    try {
      return await chrome.debugger.sendCommand({ tabId: this.debuggerState.tabId }, method, params);
    } catch (error) {
      console.error(`Failed to execute command ${method}:`, error);
      throw error;
    }
  }

  async refreshDomTree(
    generateDiff: boolean = false
  ): Promise<WebInteractionResult<DomChangeEvent | null>> {
    const tab = await TabToolkit.getCurrentActiveTab();
    if (!tab?.id) {
      throw new Error('No active tab found');
    }
    return await this.runInContentScript<DomChangeEvent | null>(tab.id, {
      name: 'refresh-dom-tree',
      generateDiff,
    });
  }

  async screenshot(): Promise<WebInteractionResult<ScreenshotResult>> {
    // Get current tab
    const tab = await TabToolkit.getCurrentActiveTab();

    if (!tab?.id) {
      throw new Error('No active tab found');
    }

    // Use chrome.tabs.captureVisibleTab for screenshot
    const dataUrl = await new Promise<string>((resolve, reject) => {
      chrome.tabs.captureVisibleTab(tab.windowId, { format: 'jpeg', quality: 80 }, dataUrl => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        resolve(dataUrl);
      });
    });

    return { success: true, data: { url: dataUrl } };
  }

  async summarize(tabId: number): Promise<WebInteractionResult<SummarizeResult>> {
    return await new Promise<WebInteractionResult<SummarizeResult>>((resolve, reject) => {
      chrome.tabs.sendMessage(
        tabId,
        {
          name: 'summarize-page',
        },
        response => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          resolve(response);
        }
      );
    });
  }

  async buildDomTemplate(tabId: number): Promise<WebInteractionResult<TemplateRegisterRequest>> {
    return await new Promise<WebInteractionResult<TemplateRegisterRequest>>((resolve, reject) => {
      chrome.tabs.sendMessage(
        tabId,
        {
          name: 'build-dom-template',
        },
        response => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          resolve(response);
        }
      );
    });
  }

  async buildDomTree(
    tabId: number,
    selector?: string,
    generateDiff: boolean = false
  ): Promise<WebInteractionResult<string>> {
    return await new Promise<WebInteractionResult<string>>((resolve, reject) => {
      chrome.tabs.sendMessage(
        tabId,
        {
          name: 'build-dom-tree',
          selector,
          generateDiff,
        },
        response => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          resolve(response);
        }
      );
    });
  }

  async refreshPage(
    tabId: number,
    timeout: number = 5000
  ): Promise<WebInteractionResult<undefined>> {
    await TabToolkit.executeInTab(() => {
      return new Promise<WebInteractionResult<undefined>>(resolve => {
        location.reload();
        resolve({ success: true, data: undefined });
      });
    }, []);

    await new Promise<void>((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        chrome.tabs.onUpdated.removeListener(listener);
        reject(new Error('Page load timeout'));
      }, timeout);
      const listener = (tid: number, info: chrome.tabs.TabChangeInfo) => {
        if (tid === tabId && info.status === 'complete') {
          clearTimeout(timeoutId);
          chrome.tabs.onUpdated.removeListener(listener);
          resolve();
        }
      };
      chrome.tabs.onUpdated.addListener(listener);
    });
    return {
      success: true,
      data: undefined,
    };
  }

  async runInContentScript<T>(
    tabId: number,
    args: { name: string } & Record<string, any>
  ): Promise<WebInteractionResult<T>> {
    return await new Promise<WebInteractionResult<T>>((resolve, reject) => {
      chrome.tabs.sendMessage(tabId, args, response => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        resolve(response);
      });
    });
  }

  async getDomVersion(tabId: number): Promise<WebInteractionResult<string>> {
    return await this.runInContentScript<string>(tabId, {
      name: 'get-dom-version',
    });
  }

  async click(tabId: number, selectorOrIndex: string): Promise<WebInteractionResult<undefined>> {
    // First, get element info from content script
    const elementInfo = await this.runInContentScript<{
      x: number;
      y: number;
      visible: boolean;
      selector: string;
      value: string;
    }>(tabId, {
      name: 'get-element',
      selectorOrIndex,
    });

    if (!elementInfo.success || !elementInfo.data) {
      return {
        success: false,
        error: elementInfo.error || 'Failed to get element information',
      };
    }

    if (!elementInfo.data.visible) {
      return {
        success: false,
        error: 'Element is not visible',
      };
    }

    // Attach debugger
    await this.attachDebugger(tabId);

    // Simulate mouse move to element
    await this.sendCommand('Input.dispatchMouseEvent', {
      type: 'mouseMoved',
      x: elementInfo.data.x,
      y: elementInfo.data.y,
      button: 'none',
      buttons: 0,
    });

    // Simulate click
    await this.sendCommand('Input.dispatchMouseEvent', {
      type: 'mousePressed',
      x: elementInfo.data.x,
      y: elementInfo.data.y,
      button: 'left',
      buttons: 1,
      clickCount: 1,
    });

    await this.sendCommand('Input.dispatchMouseEvent', {
      type: 'mouseReleased',
      x: elementInfo.data.x,
      y: elementInfo.data.y,
      button: 'left',
      buttons: 0,
      clickCount: 1,
    });

    // Detach debugger
    await this.detachDebugger();

    return {
      success: true,
      data: undefined,
    };
  }

  async scroll(tabId: number, selectorOrIndex?: string): Promise<WebInteractionResult<string>> {
    if (selectorOrIndex) {
      return await this.runInContentScript<string>(tabId, {
        name: 'scroll',
        selectorOrIndex,
      });
    } else {
      // Scroll to bottom of page using End key
      await this.sendKeys(tabId, 'End');
      return {
        success: true,
        data: 'Scrolled to bottom of page',
      };
    }
  }

  async input(
    tabId: number,
    selectorOrIndex: string,
    value: string,
    clearFirst: boolean = false,
    pressEnterAfterInput: boolean = false
  ): Promise<WebInteractionResult<string>> {
    // First, get element info from content script
    const elementInfo = await this.runInContentScript<{
      x: number;
      y: number;
      visible: boolean;
      selector: string;
      value: string;
    }>(tabId, {
      name: 'get-element',
      selectorOrIndex,
    });

    if (!elementInfo.success || !elementInfo.data) {
      return {
        success: false,
        error: elementInfo.error || 'Failed to get element information',
      };
    }

    if (!elementInfo.data.visible) {
      return {
        success: false,
        error: 'Element is not visible',
      };
    }

    // Attach debugger
    await this.attachDebugger(tabId);

    // Simulate mouse move to element
    await this.sendCommand('Input.dispatchMouseEvent', {
      type: 'mouseMoved',
      x: elementInfo.data.x,
      y: elementInfo.data.y,
      button: 'none',
      buttons: 0,
    });

    // Simulate click to focus
    await this.sendCommand('Input.dispatchMouseEvent', {
      type: 'mousePressed',
      x: elementInfo.data.x,
      y: elementInfo.data.y,
      button: 'left',
      buttons: 1,
      clickCount: 1,
    });

    await this.sendCommand('Input.dispatchMouseEvent', {
      type: 'mouseReleased',
      x: elementInfo.data.x,
      y: elementInfo.data.y,
      button: 'left',
      buttons: 0,
      clickCount: 1,
    });

    // Clear if needed
    if (clearFirst && elementInfo.data.value) {
      for (let i = 0; i < elementInfo.data.value.length; i++) {
        await this.sendCommand('Input.dispatchKeyEvent', {
          type: 'keyDown',
          key: 'Backspace',
          code: 'Backspace',
        });
        await this.sendCommand('Input.dispatchKeyEvent', {
          type: 'keyUp',
          key: 'Backspace',
          code: 'Backspace',
        });
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }

    // Input text using browser's native input API
    await this.sendCommand('Input.insertText', {
      text: value,
    });

    // Wait for completion
    await new Promise(resolve => setTimeout(resolve, 100));

    // Press Enter if requested
    if (pressEnterAfterInput) {
      await this.sendCommand('Input.dispatchKeyEvent', {
        type: 'keyDown',
        key: 'Enter',
        code: 'Enter',
        windowsVirtualKeyCode: 13,
        nativeVirtualKeyCode: 13,
        text: '\r',
      });
      await this.sendCommand('Input.dispatchKeyEvent', {
        type: 'keyUp',
        key: 'Enter',
        code: 'Enter',
        windowsVirtualKeyCode: 13,
        nativeVirtualKeyCode: 13,
      });
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Detach debugger
    await this.detachDebugger();

    return {
      success: true,
      data: `Success: Input "${value}" into element using debugger API`,
    };
  }

  async sendKeys(tabId: number, keys: string): Promise<WebInteractionResult<undefined>> {
    // Attach debugger
    await this.attachDebugger(tabId);

    // Parse the keys string and send appropriate key events
    const keySequences = parseKeySequence(keys);

    for (const keySequence of keySequences) {
      if (keySequence.isCombo) {
        // Handle key combinations like Control+c, Control+Shift+T
        await sendKeyCombo(keySequence.keys, this.sendCommand.bind(this));
      } else {
        // Handle single keys
        await sendSingleKey(keySequence.keys[0], this.sendCommand.bind(this));
      }
    }

    // Detach debugger
    await this.detachDebugger();

    return {
      success: true,
      data: undefined,
    };
  }
}
