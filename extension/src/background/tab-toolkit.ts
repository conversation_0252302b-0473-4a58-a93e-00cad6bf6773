import { TabToolKitArguments, WebInteractionResult } from '~/types/tools';

type ListTabsResult = {
  tabId?: number;
  url?: string;
  title?: string;
}[];

const TAB_LOAD_TIMEOUT = 10000;

export class TabToolkit {
  static async run(
    toolName: string,
    args: TabToolKitArguments
  ): Promise<WebInteractionResult<unknown>> {
    switch (toolName) {
      case 'openTab':
        return await TabToolkit.openTab((args as { url: string }).url);
      case 'listTabs':
        return await TabToolkit.listTabs();
      case 'closeTab':
        return await TabToolkit.closeTab((args as { tabId: number }).tabId);
      case 'switchToTab':
        const tabId = (args as { tabId: number }).tabId;
        return await TabToolkit.switchToTab(tabId);
      case 'waitForTabLoad':
        return await TabToolkit.waitForTabLoad((args as { tabId: number }).tabId);
      default:
        return {
          success: false,
          error: `Tool ${toolName} not implemented in background script`,
        };
    }
  }

  static async getCurrentActiveTab(): Promise<chrome.tabs.Tab> {
    const [tab] = await chrome.tabs.query({
      active: true,
      currentWindow: true,
    });
    if (tab?.id) {
      return tab;
    }
    throw new Error(
      'No active tab found. Please ensure the extension has the necessary permissions.'
    );
  }

  static async getTab(tabId: number): Promise<chrome.tabs.Tab> {
    const tab = await chrome.tabs.get(tabId);
    if (!tab || !tab.id) {
      throw new Error(`Tab with ID ${tabId} not found`);
    }
    // Check if tab is in a valid state (not discarded/crashed)
    if (tab.status === 'unloaded' || tab.discarded) {
      throw new Error(`Tab with ID ${tabId} is not loaded or has been discarded`);
    }
    return tab;
  }

  static async executeInTab<T>(
    userFunc: (...args: string[]) => T,
    args: string[] = [],
    tabId?: number
  ): Promise<T> {
    let tab: chrome.tabs.Tab | null = null;
    if (tabId) {
      tab = await chrome.tabs.get(tabId);
    } else {
      tab = await this.getCurrentActiveTab();
    }
    if (!tab?.id) {
      throw new Error('Tab ID not found. Please ensure the tab is properly loaded.');
    }

    // Execute script
    const [result] = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: userFunc,
      args,
    });

    if (!result) {
      console.error('Script execution returned no result');
      throw new Error('No result returned from script execution');
    }

    // If result is a Promise, wait for it to complete
    if (result.result instanceof Promise) {
      const promiseResult = await result.result;
      // Check if Promise result contains error
      if (
        promiseResult &&
        typeof promiseResult === 'object' &&
        'success' in promiseResult &&
        !promiseResult.success
      ) {
        throw new Error(promiseResult.error || 'Unknown error in Promise result');
      }
      return promiseResult;
    }

    // Check if result contains error
    if (
      result.result &&
      typeof result.result === 'object' &&
      'success' in result.result &&
      !result.result.success
    ) {
      if ('error' in result.result) {
        throw new Error(result.result.error as string);
      }
      throw new Error('Unknown error in result');
    }

    return result.result as T;
  }
  /**
   * Open a new tab with a specific URL
   */
  static async openTab(url: string): Promise<WebInteractionResult<undefined>> {
    const existingTabs = await chrome.tabs.query({});
    const matchedTab = existingTabs.find(tab => tab.url === url);

    if (matchedTab && matchedTab.id !== undefined) {
      // 只调用 switchToTab，让它负责等待
      const switchResult = await TabToolkit.switchToTab(matchedTab.id);
      if (switchResult.success) {
        return {
          success: true,
          data: undefined,
        };
      } else {
        return {
          success: false,
          error: switchResult.error || 'Failed to switch to existing tab',
        };
      }
    }

    // 新建 tab 并等待加载完成
    return new Promise(resolve => {
      chrome.tabs.create({ url }, async newTab => {
        if (chrome.runtime.lastError) {
          resolve({
            success: false,
            error: chrome.runtime.lastError.message ?? 'Unknown error',
          });
          return;
        }

        // 等待新 tab 加载完成
        const loadResult = await TabToolkit.waitForTabLoad(newTab.id!, TAB_LOAD_TIMEOUT);
        if (loadResult.success) {
          resolve({
            success: true,
            data: undefined,
          });
        } else {
          resolve({
            success: false,
            error: loadResult.error || 'Failed to load new tab',
          });
        }
      });
    });
  }

  /**
   * Close a specific tab
   */
  static closeTab(tabId: number): Promise<WebInteractionResult<undefined>> {
    return new Promise(resolve => {
      chrome.tabs.remove(tabId, () => {
        if (chrome.runtime.lastError) {
          resolve({
            success: false,
            error: chrome.runtime.lastError.message ?? 'Unknown error',
          });
        } else {
          resolve({
            success: true,
            data: undefined,
          });
        }
      });
    });
  }

  /**
   * Find a tab by URL or title
   */
  static async listTabs(): Promise<WebInteractionResult<ListTabsResult>> {
    return new Promise((resolve, reject) => {
      chrome.tabs.query({ currentWindow: true }, tabs => {
        if (chrome.runtime.lastError) {
          reject({
            success: false,
            error: chrome.runtime.lastError.message,
          });
        } else {
          resolve({
            success: true,
            data: tabs.map(tab => ({
              tabId: tab.id,
              url: tab.url,
              title: tab.title,
            })),
          });
        }
      });
    });
  }

  /**
   * Switch to a specific tab
   */
  static async switchToTab(tabId: number): Promise<WebInteractionResult<undefined>> {
    return new Promise(resolve => {
      chrome.tabs.update(tabId, { active: true }, async tab => {
        if (chrome.runtime.lastError) {
          resolve({
            success: false,
            error: chrome.runtime.lastError.message ?? 'Unknown error',
          });
        } else if (tab) {
          const loadResult = await TabToolkit.waitForTabLoad(tabId, TAB_LOAD_TIMEOUT);
          if (loadResult.success) {
            resolve({
              success: true,
              data: undefined,
            });
          } else {
            resolve({
              success: false,
              error: loadResult.error || 'Failed to load tab',
            });
          }
        } else {
          console.error('Failed to switch to tab');
          resolve({
            success: false,
            error: 'Failed to switch to tab',
          });
        }
      });
    });
  }

  /**
   * Wait for a tab to load
   */
  static waitForTabLoad(
    tabId: number,
    timeout: number = TAB_LOAD_TIMEOUT
  ): Promise<WebInteractionResult<undefined>> {
    return new Promise(resolve => {
      const start = Date.now();

      const check = () => {
        chrome.tabs.get(tabId, tab => {
          if (chrome.runtime.lastError) {
            return resolve({
              success: false,
              error: chrome.runtime.lastError.message ?? 'Unknown error',
            });
          }

          if (!tab) {
            return resolve({
              success: false,
              error: 'Tab not found',
            });
          }

          if (tab.status === 'complete' || tab.status === 'interactive') {
            return resolve({
              success: true,
              data: undefined,
            });
          }

          if (Date.now() - start > timeout) {
            return resolve({
              success: false,
              error: 'Tab load timeout',
            });
          }

          setTimeout(check, 200); // 稍微拉长间隔，减少资源占用
        });
      };

      check();
    });
  }
}
