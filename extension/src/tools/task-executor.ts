import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AgentRuntimeConfig,
  <PERSON>l<PERSON><PERSON><PERSON>,
  TaskResult,
  TaskNode,
} from '@the-agent/shared';
import OpenAI from 'openai';
import { parseToolParams, toOpenAITools } from '~/utils/toolkit';
import { GlobalContext } from '~/types/task';
import { ToolDescription } from '~/types';

export abstract class TaskToolExecutor implements ToolExecutor {
  protected c: GlobalContext;

  constructor(c: GlobalContext) {
    this.c = c;
  }

  abstract getToolDescriptions(): ToolDescription[];

  abstract executeInternal(
    task: TaskNode,
    name: string,
    params: any,
    options?: ToolOptions
  ): Promise<ToolCallResult>;

  abstract getPostToolcallMessageInternal(toolCall: ToolCall): string;

  getTools(): OpenAI.ChatCompletionTool[] {
    return toOpenAITools(this.getToolDescriptions());
  }

  async execute(toolCall: ToolCall, options?: AgentRuntimeConfig): Promise<ToolCallResult> {
    const task = options?.taskOptions?.task;
    if (!task) {
      return {
        success: false,
        error: 'Task ID is required',
      };
    }
    const params = parseToolParams(toolCall);
    const toolName = toolCall.function.name;
    if (toolName === 'TaskToolkit_finishTask') {
      const p = params as Partial<TaskResult>;
      if (typeof p !== 'object') {
        return {
          success: false,
          error: 'Invalid task result format',
        };
      }
      task.finish({
        id: task.id,
        status: p.status ?? 'error',
        output: p.output ?? 'no output',
      });
      return {
        success: p.status === 'completed',
        data: p.output,
      };
    }
    try {
      return await this.executeInternal(task, toolName, params, options?.toolOptions);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  getPostToolcallMessage(toolCall: ToolCall): string {
    if (!toolCall.result) {
      throw new Error('Tool call result is required');
    }
    if (toolCall.function.name === 'TaskToolkit_finishTask') {
      return TASK_DONE_MESSAGE;
    } else {
      return this.getPostToolcallMessageInternal(toolCall);
    }
  }

  isFinalToolCall(toolCall: ToolCall) {
    return toolCall.function.name === 'TaskToolkit_finishTask';
  }
}

export const TASK_DONE_MESSAGE = `You have successfully executed the task and stored the result to database.
Simply reply "Task Completed" and exit. Thank you!`;
