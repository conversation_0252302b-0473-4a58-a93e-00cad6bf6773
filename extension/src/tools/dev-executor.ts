import { <PERSON><PERSON><PERSON>all<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ToolExecutor } from '@the-agent/shared';
import OpenAI from 'openai';
import { executeWebToolkit, parseToolParams, toOpenAITools } from '~/utils/toolkit';
import { WEB_TOOLKIT_ANALYZE_TOOLS } from './tool-descriptions';

export class DevToolExecutor implements ToolExecutor {
  async execute(toolCall: ToolCall): Promise<ToolCallResult> {
    if (!toolCall.function.name) {
      throw new Error('Tool name is required');
    }

    const toolName = toolCall.function.name;
    const params = parseToolParams(toolCall);
    if (toolName.startsWith('WebToolkit_')) {
      return await executeWebToolkit(toolName, params);
    }

    switch (toolName) {
      case 'DevToolkit_render':
        const p1 = params as { script: string };
        return {
          success: true,
          data: p1.script,
        };
      default:
        return {
          success: false,
          error: 'Invalid tool call',
        };
    }
  }

  getTools(): OpenAI.ChatCompletionTool[] {
    return toOpenAITools([
      {
        name: 'DevToolkit_render',
        description: "Render the script generated per user's request",
        parameters: {
          type: 'object',
          properties: {
            script: {
              type: 'string',
              description: "The script generated per user's request",
            },
          },
          required: ['script'],
        },
        returns: {
          type: 'null',
          description: 'No return value',
        },
      },
      ...WEB_TOOLKIT_ANALYZE_TOOLS,
    ]);
  }

  getPostToolcallMessage(): string {
    return '';
  }
}
