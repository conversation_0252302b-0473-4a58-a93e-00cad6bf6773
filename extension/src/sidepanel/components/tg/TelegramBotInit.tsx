import { useState } from 'react';
import { BindBot, BotInfo } from './BindBot';
import { BindGroupChat } from './BindGroupChat';
import { useTelegramBotStore } from '~/stores/telegramBotStore';
import { useTelegramStore } from '~/stores/telegramStore';

export const TelegramBotInit = () => {
  const [step, setStep] = useState<'bindBot' | 'bindGroupChat'>('bindBot');
  const [botInfo, setBotInfo] = useState<BotInfo | null>(null);
  const { setLocalBotParams } = useTelegramBotStore();
  const { telegramUser } = useTelegramStore();

  return (
    <div
      style={{
        paddingTop: '24px',
      }}
    >
      {step === 'bindBot' && (
        <BindBot
          onSuccess={(botInfo, isStartWithGroup) => {
            if (isStartWithGroup) {
              setBotInfo(botInfo);
              setStep('bindGroupChat');
            } else {
              setLocalBotParams({
                token: botInfo.token,
                targetUserId: telegramUser?.id.toString() || '',
              });
            }
          }}
        />
      )}

      {step === 'bindGroupChat' && botInfo && (
        <BindGroupChat
          botInfo={botInfo}
          onSuccess={selectedGroupChat => {
            setLocalBotParams({
              token: botInfo.token,
              groupChatId: selectedGroupChat.id.toString(),
            });
          }}
          onBack={() => {
            setStep('bindBot');
            setBotInfo(null);
          }}
        />
      )}
    </div>
  );
};
