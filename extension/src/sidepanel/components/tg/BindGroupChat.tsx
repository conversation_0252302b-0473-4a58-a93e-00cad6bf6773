import { Api, utils } from '@the-agent/telegram';
import { RefreshCwIcon, Search } from 'lucide-react';
import { useMemo, useState } from 'react';
import useDebouncedEffect from '~/hooks/useDebouncedEffect';
import { getTelegramClient } from '~/services/gramjs';
import LoadingModal from '../modals/LoadingModal';
import { BotInfo } from './BindBot';
import { isTelegramAuthError } from '~/utils/telegram';
import { useTelegramStore } from '~/stores/telegramStore';
import filterIcon from '~/assets/icons/filter.svg';
import { getMessage } from '~/utils/i18n';

type CustomDialog = {
  dialog: Api.Dialog;
  title?: string;
  name?: string;
  id: string;
};

export const BindGroupChat = (props: {
  botInfo: BotInfo;
  onSuccess: (selectedGroupChat: CustomDialog) => void;
  onBack: () => void;
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [groupChats, setGroupChats] = useState<CustomDialog[]>([]);
  const [selectedGroupChat, setSelectedGroupChat] = useState<CustomDialog | null>(null);

  const { logoutTelegram, signInTelegram } = useTelegramStore();

  const loadGroupChats = async () => {
    const chats: CustomDialog[] = [];
    try {
      const LIMIT = 50;
      let offsetId = null;
      while (1) {
        const telegramClient = getTelegramClient();
        const dialogs = await telegramClient.getDialogs({
          limit: LIMIT,
          offsetId: offsetId ? Number(offsetId) : undefined,
        });

        if (dialogs.length > 0) {
          offsetId = dialogs[dialogs.length - 1].id;
        }

        const filteredChats = dialogs.filter(dialog => dialog.isGroup && dialog.id);
        chats.push(
          ...filteredChats.map(dialog => ({
            ...dialog,
            id: dialog.id?.toString() || '',
          }))
        );

        const chatCount = dialogs?.length || 0;
        if (chatCount >= LIMIT) {
          continue;
        } else {
          break;
        }
      }
    } catch (error: any) {
      console.error('=========loadGroupChats err========', error);
      if (isTelegramAuthError(error)) {
        logoutTelegram();
        signInTelegram();
      }
    } finally {
      setLoading(false);
      setGroupChats(chats);
    }
  };

  useDebouncedEffect(
    () => {
      loadGroupChats();
    },
    [],
    1000
  );

  const filterGroupChats = useMemo(() => {
    return groupChats.filter(chat =>
      chat.title?.toLowerCase().includes(searchKeyword.toLowerCase())
    );
  }, [groupChats, searchKeyword.toLowerCase()]);

  const checkGroupChatMembers = async (chat: Api.Dialog) => {
    let botUser: Api.User | null = null;

    try {
      const botId = props.botInfo.token.split(':')[0];
      const LIMIT = 50;
      let page = 0;
      while (1) {
        const telegramClient = getTelegramClient();
        const members = await telegramClient.getParticipants(chat.peer, {
          limit: LIMIT,
          offset: page * LIMIT,
          search: props.botInfo.username,
        });

        const botMember = members?.find(member => member?.id?.toString() === botId);
        if (botMember) {
          botUser = botMember;
          break;
        }

        const memberLength = members?.length || 0;
        if (memberLength >= LIMIT) {
          page++;
          continue;
        } else {
          break;
        }
      }
    } catch (err: any) {
      setError(err.message);
      if (isTelegramAuthError(err)) {
        logoutTelegram();
        signInTelegram();
      }
    }

    return botUser;
  };

  const handleSubmit = async () => {
    try {
      if (!selectedGroupChat) {
        return;
      }

      setLoading(true);
      const botMember = await checkGroupChatMembers(selectedGroupChat.dialog);

      if (!botMember) {
        setError(getMessage('botIsNotAMemberOfTheGroup'));
        return;
      }

      // @ts-expect-error botMember is not typed
      if (!botMember.participant?.adminRights) {
        setError(getMessage('botIsNotAnAdminOfTheGroup'));
        return;
      }

      props.onSuccess(selectedGroupChat);
    } catch (err: any) {
      setError(err.message);
      if (isTelegramAuthError(err)) {
        logoutTelegram();
        signInTelegram();
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        padding: '60px 12px 12px 12px',
      }}
    >
      <div
        style={{
          textAlign: 'center',
          fontSize: 24,
          fontWeight: 600,
        }}
      >
        {getMessage('bindGroupChat')}

        <RefreshCwIcon
          style={{
            cursor: 'pointer',
            marginLeft: 4,
            opacity: loading ? 0.6 : 1,
          }}
          size={14}
          onClick={loadGroupChats}
        />
      </div>

      <div
        style={{
          marginTop: '12px',
          border: 'solid 1px #CDCDCD',
          borderRadius: '12px',
          padding: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 12,
        }}
      >
        <Search size={16} />

        <input
          style={{
            border: 'none',
            outline: 'none',
            flex: 1,
            fontSize: 14,
            fontWeight: 500,
            opacity: 0.8,
          }}
          placeholder={getMessage('search')}
          value={searchKeyword}
          onChange={e => setSearchKeyword(e.target.value)}
        />

        <img src={filterIcon} alt="filter" style={{ width: 16, height: 16 }} />
      </div>

      {error && <div style={{ color: 'red', textAlign: 'center', marginTop: '12px' }}>{error}</div>}

      <div
        style={{
          marginTop: '24px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'stretch',
          gap: 12,
          paddingBottom: '120px',
        }}
      >
        {filterGroupChats.map(chat => {
          return (
            <div
              key={utils.getPeerId(chat.dialog.peer)}
              style={{
                background: '#3333330A',
                borderRadius: '12px',
                display: 'flex',
                gap: 12,
                padding: '12px 8px',
                cursor: 'pointer',
                alignItems: 'center',
                border:
                  selectedGroupChat &&
                  utils.getPeerId(chat.dialog.peer) ===
                    utils.getPeerId(selectedGroupChat?.dialog.peer)
                    ? '1px solid #333'
                    : '1px solid #3333330A',
              }}
              onClick={() => {
                setSelectedGroupChat(chat);
              }}
            >
              <div
                style={{
                  fontSize: 14,
                  fontWeight: 500,
                  opacity: 0.8,
                }}
              >
                {chat.title}
              </div>
            </div>
          );
        })}
      </div>

      <div
        style={{
          position: 'fixed',
          backgroundColor: '#fff',
          bottom: 0,
          left: 0,
          right: 0,
          borderTop: '1px solid #CDCDCD90',
          padding: '12px',
        }}
      >
        <div
          style={{
            fontSize: 12,
            fontWeight: 500,
            opacity: 0.6,
          }}
        >
          {getMessage('addBotToGroup', [props.botInfo.username])}
        </div>

        <div
          style={{
            marginTop: '12px',
            padding: '12px',
            borderRadius: '8px',
            backgroundColor: '#333',
            cursor: 'pointer',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: selectedGroupChat ? 1 : 0.5,
          }}
          onClick={handleSubmit}
        >
          {getMessage('startBot')}
        </div>

        <div
          style={{
            marginTop: '12px',
            textAlign: 'center',
            cursor: 'pointer',
            color: '#333',
            opacity: 0.6,
            textDecoration: 'underline',
            fontSize: 12,
          }}
          onClick={props.onBack}
        >
          {getMessage('back')}
        </div>
      </div>

      <LoadingModal open={loading} onClose={() => setLoading(false)} />
    </div>
  );
};
