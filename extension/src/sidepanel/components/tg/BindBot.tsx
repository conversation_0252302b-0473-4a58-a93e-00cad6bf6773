import { Api } from '@the-agent/telegram';
import { useRef, useState } from 'react';
import useDebouncedEffect from '~/hooks/useDebouncedEffect';
import { getTelegramClient } from '~/services/gramjs';
import LoadingModal from '../modals/LoadingModal';
import { isTelegramAuthError } from '~/utils/telegram';
import { useTelegramStore } from '~/stores/telegramStore';
import { RefreshCwIcon } from 'lucide-react';
import { getMessage } from '~/utils/i18n';

export interface BotInfo {
  username: string;
  token: string;
}

export interface BotFatherBotInlineButton {
  data: string;
  text: string;
}

export const BindBot = (props: {
  onSuccess: (botInfo: BotInfo, isStartWithGroup: boolean) => void;
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [botFatherInlineButtons, setBotFatherInlineButtons] = useState<BotFatherBotInlineButton[]>(
    []
  );
  const [selectedBot, setSelectedBot] = useState<BotFatherBotInlineButton | null>(null);
  const botFatherPeerRef = useRef<any | null>(null);
  const { logoutTelegram, signInTelegram } = useTelegramStore();

  const loadDialogsWithBotFather = async () => {
    try {
      let retryCount = 0;
      let offsetId = null;
      const LIMIT = 20;
      while (1) {
        const telegramClient = getTelegramClient();
        const dialogs = await telegramClient.getDialogs({
          limit: LIMIT,
          offsetId: offsetId ? Number(offsetId) : undefined,
        });

        if (dialogs.length > 0) {
          offsetId = dialogs[dialogs.length - 1].id;
        }

        const botFatherChat = dialogs?.find(dialog => {
          const chat = dialog?.message?.chat?.toJSON();
          const user = chat as Api.User;
          return user.bot && user.username === 'BotFather';
        });

        if (botFatherChat) {
          botFatherPeerRef.current = botFatherChat.message?._chatPeer;
          break;
        }

        retryCount++;
        if (retryCount > 5) {
          break;
        }

        const chatCount = dialogs?.length || 0;
        if (chatCount >= LIMIT) {
          continue;
        } else {
          break;
        }
      }
    } catch (error: any) {
      if (isTelegramAuthError(error)) {
        logoutTelegram();
        signInTelegram();
      }
      setError(error?.message);
    }
  };

  const getBotFatherLatestMessage = async () => {
    const botFatherPeer = botFatherPeerRef.current;
    if (!botFatherPeer) {
      throw new Error('BotFather chat not found');
    }

    const telegramClient = getTelegramClient();
    const botFatherMessages = await telegramClient.getMessages(botFatherPeer, {
      limit: 10,
    });
    return botFatherMessages?.[0] || null;
  };

  const loadUserBots = async () => {
    try {
      const telegramClient = getTelegramClient();
      await telegramClient.sendMessage('@BotFather', {
        message: '/mybots',
      });
      await new Promise(resolve => setTimeout(resolve, 1000));

      await loadDialogsWithBotFather();

      if (!botFatherPeerRef.current) {
        throw new Error('BotFather chat not found');
      }

      let botFatherMessage: Api.Message | null = null;
      botFatherMessage = await getBotFatherLatestMessage();

      const inlineButtons: BotFatherBotInlineButton[] = [];
      botFatherMessage?.buttons?.forEach(buttonArr => {
        buttonArr.forEach(button => {
          const dataString = button.data?.toString();
          inlineButtons.push({
            text: button.text,
            data: dataString || '',
          });
        });
      });

      setError(null);
      setBotFatherInlineButtons(inlineButtons);
    } catch (err: any) {
      setError(err.message);
      if (isTelegramAuthError(err)) {
        logoutTelegram();
        signInTelegram();
      }
    } finally {
      setLoading(false);
    }
  };

  useDebouncedEffect(
    () => {
      loadUserBots();
    },
    [],
    1000
  );

  const getBotApiToken = async () => {
    const botFatherPeer = botFatherPeerRef.current;
    if (!botFatherPeer) {
      throw new Error(getMessage('botFatherChatNotFound'));
    }

    let botFatherMessage = await getBotFatherLatestMessage();

    if (!botFatherMessage?.message?.includes('Choose a bot from the list below:')) {
      throw new Error('Bot list not found');
    }

    let replyButton: any = null;
    botFatherMessage?.buttons?.forEach(buttonArr => {
      buttonArr.forEach(button => {
        if ((button as any).text === selectedBot?.text) {
          replyButton = button;
        }
      });
    });

    if (!replyButton) {
      throw new Error('BotFather reply button not found');
    }

    await replyButton.click({});
    await new Promise(resolve => setTimeout(resolve, 1000));

    let apiTokenButton: any = null;
    botFatherMessage = await getBotFatherLatestMessage();
    botFatherMessage?.buttons?.forEach(buttonArr => {
      buttonArr.forEach(button => {
        if ((button as any).text === 'API Token') {
          apiTokenButton = button;
        }
      });
    });

    if (!apiTokenButton) {
      throw new Error(getMessage('apiTokenButtonNotFound'));
    }

    await apiTokenButton.click({});
    await new Promise(resolve => setTimeout(resolve, 1000));

    botFatherMessage = await getBotFatherLatestMessage();
    // Example text: "Here is the token for bot Mulu Games @mulugames_bot:\n\n7441501735:AAGMn57YFS6Ns3o0OxNHKIcb8ksBInG_Obw"
    const apiToken = botFatherMessage?.message?.split('\n\n')[1];
    return apiToken;
  };

  const handleSubmit = async (isStartWithGroup: boolean) => {
    try {
      if (!selectedBot) {
        return;
      }

      setLoading(true);
      const apiToken = await getBotApiToken();
      if (apiToken) {
        props.onSuccess(
          {
            username: selectedBot.text,
            token: apiToken,
          },
          isStartWithGroup
        );
      }
    } catch (err: any) {
      const errMsg = err instanceof Error ? err.message : err;
      console.error('Error when getting bot api token: ', errMsg);
      setError(errMsg);
      if (isTelegramAuthError(err)) {
        logoutTelegram();
        signInTelegram();
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        padding: '60px 12px 12px 12px',
      }}
    >
      <div
        style={{
          textAlign: 'center',
          fontSize: 24,
          fontWeight: 600,
        }}
      >
        {getMessage('bindTelegramBot')}
        <RefreshCwIcon
          style={{
            cursor: 'pointer',
            marginLeft: 4,
            opacity: loading ? 0.6 : 1,
          }}
          size={14}
          onClick={loadUserBots}
        />
      </div>

      {error && <div style={{ color: 'red', textAlign: 'center', marginTop: '12px' }}>{error}</div>}

      <div
        style={{
          marginTop: '24px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'stretch',
          gap: 12,
          paddingBottom: '140px',
        }}
      >
        {botFatherInlineButtons.map(button => {
          return (
            <div
              key={button.text}
              style={{
                background: '#3333330A',
                borderRadius: '12px',
                display: 'flex',
                gap: 12,
                padding: '12px 8px',
                cursor: 'pointer',
                alignItems: 'center',
                border:
                  selectedBot?.data === button.data ? '1px solid #333' : '1px solid #3333330A',
              }}
              onClick={() => {
                setSelectedBot(button);
              }}
            >
              <div
                style={{
                  fontSize: 14,
                  fontWeight: 500,
                  opacity: 0.8,
                }}
              >
                {button.text}
              </div>
            </div>
          );
        })}
      </div>

      <div
        style={{
          position: 'fixed',
          backgroundColor: '#fff',
          bottom: 0,
          left: 0,
          right: 0,
          borderTop: '1px solid #CDCDCD90',
          padding: '12px',
        }}
      >
        <div
          style={{
            fontSize: 12,
            fontWeight: 500,
            opacity: 0.6,
          }}
        >
          {getMessage('getBotFromBotFather')}
        </div>

        <div
          style={{
            marginTop: '12px',
            padding: '12px',
            borderRadius: '8px',
            backgroundColor: '#333',
            cursor: 'pointer',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: selectedBot ? 1 : 0.5,
            textTransform: 'uppercase',
          }}
          onClick={() => handleSubmit(false)}
        >
          {getMessage('startBot')}
        </div>

        <div
          style={{
            marginTop: '12px',
            padding: '12px',
            borderRadius: '8px',
            backgroundColor: '#333',
            cursor: 'pointer',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: selectedBot ? 1 : 0.5,
            textTransform: 'uppercase',
          }}
          onClick={() => handleSubmit(true)}
        >
          {getMessage('startBotWithGroup')}
        </div>
      </div>

      <LoadingModal open={loading} onClose={() => setLoading(false)} />
    </div>
  );
};
