import { useState } from 'react';
import { providePassword } from '~/services/gramjs/authController';
import { useHint } from '~/stores/telegramStore';
import monkey from '../../../../assets/tgs/auth/monkey.png';
import { getMessage } from '~/utils/i18n';

export const AuthPassword = () => {
  const [password, setPassword] = useState('');
  const hint = useHint();

  const handleSubmit = async () => {
    if (password.trim()) {
      providePassword(password.trim());
      setPassword(''); // Clear the input after submitting
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && password.trim()) {
      handleSubmit();
    }
  };

  return (
    <div
      style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', paddingTop: '70px' }}
    >
      <div
        style={{
          fontSize: 24,
          fontWeight: 600,
          textAlign: 'center',
        }}
      >
        {getMessage('enterPassword')}
      </div>

      <div style={{ fontSize: 14, marginTop: '16px', textAlign: 'center', opacity: 0.8 }}>
        {getMessage('passwordTip1')}
      </div>

      <img src={monkey} style={{ width: '250px', height: 'auto', marginTop: '24px' }} />

      <input
        type="password"
        value={password}
        onChange={e => setPassword(e.target.value)}
        onKeyPress={handleKeyPress}
        placeholder={getMessage('enterPassword')}
        style={{
          marginTop: '24px',
          width: '250px',
          padding: '12px',
          borderRadius: '8px',
          border: '1px solid #ccc',
          outline: 'none',
        }}
      />

      {hint && (
        <div style={{ marginTop: '16px', fontSize: 12, opacity: 0.8 }}>
          {getMessage('hint')}: {hint}
        </div>
      )}

      {password && (
        <div
          style={{
            marginTop: '24px',
            width: '250px',
            padding: '12px',
            borderRadius: '8px',
            backgroundColor: '#333',
            cursor: 'pointer',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            textTransform: 'uppercase',
          }}
          onClick={handleSubmit}
        >
          {getMessage('next')}
        </div>
      )}
    </div>
  );
};
