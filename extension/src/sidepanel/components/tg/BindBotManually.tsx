import { useState } from 'react';
import LoadingModal from '../modals/LoadingModal';
import { sessions, TelegramClient } from '@the-agent/telegram';
import { getTelegramApiCredentials } from '~/services/gramjs';

export const BindBotManually = (props: {
  onSuccess: (info: { userId: string; apiToken: string }) => void;
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [apiToken, setApiToken] = useState<string>();
  const [userUsername, setUserUsername] = useState<string>();

  const handleSubmit = async () => {
    const formattedUserUsername = userUsername?.trim()?.replace('@', '');
    const formattedApiToken = apiToken?.trim();
    if (!formattedApiToken || !formattedUserUsername) {
      return;
    }

    try {
      setLoading(true);

      const SESSION = new sessions.StringSession('');
      const { apiId, apiHash } = getTelegramApiCredentials();
      const botClient = new TelegramClient(SESSION, apiId, apiHash, { connectionRetries: 5 });
      await botClient.start({
        botAuthToken: formattedApiToken,
      });

      const isAuthorized = await botClient.checkAuthorization();
      if (!isAuthorized) {
        throw new Error('Bot is not authorized');
      }

      // Get the entity (user) by username
      const entity = await botClient.getEntity(formattedUserUsername);

      let userId = null;
      if (entity && 'id' in entity) {
        userId = entity.id.toString();
      }

      if (!userId) {
        throw new Error('User not found');
      }

      await botClient.disconnect();
      await botClient.destroy();

      props.onSuccess({
        userId: userId,
        apiToken: formattedApiToken,
      });
    } catch (err: any) {
      console.error('=========err========', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        padding: '60px 12px 12px 12px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'stretch',
      }}
    >
      <div
        style={{
          textAlign: 'center',
          fontSize: 24,
          fontWeight: 600,
        }}
      >
        Bind Telegam Bot
      </div>

      {error && <div style={{ color: 'red', textAlign: 'center', marginTop: '12px' }}>{error}</div>}

      <div
        style={{
          alignSelf: 'center',
        }}
      >
        <div style={{ marginTop: '24px', fontSize: 14, fontWeight: 500 }}>Bot Token</div>

        <input
          type="password"
          value={apiToken}
          onChange={e => setApiToken(e.target.value)}
          placeholder="Enter your api token"
          style={{
            marginTop: '4px',
            width: '300px',
            padding: '12px',
            borderRadius: '8px',
            border: '1px solid #ccc',
            outline: 'none',
          }}
        />
        <div
          style={{
            marginTop: '8px',
            fontSize: 12,
            fontWeight: 500,
            opacity: 0.6,
          }}
        >
          Get from @BotFather on Telegram
        </div>
      </div>

      <div
        style={{
          alignSelf: 'center',
        }}
      >
        <div style={{ marginTop: '24px', fontSize: 14, fontWeight: 500 }}>Username</div>

        <input
          type="text"
          value={userUsername}
          onChange={e => setUserUsername(e.target.value)}
          placeholder="Enter your username"
          style={{
            marginTop: '4px',
            width: '300px',
            padding: '12px',
            borderRadius: '8px',
            border: '1px solid #ccc',
            outline: 'none',
          }}
        />
      </div>

      <div
        style={{
          position: 'fixed',
          backgroundColor: '#fff',
          bottom: 0,
          left: 0,
          right: 0,
          borderTop: '1px solid #CDCDCD90',
          padding: '12px',
        }}
      >
        <div
          style={{
            marginTop: '12px',
            padding: '12px',
            borderRadius: '8px',
            backgroundColor: '#333',
            cursor: 'pointer',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: apiToken && userUsername ? 1 : 0.5,
          }}
          onClick={handleSubmit}
        >
          NEXT
        </div>
      </div>

      <LoadingModal open={loading} onClose={() => setLoading(false)} />
    </div>
  );
};
