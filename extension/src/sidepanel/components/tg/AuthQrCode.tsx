import QRCode from 'react-qr-code';
import { useQrCodeData } from '~/stores/telegramStore';
import telegramIcon from '~/assets/icons/telegram.svg';
import { getMessage } from '~/utils/i18n';

interface AuthQrCodeProps {
  size?: number;
  className?: string;
  onBindBotManually: () => void;
}

const DATA_PREFIX = 'tg://login?token=';

export const AuthQrCode: React.FC<AuthQrCodeProps> = ({ onBindBotManually }) => {
  const qrCodeData = useQrCodeData();

  const qrCodeValue = `${DATA_PREFIX}${qrCodeData?.token}`;

  return (
    <div
      style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', paddingTop: '70px' }}
    >
      <div
        style={{
          fontSize: 24,
          fontWeight: 600,
          textAlign: 'center',
        }}
      >
        {getMessage('login')}
      </div>

      <div
        style={{
          borderRadius: '20px',
          backgroundColor: '#eee',
          padding: '16px',
          marginTop: '24px',
          position: 'relative',
        }}
      >
        <QRCode
          size={230}
          style={{ height: '230px', maxWidth: '230px', width: '230px' }}
          value={qrCodeValue}
          viewBox={`0 0 230 230`}
        />

        <img
          src={telegramIcon}
          alt="telegram"
          style={{
            position: 'absolute',
            backgroundColor: 'white',
            borderRadius: '50%',
            top: 0,
            left: 0,
            width: 50,
            height: 50,
            zIndex: 10,
            right: 0,
            margin: 'auto',
            bottom: 0,
          }}
        />
      </div>

      <div
        style={{
          margin: '24px 16px 0 16px',
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          alignItems: 'start',
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'start',
            gap: '12px',
          }}
        >
          <div
            style={{
              minWidth: '24px',
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              backgroundColor: '#007AFF',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              fontWeight: '600',
            }}
          >
            1
          </div>

          <span style={{ marginTop: '5px', fontSize: '12px', color: '#333333' }}>
            {getMessage('loginTip1')}
          </span>
        </div>

        <div
          style={{
            display: 'flex',
            alignItems: 'start',
            gap: '12px',
          }}
        >
          <div
            style={{
              minWidth: '24px',
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              backgroundColor: '#007AFF',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              fontWeight: '600',
            }}
          >
            2
          </div>
          <span style={{ marginTop: '5px', fontSize: '12px', color: '#333333' }}>
            {getMessage('loginTip2')}
          </span>
        </div>

        <div
          style={{
            display: 'flex',
            alignItems: 'start',
            gap: '12px',
          }}
        >
          <div
            style={{
              minWidth: '24px',
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              backgroundColor: '#007AFF',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              fontWeight: '600',
            }}
          >
            3
          </div>

          <span style={{ marginTop: '5px', fontSize: '12px', color: '#333333' }}>
            {getMessage('loginTip3')}
          </span>
        </div>
      </div>

      <div
        style={{
          margin: '24px 32px 0 32px',
          alignSelf: 'stretch',
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
        }}
      >
        <div style={{ flex: 1, height: 1, backgroundColor: '#33333340' }} />

        <span style={{ fontSize: '14px', color: '#33333340' }}>OR</span>

        <div style={{ flex: 1, height: 1, backgroundColor: '#33333340' }} />
      </div>

      <div
        style={{
          alignSelf: 'stretch',
          margin: '24px 32px 0 32px',
          height: 45,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: 12,
          border: '1px solid #CDCDCD',
          cursor: 'pointer',
        }}
        onClick={onBindBotManually}
      >
        <div
          style={{
            fontSize: 16,
            fontWeight: 500,
            color: '#333333',
          }}
        >
          {getMessage('bindBotManually')}
        </div>
      </div>
    </div>
  );
};
