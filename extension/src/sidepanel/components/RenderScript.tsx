import React, { useEffect, useState } from 'react';
import DevIcon from '~/assets/icons/dev.svg';
import { db } from '~/storages/indexdb';
import { updateMiniappDeveloping } from '~/services/miniapp';
import { Developing } from '@the-agent/shared';

interface RenderScriptProps {
  name?: string; // miniapp name
  version?: number; // miniapp version
  code?: string; // miniapp code
  miniapp_id?: number; // miniapp id
}

const RenderScript: React.FC<RenderScriptProps> = ({ name, version, code, miniapp_id }) => {
  // State to hold the latest script data from IndexedDB
  const [latestScript, setLatestScript] = useState<{
    name: string;
    version: number;
    code: string;
    miniapp_id: number;
  } | null>(null);

  // Determine display values - prioritize latest script from IndexedDB, then props
  const displayName = latestScript?.name || name || 'Untitled Script';
  const displayVersion = latestScript?.version || version || 1;
  const displayCode = latestScript?.code || code || '// No code available';
  const displayMiniappId = latestScript?.miniapp_id || miniapp_id || 0;

  // Load the latest script from IndexedDB on component mount
  useEffect(() => {
    const loadLatestScript = async () => {
      try {
        if (!miniapp_id) {
          console.warn('No miniapp_id provided, skipping script load');
          return;
        }

        // Get script from miniapp developing field
        const miniapp = await db.getMiniapp(miniapp_id);

        if (miniapp?.developing) {
          console.log('Found latest script in extension storage:', miniapp.developing);
          setLatestScript({
            name: name || 'Untitled Script',
            version: miniapp.developing.version,
            code: miniapp.developing.code,
            miniapp_id: miniapp_id,
          });
        } else {
          console.log('No script found in extension storage, using provided props');
          // Store the current script data if provided
          if (code && name) {
            const developing: Developing = {
              code: code,
              version: version || 1,
              updated_at: Date.now(),
            };
            await updateMiniappDeveloping(miniapp_id, developing);
          }
        }
      } catch (error) {
        console.error('Failed to load latest script:', error);
      }
    };

    loadLatestScript();
  }, [miniapp_id, code, name, version]);

  // Note: In extension context, we don't need to listen for sync events
  // since the extension is the source of truth for script data

  const handleViewCode = () => {
    const webUrl = process.env.PLASMO_PUBLIC_WEB_URL || 'https://mysta.ai';

    // Get user's locale from browser, default to 'en'
    const locale = navigator.language.split('-')[0] || 'en';

    // Pass script data as URL parameters and include code as fallback
    const params = new URLSearchParams({
      name: displayName,
      version: displayVersion.toString(),
      miniapp_id: displayMiniappId.toString(),
    });

    // For cross-origin compatibility, also pass code as base64 in URL as fallback
    try {
      const base64Code = btoa(displayCode);
      params.set('code64', base64Code);
      params.set('version', version?.toString() || '1');
    } catch (error) {
      console.warn('Failed to encode code as base64:', error);
    }

    const url = `${webUrl}/${locale}/miniapp/${displayMiniappId}/script/?${params.toString()}`;
    window.open(url, '_blank');
  };

  const handleTest = () => {
    chrome.runtime.sendMessage(
      {
        name: 'inject-script',
        body: { code: displayCode },
      },
      response => {
        if (response?.success) {
          console.log('Script injected successfully');
        } else {
          console.error('Failed to inject script:', response?.error);
        }
      }
    );
  };

  const handleInstall = () => {
    // TODO: Implement install functionality
    console.log('Install script');
  };

  return (
    <div
      style={{
        border: '1px solid #d1d5db',
        borderRadius: '12px',
        backgroundColor: '#ffffff',
        overflow: 'hidden',
        margin: '8px 0',
        width: '100%',
      }}
    >
      {/* Upper Section */}
      <div style={{ padding: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <img
            src={DevIcon}
            alt="Dev Icon"
            style={{
              width: '48px',
              height: '48px',
              borderRadius: '12px',
            }}
          />
          <div style={{ flex: 1, minWidth: 0 }}>
            <div
              style={{
                fontSize: '16px',
                fontWeight: 600,
                color: '#111827',
                marginBottom: '2px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {displayName}
            </div>
            <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '2px' }}>
              Version {displayVersion}
            </div>
            <button
              onClick={handleViewCode}
              style={{
                background: 'none',
                border: 'none',
                color: '#6b7280',
                fontSize: '12px',
                cursor: 'pointer',
                padding: 0,
                textDecoration: 'underline',
              }}
              onMouseOver={e => {
                e.currentTarget.style.color = '#374151';
              }}
              onMouseOut={e => {
                e.currentTarget.style.color = '#6b7280';
              }}
            >
              Click to view the code
            </button>
          </div>
        </div>
      </div>

      {/* Lower Section */}
      <div
        style={{
          borderTop: '1px solid #f3f4f6',
          padding: '12px 16px',
          display: 'flex',
          gap: '8px',
        }}
      >
        <button
          onClick={handleTest}
          style={{
            flex: 1,
            padding: '8px 12px',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: '#ffffff',
            color: '#374151',
            fontSize: '14px',
            fontWeight: 500,
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '6px',
            whiteSpace: 'nowrap',
            minWidth: 0,
          }}
          onMouseOver={e => {
            e.currentTarget.style.backgroundColor = '#f9fafb';
            e.currentTarget.style.borderColor = '#9ca3af';
          }}
          onMouseOut={e => {
            e.currentTarget.style.backgroundColor = '#ffffff';
            e.currentTarget.style.borderColor = '#d1d5db';
          }}
        >
          <svg
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12.0011 3C7.03911 3 3 7.03686 3 12.0011C3.00226 16.9631 7.03911 21 12.0011 21C16.9631 21 21 16.9631 21 12.0011C21 7.03911 16.9631 3 12.0011 3ZM15.9049 12.5201L10.2659 15.7762C9.86649 16.0064 9.36781 15.7176 9.36781 15.2572V8.74502C9.36781 8.28469 9.86649 7.99586 10.2659 8.22602L15.9049 11.4821C16.3042 11.7123 16.3042 12.29 15.9049 12.5201Z"
              fill="currentColor"
            />
          </svg>
          Test
        </button>

        <button
          onClick={handleInstall}
          style={{
            flex: 1,
            padding: '8px 12px',
            border: 'none',
            borderRadius: '8px',
            backgroundColor: '#374151',
            color: '#ffffff',
            fontSize: '14px',
            fontWeight: 500,
            cursor: 'pointer',
            transition: 'background-color 0.2s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '6px',
            whiteSpace: 'nowrap',
            minWidth: 0,
          }}
          onMouseOver={e => {
            e.currentTarget.style.backgroundColor = '#1f2937';
          }}
          onMouseOut={e => {
            e.currentTarget.style.backgroundColor = '#374151';
          }}
        >
          <svg
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12 15L7 10H10V3H14V10H17L12 15Z" fill="currentColor" />
            <path
              d="M20 15V18C20 19.1 19.1 20 18 20H6C4.9 20 4 19.1 4 18V15H6V18H18V15H20Z"
              fill="currentColor"
            />
          </svg>
          Install
        </button>
      </div>
    </div>
  );
};

export default RenderScript;
