import React from 'react';
import { useLanguage } from '../../utils/i18n';

const Thinking: React.FC = () => {
  const { getMessage } = useLanguage();
  return (
    <div className="flex items-center text-gray-500 py-1">
      <style>
        {`
          @keyframes dots {
            0%, 20% { opacity: 0; }
            50% { opacity: 1; }
            80%, 100% { opacity: 0; }
          }
          .loading-dot {
            animation: dots 1.5s infinite;
          }
        `}
      </style>

      <div className="text-sm font-medium text-gray-600">
        <span className="inline-flex ml-3">
          <span>{getMessage('thinking')}</span>
          <span className="loading-dot" style={{ animationDelay: '0.3s' }}>
            .
          </span>
          <span className="loading-dot" style={{ animationDelay: '0.6s' }}>
            .
          </span>
          <span className="loading-dot" style={{ animationDelay: '0.6s' }}>
            .
          </span>
        </span>
      </div>
    </div>
  );
};

export default Thinking;
