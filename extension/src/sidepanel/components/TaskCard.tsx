import React, { useState } from 'react';
import WaitIcon from '~/assets/icons/wait.svg';
import RunIcon from '~/assets/icons/run.svg';
import DoneIcon from '~/assets/icons/done.svg';
import ErrorIcon from '~/assets/icons/error.svg';
import ArrowIcon from '~/assets/icons/arrow.svg';
import idIcon from '~/assets/icons/id.svg';
import goalIcon from '~/assets/icons/goal.svg';
import outputIcon from '~/assets/icons/output.svg';
import inputIcon from '~/assets/icons/input.svg';
import { TaskWithStatus } from '~/types/task';
interface TaskCardProps {
  task: TaskWithStatus;
}

const TaskCard: React.FC<TaskCardProps> = ({ task }) => {
  const [isExpanded, setIsExpanded] = useState(true);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const getStatusIcon = (status?: string | null) => {
    switch (status) {
      case 'completed':
        return <img src={DoneIcon} alt="Completed" />;
      case 'error':
        return <img src={ErrorIcon} alt="Error" />;
      case 'pending':
        return <img src={RunIcon} alt="Pending" />;
      case 'waiting':
        return <img src={WaitIcon} alt="Waiting" />;
      default:
        return <img src={WaitIcon} alt="Waiting" />;
    }
  };

  const getStatusText = (status?: string | null) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'error':
        return 'Error occurred';
      case 'pending':
      case 'running':
        return 'Being executed...';
      case 'waiting':
        return 'Waiting';
      default:
        return 'Unknown status';
    }
  };

  const getGoalText = (content?: string | null) => {
    return content || 'No goal specified';
  };

  const getOutputText = (status?: string | null, output?: string | null) => {
    if (status === 'error' && output) {
      return output;
    }
    if (status === 'completed') {
      return output || 'Task completed successfully';
    }
    if (status === 'pending' || status === 'running') {
      return 'Task is being executed...';
    }
    return 'Task is waiting to be executed';
  };

  const getInputsFromText = () => {
    return 'Ds oftasks this task depends on.';
  };

  return (
    <div className="task-card">
      {/* Upper section */}
      <div className="task-card-header">
        {getStatusIcon(task.status)}
        <div className="task-card-content">
          <div className="task-card-title">{task.task.goal}</div>
          <div className="task-card-status">{getStatusText(task.status)}</div>
        </div>
        <button
          className="task-card-expand-btn"
          onClick={toggleExpanded}
          title={isExpanded ? 'Collapse details' : 'Expand details'}
          aria-label={isExpanded ? 'Collapse task details' : 'Expand task details'}
        >
          <img
            src={ArrowIcon}
            alt=""
            className={`task-card-arrow ${isExpanded ? '' : 'collapsed'}`}
          />
        </button>
      </div>

      {/* Separator line */}
      <div className={`task-card-separator ${isExpanded ? '' : 'hidden'}`} />

      {/* Lower section - scrollable */}
      <div className={`task-card-details ${isExpanded ? '' : 'collapsed'}`}>
        {/* ID */}
        <div className="task-card-field">
          <div className="task-card-field-header">
            <img src={idIcon} alt="ID" className="task-card-field-icon" />
            <span className="task-card-field-label">ID:</span>
          </div>
          <div className="task-card-field-content monospace">
            {task.task.id || '232132132132133321'}
          </div>
        </div>

        {/* Goal */}
        <div className="task-card-field">
          <div className="task-card-field-header">
            <img src={goalIcon} alt="Goal" className="task-card-field-icon" />
            <span className="task-card-field-label">Goal:</span>
          </div>
          <div className="task-card-field-content">{getGoalText(task.task.goal)}</div>
        </div>

        {/* Output */}
        <div className="task-card-field">
          <div className="task-card-field-header">
            <img src={outputIcon} alt="Output" className="task-card-field-icon" />
            <span className="task-card-field-label">Output:</span>
          </div>
          <div className="task-card-field-content">
            {getOutputText(task.status, task.task.output)}
          </div>
        </div>

        {/* InputsFrom */}
        <div className="task-card-field">
          <div className="task-card-field-header">
            <img src={inputIcon} alt="InputsFrom" className="task-card-field-icon" />
            <span className="task-card-field-label">InputsFrom:</span>
          </div>
          <div className="task-card-field-content">{getInputsFromText()}</div>
        </div>
      </div>
    </div>
  );
};

export default TaskCard;
