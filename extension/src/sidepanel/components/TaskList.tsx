import React, { useState } from 'react';
import TaskMessage from './TaskMessage';
import { Message as SMessage, TaskStatus } from '@the-agent/shared';
import WorkflowDetailModal from './WorkflowDetailModal';
import { TaskWithStatus } from '~/types/task';
import { db } from '~/storages/indexdb';

interface TaskListProps {
  tasks: TaskWithStatus[];
  runId?: string | null;
}

const TaskList: React.FC<TaskListProps> = ({ tasks, runId }) => {
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    taskMessage: TaskWithStatus;
    history: SMessage[];
  }>({
    isOpen: false,
    taskMessage: tasks[0],
    history: [],
  });

  const handleOpenModal = async (taskMessage: TaskWithStatus) => {
    let history: SMessage[] = [];

    // Get history messages from indexdb where run_id and task_id
    if (runId && taskMessage.task.id) {
      try {
        const messages = await db.getMessagesByRunIdAndTaskId(runId, taskMessage.task.id);
        history = messages.map(
          msg =>
            ({
              ...msg,
              // Convert VersionedMessage to SMessage by removing version field
              version: undefined,
            }) as SMessage
        );
      } catch (error) {
        console.error('Failed to fetch task history:', error);
        // Fallback to empty history on error
        history = [];
      }
    }

    setModalState({
      isOpen: true,
      taskMessage,
      history,
    });
  };

  const handleCloseModal = () => {
    setModalState({
      isOpen: false,
      taskMessage: tasks[0],
      history: [],
    });
  };

  if (tasks.length === 0) return null;

  return (
    <div
      style={{
        borderRadius: 8,
        marginBottom: 8,
        position: 'relative',
      }}
    >
      {/* Connection Lines */}
      {tasks.length > 1 && (
        <div
          style={{
            position: 'absolute',
            left: 5,
            top: 34, // half card(22) + dot(12)
            height: (tasks.length - 1) * 61, // card(52) + margin(8) + detail(1)
            width: 2,
            backgroundColor: '#e5e7eb',
            zIndex: 0,
          }}
        />
      )}

      {tasks.map(task => {
        return (
          <div key={task.task.id} style={{ position: 'relative', zIndex: 1 }}>
            <TaskMessage
              title={task.task.goal || 'Task'}
              status={task.status as TaskStatus}
              onClick={() => handleOpenModal(task)}
            />
          </div>
        );
      })}

      <WorkflowDetailModal
        isOpen={modalState.isOpen}
        onClose={handleCloseModal}
        taskMessage={modalState.taskMessage}
        history={modalState.history}
      />
    </div>
  );
};

export default TaskList;
