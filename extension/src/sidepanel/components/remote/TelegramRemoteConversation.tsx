'use client';

import { ArrowLeft } from 'lucide-react';
import { useEffect, useRef } from 'react';
import remoteWhiteIcon from '~/assets/icons/remote-white.svg';
import useDebouncedEffect from '~/hooks/useDebouncedEffect';
import { useAuthState, useError, useQrCodeData, useTelegramStore } from '~/stores/telegramStore';
import { AuthPassword } from '../tg/AuthPassword';
import { AuthQrCode } from '../tg/AuthQrCode';
import { TelegramBotInit } from '../tg/TelegramBotInit';
import { RemoteSingleConversation } from './RemoteSingleConversation';
import { useLocalBotParams, useTelegramBotStore } from '~/stores/telegramBotStore';
import { Loading } from '../Loading';
import { useState } from 'react';
import { BindBotManually } from '../tg/BindBotManually';

export default function TelegramRemoteConversation({ onBack }: { onBack: () => void }) {
  const { signInTelegram } = useTelegramStore();
  const { setLocalBotParams } = useTelegramBotStore();
  const localBotParams = useLocalBotParams();
  const authState = useAuthState();
  const qrCodeData = useQrCodeData();
  const error = useError();

  const authStateRef = useRef<string | null>(null);
  const [showBindBotManually, setShowBindBotManually] = useState(false);

  const botInited = localBotParams?.token;

  useEffect(() => {
    authStateRef.current = authState || null;
  }, [authState]);

  useDebouncedEffect(
    () => {
      signInTelegram();
    },
    [],
    1000
  );

  const renderContent = () => {
    if (showBindBotManually) {
      return (
        <BindBotManually
          onSuccess={info => {
            setLocalBotParams({
              token: info.apiToken,
              targetUserId: info.userId,
            });
          }}
        />
      );
    }

    if (authState === 'authorizationStateWaitQrCode' && qrCodeData) {
      return (
        <AuthQrCode
          size={200}
          className="my-qr-code"
          onBindBotManually={() => {
            setShowBindBotManually(true);
          }}
        />
      );
    }

    if (authState === 'authorizationStateWaitQrCode' && !qrCodeData) {
      return (
        <div>
          <Loading />
        </div>
      );
    }

    if (authState === 'authorizationStateWaitPassword') {
      return <AuthPassword />;
    }

    if (authState === 'authorizationStateReady') {
      return <TelegramBotInit />;
    }

    return <Loading />;
  };

  if (botInited) {
    return <RemoteSingleConversation onBack={onBack} />;
  }

  return (
    <div
      style={{
        height: '100vh',
        width: '100%',
        position: 'relative',
      }}
    >
      {!botInited && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            backgroundColor: 'white',
            zIndex: 10,
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              backgroundColor: '#ffffff',
              padding: '0 16px',
              height: '44px',
              borderBottom: '1px solid #f0f0f0',
            }}
          >
            <ArrowLeft
              style={{
                cursor: 'pointer',
              }}
              onClick={onBack}
            />

            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '2px',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '999px',
                border: '1.5px solid #333333',
                backgroundColor: '#333333',
              }}
            >
              <img src={remoteWhiteIcon} alt="remote" />

              <div style={{ fontSize: 12, fontWeight: 500, color: 'white' }}>Remote</div>
            </div>

            <ArrowLeft style={{ visibility: 'hidden' }} />
          </div>
        </div>
      )}

      <div style={{}}>
        {error && <div style={{ color: 'red', marginBottom: '16px' }}>Error: {error}</div>}

        {renderContent()}
      </div>
    </div>
  );
}
