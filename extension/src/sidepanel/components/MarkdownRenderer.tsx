import React, { useMemo, Component, ReactNode } from 'react';
import Markdown from 'react-markdown';
import 'property-information';

interface MarkdownRendererProps {
  content?: string;
}

// Error Boundary to catch React rendering errors
class MarkdownErrorBoundary extends Component<
  { children: ReactNode; fallback: ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: ReactNode; fallback: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error) {
    console.error('MarkdownRenderer error:', error);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}

// Clean potentially problematic content
const cleanContent = (content: string): string => {
  if (!content || typeof content !== 'string') return '';

  return content
    .replace(new RegExp(String.fromCharCode(0), 'g'), '') // Remove null characters
    .replace(/[\uFFF0-\uFFFF]/g, '') // Remove special Unicode characters
    .trim();
};

// Simple fallback renderer
const FallbackRenderer: React.FC<{ content: string }> = ({ content }) => (
  <div style={{ whiteSpace: 'pre-wrap', lineHeight: '1.5' }}>{content}</div>
);

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content = '' }) => {
  const sanitizedContent = useMemo(() => cleanContent(content), [content]);

  const fallback = useMemo(
    () => <FallbackRenderer content={sanitizedContent} />,
    [sanitizedContent]
  );

  if (!sanitizedContent) return null;

  return (
    <div style={{ width: '100%' }}>
      <MarkdownErrorBoundary fallback={fallback}>
        <Markdown>{sanitizedContent}</Markdown>
      </MarkdownErrorBoundary>
    </div>
  );
};

export default MarkdownRenderer;
