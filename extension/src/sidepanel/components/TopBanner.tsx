import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useLanguage } from '~/utils/i18n';
import { BANNER_STORAGE_KEY } from '~/configs/common';

export default function TopBanner() {
  const [isVisible, setIsVisible] = useState(false);
  const { getMessage } = useLanguage();

  useEffect(() => {
    chrome.storage.local.get([BANNER_STORAGE_KEY], result => {
      setIsVisible(!result[BANNER_STORAGE_KEY]);
    });
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    chrome.storage.local.set({ [BANNER_STORAGE_KEY]: true });
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      style={{
        position: 'absolute',
        top: '44px', // Below header
        left: 0,
        right: 0,
        backgroundColor: '#2563eb',
        color: 'white',
        zIndex: 20,
        fontSize: '14px',
        height: '40px',
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 16px',
          width: '100%',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flex: 1 }}>
          <a
            href="https://t.me/mysta_ai"
            target="_blank"
            rel="noopener noreferrer"
            style={{
              color: 'white',
              textDecoration: 'none',
              whiteSpace: 'nowrap',
              transition: 'opacity 0.2s ease',
            }}
            onMouseOver={e => (e.currentTarget.style.opacity = '0.8')}
            onMouseOut={e => (e.currentTarget.style.opacity = '1')}
          >
            {getMessage('joinTelegramGroup')}
          </a>
        </div>
        <button
          onClick={handleClose}
          style={{
            marginLeft: '16px',
            padding: '4px',
            borderRadius: '50%',
            border: 'none',
            backgroundColor: 'transparent',
            color: 'white',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'background-color 0.2s ease',
          }}
          onMouseOver={e => (e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)')}
          onMouseOut={e => (e.currentTarget.style.backgroundColor = 'transparent')}
          aria-label="Close banner"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
}
