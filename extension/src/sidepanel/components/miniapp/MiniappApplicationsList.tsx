import React, { useState, useMemo } from 'react';
import { Dropdown, Tooltip, Modal } from 'antd';
import type { MenuProps } from 'antd';
import { MiniApp } from '@the-agent/shared';
import { Archive, Trash2 } from 'lucide-react';
import archiveIcon from '~/assets/icons/archive.svg';
import activateIcon from '~/assets/icons/activate.svg';
import dotIcon from '~/assets/icons/dot.svg';
import newminiappIcon from '~/assets/icons/newminiapp.svg';
import { getAvatarColor, getInitials } from '~/utils/profile';
import { useLanguage } from '~/utils/i18n';

interface MiniappApplicationsListProps {
  miniapps: MiniApp[];
  onSelectMiniapp: (miniapp: MiniApp) => void;
  onNewProject: () => void;
  onArchiveMiniapp?: (miniapp: MiniApp) => void;
  onDeleteMiniapp?: (miniapp: MiniApp) => void;
  onActivateMiniapp?: (miniapp: MiniApp) => void;
  onShowArchived?: () => void;
  isArchivedView?: boolean;
}

type FilterType = 'All' | 'Deployed' | 'Developing';

const MiniappApplicationsList: React.FC<MiniappApplicationsListProps> = ({
  miniapps,
  onSelectMiniapp,
  onNewProject,
  onArchiveMiniapp,
  onDeleteMiniapp,
  onActivateMiniapp,
  onShowArchived,
  isArchivedView = false,
}) => {
  const { getMessage } = useLanguage();
  const [filter, setFilter] = useState<FilterType>('All');
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const [miniappToArchive, setMiniappToArchive] = useState<MiniApp | null>(null);

  // Get status with styling
  const getStatus = (miniapp: MiniApp) => {
    const isDeployed = !!miniapp.installation;
    const backgroundColor = isDeployed ? '#03BD70' : '#F5940C';

    return (
      <span
        style={{
          backgroundColor,
          color: '#ffffff',
          fontSize: '10px',
          fontWeight: 500,
          padding: '2px 4px',
          borderRadius: '4px',
          display: 'inline-block',
        }}
      >
        {isDeployed ? getMessage('miniappStatusDeployed') : getMessage('miniappStatusDeveloping')}
      </span>
    );
  };

  // Filter miniapps
  const filteredMiniapps = useMemo(() => {
    if (filter === 'All') return miniapps;
    if (filter === 'Deployed') return miniapps.filter(app => app.installation);
    if (filter === 'Developing') return miniapps.filter(app => !app.installation);
    return miniapps;
  }, [miniapps, filter]);

  // Filter dropdown items
  const filterItems = [
    { key: 'All', label: getMessage('filterAll') },
    { key: 'Deployed', label: getMessage('filterDeployed') },
    { key: 'Developing', label: getMessage('filterDeveloping') },
  ];

  const filterLabel = useMemo(() => {
    switch (filter) {
      case 'All':
        return getMessage('filterAll');
      case 'Deployed':
        return getMessage('filterDeployed');
      case 'Developing':
        return getMessage('filterDeveloping');
      default:
        return filter;
    }
  }, [filter, getMessage]);

  // Handle archive confirmation
  const handleArchiveClick = (miniapp: MiniApp) => {
    setMiniappToArchive(miniapp);
    setShowArchiveDialog(true);
  };

  const handleArchiveConfirm = () => {
    if (miniappToArchive && onArchiveMiniapp) {
      onArchiveMiniapp(miniappToArchive);
    }
    setShowArchiveDialog(false);
    setMiniappToArchive(null);
  };

  const handleArchiveCancel = () => {
    setShowArchiveDialog(false);
    setMiniappToArchive(null);
  };

  // Dot menu items
  const getDotMenuItems = (_miniapp: MiniApp): MenuProps['items'] => {
    if (isArchivedView) {
      return [
        {
          key: 'activate',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <img src={activateIcon} alt="Activate" style={{ width: 18, height: 18 }} />
              {getMessage('activate')}
            </div>
          ),
        },
        {
          key: 'delete',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Trash2 size={16} />
              {getMessage('delete')}
            </div>
          ),
        },
      ];
    } else {
      return [
        {
          key: 'archive',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Archive size={16} />
              {getMessage('archive')}
            </div>
          ),
        },
        {
          key: 'delete',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Trash2 size={16} />
              {getMessage('delete')}
            </div>
          ),
        },
      ];
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Pin top bar */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px 16px 0px 16px',
          backgroundColor: '#ffffff',
        }}
      >
        {/* Filter dropdown */}
        <Dropdown
          menu={{
            items: filterItems,
            onClick: ({ key }) => setFilter(key as FilterType),
          }}
          trigger={['click']}
        >
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 12px',
              borderRadius: '6px',
              border: '0px solid #d1d5db',
              backgroundColor: '#ffffff',
              color: '#374151',
              fontSize: '14px',
              cursor: 'pointer',
              transition: 'all 0.2s',
            }}
          >
            {filterLabel}
            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" strokeWidth="1.5" fill="none" />
            </svg>
          </button>
        </Dropdown>

        {/* Archived button - only show in non-archived view */}
        {!isArchivedView && (
          <Tooltip title={getMessage('archived')}>
            <button
              onClick={onShowArchived}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                border: 'none',
                backgroundColor: 'transparent',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f3f4f6';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <img src={archiveIcon} alt="Archive" style={{ width: 18, height: 18 }} />
            </button>
          </Tooltip>
        )}
      </div>

      {/* Miniapp list */}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '16px',
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          {filteredMiniapps.map(miniapp => (
            <div
              key={miniapp.id}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '12px',
                borderRadius: '8px',
                backgroundColor: '#F9F9F9',
                cursor: 'pointer',
                transition: 'all 0.2s',
                border: isArchivedView ? '1px dashed #9ca3af' : '1px solid transparent',
                background: '#F9F9F9',
              }}
              onClick={() => onSelectMiniapp(miniapp)}
              onMouseOver={e => {
                e.currentTarget.style.background =
                  'linear-gradient(#F9F9F9, #F9F9F9) padding-box, linear-gradient(90deg, #723DF6, #0081E4) border-box';
                e.currentTarget.style.border = '1px solid transparent';
              }}
              onMouseOut={e => {
                e.currentTarget.style.background = '#F9F9F9';
                e.currentTarget.style.border = isArchivedView
                  ? '1px dashed #9ca3af'
                  : '1px solid transparent';
              }}
            >
              {/* Avatar */}
              <div
                style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: getAvatarColor(miniapp.name),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#ffffff',
                  fontSize: '14px',
                  fontWeight: 600,
                  flexShrink: 0,
                }}
              >
                {getInitials(miniapp.name)}
              </div>

              {/* Name and status */}
              <div style={{ flex: 1, minWidth: 0 }}>
                <div
                  style={{
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#111827',
                    marginBottom: '2px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {miniapp.name}
                </div>
                <div>{getStatus(miniapp)}</div>
              </div>

              {/* Dot menu */}
              <div onClick={e => e.stopPropagation()}>
                <Dropdown
                  menu={{
                    items: getDotMenuItems(miniapp),
                    onClick: ({ key, domEvent }) => {
                      domEvent?.stopPropagation();
                      if (key === 'archive') {
                        handleArchiveClick(miniapp);
                      } else if (key === 'delete') {
                        onDeleteMiniapp?.(miniapp);
                      } else if (key === 'activate') {
                        onActivateMiniapp?.(miniapp);
                      }
                    },
                  }}
                  trigger={['click']}
                  placement="bottomRight"
                >
                  <button
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '24px',
                      height: '24px',
                      borderRadius: '4px',
                      border: 'none',
                      backgroundColor: 'transparent',
                      cursor: 'pointer',
                      transition: 'background 0.2s',
                    }}
                    onClick={e => e.stopPropagation()}
                    onMouseOver={e => {
                      e.currentTarget.style.backgroundColor = '#f3f4f6';
                    }}
                    onMouseOut={e => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    <img src={dotIcon} alt="More" style={{ width: 16, height: 16 }} />
                  </button>
                </Dropdown>
              </div>
            </div>
          ))}
        </div>

        {/* New Project button - only show in non-archived view */}
        {!isArchivedView && (
          <button
            onClick={onNewProject}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              width: '100%',
              padding: '12px 24px',
              marginTop: '16px',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              backgroundColor: '#ffffff',
              color: '#374151',
              fontSize: '14px',
              fontWeight: 500,
              cursor: 'pointer',
              transition: 'all 0.2s',
            }}
            onMouseOver={e => {
              e.currentTarget.style.backgroundColor = '#f9fafb';
              e.currentTarget.style.borderColor = '#9ca3af';
            }}
            onMouseOut={e => {
              e.currentTarget.style.backgroundColor = '#ffffff';
              e.currentTarget.style.borderColor = '#d1d5db';
            }}
          >
            <img src={newminiappIcon} alt="New Project" style={{ width: 20, height: 20 }} />
            {getMessage('newProject')}
          </button>
        )}
      </div>

      {/* Archive Confirmation Dialog */}
      <Modal
        title={
          <div style={{ textAlign: 'center', fontSize: '20px', fontWeight: 600, color: '#111827' }}>
            {getMessage('archivingTitle')}
          </div>
        }
        open={showArchiveDialog}
        onCancel={handleArchiveCancel}
        footer={null}
        centered
        width={300}
        closable={false}
      >
        <div style={{ textAlign: 'center' }}>
          <p
            style={{
              fontSize: '14px',
              color: '#374151',
              lineHeight: '1.6',
              fontWeight: 400,
            }}
          >
            {getMessage('archiveConfirm1')}
            <br />
            {getMessage('archiveConfirm2')}
            <br />
            {getMessage('archiveConfirm3')}
          </p>

          {/* Custom Buttons */}
          <div style={{ display: 'flex', gap: '16px', justifyContent: 'center' }}>
            <button
              onClick={handleArchiveCancel}
              style={{
                padding: '12px 32px',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                backgroundColor: '#ffffff',
                color: '#374151',
                fontSize: '16px',
                fontWeight: 500,
                cursor: 'pointer',
                minWidth: '100px',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
                e.currentTarget.style.borderColor = '#9ca3af';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#ffffff';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
            >
              {getMessage('no')}
            </button>
            <button
              onClick={handleArchiveConfirm}
              style={{
                padding: '12px 32px',
                borderRadius: '8px',
                border: 'none',
                backgroundColor: '#374151',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: 500,
                cursor: 'pointer',
                minWidth: '100px',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#1f2937';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#374151';
              }}
            >
              {getMessage('yes')}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default MiniappApplicationsList;
