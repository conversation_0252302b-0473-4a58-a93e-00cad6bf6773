import React from 'react';
import noMiniappImage from '~/assets/imgs/no-miniapp.png';
import newminiappIcon from '~/assets/icons/newminiapp.svg';
import { useLanguage } from '~/utils/i18n';

interface MiniappEmptyStateProps {
  fromArchived: boolean;
  onNewProject: () => void;
  onClose?: () => void;
}

const MiniappEmptyState: React.FC<MiniappEmptyStateProps> = ({ fromArchived, onNewProject }) => {
  const { getMessage } = useLanguage();
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        backgroundColor: '#ffffff',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      }}
    >
      {/* Main Content */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          flex: 1,
          padding: '20px 20px 40px 20px',
          gap: '24px',
        }}
      >
        {/* Illustration */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '8px',
          }}
        >
          <img
            src={noMiniappImage}
            alt="No MiniApp illustration"
            style={{
              width: '200px',
              height: 'auto',
              opacity: 0.8,
            }}
          />
        </div>

        {/* Text Content */}
        <div
          style={{
            textAlign: 'center',
            maxWidth: '300px',
          }}
        >
          <h2
            style={{
              fontSize: '20px',
              fontWeight: 600,
              color: '#111827',
              margin: '0 0 8px 0',
              lineHeight: 1.3,
            }}
          >
            {fromArchived ? getMessage('emptyArchivedMiniapps') : getMessage('emptyMiniapps')}
          </h2>
        </div>

        {/* New Project Button */}
        {!fromArchived && (
          <button
            onClick={onNewProject}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              padding: '14px 32px',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              backgroundColor: '#ffffff',
              color: '#374151',
              fontSize: '16px',
              fontWeight: 500,
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              minWidth: '300px',
              boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            }}
            onMouseOver={e => {
              e.currentTarget.style.backgroundColor = '#f9fafb';
              e.currentTarget.style.borderColor = '#9ca3af';
              e.currentTarget.style.boxShadow = '0 2px 4px 0 rgba(0, 0, 0, 0.1)';
            }}
            onMouseOut={e => {
              e.currentTarget.style.backgroundColor = '#ffffff';
              e.currentTarget.style.borderColor = '#d1d5db';
              e.currentTarget.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
            }}
          >
            <img
              src={newminiappIcon}
              alt="New Project"
              style={{
                width: 18,
                height: 18,
              }}
            />
            {getMessage('newProject')}
          </button>
        )}
      </div>
    </div>
  );
};

export default MiniappEmptyState;
