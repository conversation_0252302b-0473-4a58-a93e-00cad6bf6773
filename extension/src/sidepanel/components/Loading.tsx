import loadingGif from '~/assets/imgs/loading.gif';
import { useLanguage } from '~/utils/i18n';

export const Loading = () => {
  const { getMessage } = useLanguage();

  return (
    <div
      style={{
        paddingTop: 200,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 8,
      }}
    >
      <img
        src={loadingGif}
        alt="loading"
        style={{
          width: 64,
          height: 64,
        }}
      />

      <div
        style={{
          fontSize: 14,
          opacity: 0.6,
        }}
      >
        {getMessage('loading') || 'Loading...'}
      </div>
    </div>
  );
};
