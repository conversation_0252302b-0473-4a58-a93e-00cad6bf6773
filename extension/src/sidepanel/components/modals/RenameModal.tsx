import { Modal } from 'antd';
import { useEffect, useState } from 'react';
import { useLanguage } from '~/utils/i18n';

interface RenameModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (newName: string) => void;
}

export default function RenameModal({ open, onClose, onConfirm }: RenameModalProps) {
  const { getMessage } = useLanguage();
  const [newName, setNewName] = useState('');

  useEffect(() => {
    if (open) {
      setNewName('');
    }
  }, [open]);

  return (
    <Modal
      open={open}
      footer={null}
      centered
      closable={false}
      width={320}
      onCancel={onClose}
      styles={{
        body: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          padding: '0',
        },
        content: {
          borderRadius: 16,
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        },
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.45)',
        },
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '16px',
        }}
      >
        <div style={{ fontSize: '18px', fontWeight: 500 }}>{getMessage('renameConversation')}</div>

        <input
          type="text"
          value={newName}
          onChange={e => setNewName(e.target.value)}
          placeholder={getMessage('enterNewName')}
          style={{
            marginTop: '4px',
            width: '250px',
            padding: '12px',
            borderRadius: '8px',
            border: '1px solid #ccc',
            outline: 'none',
          }}
        />

        <div style={{ display: 'flex', gap: '16px', alignSelf: 'stretch' }}>
          <div
            style={{
              flex: 1,
              height: '36px',
              borderRadius: '8px',
              border: '1px solid #333',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onClick={() => {
              onClose();
            }}
          >
            <div
              style={{
                fontSize: 14,
                fontWeight: 500,
                color: '#333',
              }}
            >
              {getMessage('cancel')}
            </div>
          </div>

          <div
            style={{
              flex: 1,
              height: '36px',
              borderRadius: '8px',
              backgroundColor: '#333',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: newName.trim() ? 'pointer' : 'not-allowed',
              opacity: newName.trim() ? 1 : 0.5,
            }}
            onClick={e => {
              if (!newName.trim()) {
                return;
              }
              e.stopPropagation();
              onConfirm(newName.trim());
              onClose();
            }}
          >
            <div style={{ fontSize: 14, fontWeight: 500, color: '#fff' }}>{getMessage('save')}</div>
          </div>
        </div>
      </div>
    </Modal>
  );
}
