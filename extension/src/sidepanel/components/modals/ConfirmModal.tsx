import { Modal } from 'antd';
import { useLanguage } from '~/utils/i18n';

interface LoadingModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  content: string;
  title?: string;
}

export default function LoadingModal({
  open,
  content,
  title,
  onClose,
  onConfirm,
}: LoadingModalProps) {
  const { getMessage } = useLanguage();

  return (
    <Modal
      open={open}
      footer={null}
      centered
      closable={false}
      width={320}
      onCancel={onClose}
      styles={{
        body: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          padding: '0',
        },
        content: {
          borderRadius: 16,
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        },
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.45)',
        },
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '16px',
        }}
      >
        <div style={{ fontSize: '18px', fontWeight: 500 }}>{title || getMessage('confirm')}</div>

        {/* Default text if no text provided */}
        <div
          style={{
            fontSize: '14px',
            textAlign: 'center',
            lineHeight: '1.5',
          }}
        >
          {content}
        </div>

        <div style={{ display: 'flex', gap: '16px' }}>
          <div
            style={{
              width: '100px',
              height: '36px',
              borderRadius: '8px',
              border: '1px solid #333',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onClick={() => {
              onClose();
            }}
          >
            <div
              style={{
                fontSize: 14,
                fontWeight: 500,
                color: '#333',
              }}
            >
              {getMessage('no')}
            </div>
          </div>

          <div
            style={{
              width: '100px',
              height: '36px',
              borderRadius: '8px',
              backgroundColor: '#333',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onClick={() => {
              onClose();
              onConfirm();
            }}
          >
            <div style={{ fontSize: 14, fontWeight: 500, color: '#fff' }}>{getMessage('yes')}</div>
          </div>
        </div>
      </div>
    </Modal>
  );
}
