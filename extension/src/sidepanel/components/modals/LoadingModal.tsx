import { Modal } from 'antd';
import { useLanguage } from '~/utils/i18n';
import loadingGif from '~/assets/imgs/loading.gif';

interface LoadingModalProps {
  open: boolean;
  onClose?: () => void;
  text?: string;
  size?: 'small' | 'medium' | 'large';
}

export default function LoadingModal({ open, text, size = 'medium' }: LoadingModalProps) {
  const { getMessage } = useLanguage();

  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return { width: 200, spinnerSize: 32 };
      case 'large':
        return { width: 400, spinnerSize: 48 };
      default:
        return { width: 300, spinnerSize: 40 };
    }
  };

  const { width } = getSizeConfig();

  return (
    <Modal
      open={open}
      footer={null}
      centered
      closable={false}
      width={width}
      // onCancel={onClose}
      styles={{
        body: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          padding: '32px 24px',
        },
        content: {
          borderRadius: 16,
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        },
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.45)',
        },
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '16px',
        }}
      >
        {/* Spinner */}
        <img
          src={loadingGif}
          alt="loading"
          style={{
            width: 64,
            height: 64,
          }}
        />

        {/* Loading Text */}
        {text && (
          <div
            style={{
              fontSize: '16px',
              fontWeight: 500,
              color: '#374151',
              textAlign: 'center',
              lineHeight: '1.5',
            }}
          >
            {text}
          </div>
        )}

        {/* Default text if no text provided */}
        {!text && (
          <div
            style={{
              fontSize: '16px',
              fontWeight: 500,
              color: '#374151',
              textAlign: 'center',
              lineHeight: '1.5',
            }}
          >
            {getMessage('loading') || 'Loading...'}
          </div>
        )}
      </div>
    </Modal>
  );
}
