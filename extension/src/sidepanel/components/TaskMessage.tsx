import React from 'react';
import WaitIcon from '~/assets/icons/wait.svg';
import RunIcon from '~/assets/icons/run.svg';
import DoneIcon from '~/assets/icons/done.svg';
import ErrorIcon from '~/assets/icons/error.svg';
import ArrowIcon from '~/assets/icons/arrow.svg';
import { TaskStatus } from '@the-agent/shared';

interface TaskMessageProps {
  title: string;
  status: TaskStatus;
  error?: string | null;
  onClick?: () => void;
}

const TaskMessage: React.FC<TaskMessageProps> = ({ title, status, error, onClick }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'waiting':
        return {
          icon: WaitIcon,
          statusText: 'Waiting for execution',
          bgColor: '#f9fafb',
          borderColor: '#e5e7eb',
          dotColor: '#d1d5db',
        };
      case 'pending':
        return {
          icon: RunIcon,
          statusText: 'Being executed...',
          bgColor: '#eff6ff',
          borderColor: '#bfdbfe',
          dotColor: '#3b82f6',
          spinning: true,
        };
      case 'completed':
        return {
          icon: DoneIcon,
          statusText: 'Execution completed',
          bgColor: '#f0fdf4',
          borderColor: '#bbf7d0',
          dotColor: '#22c55e',
        };
      case 'error':
        return {
          icon: ErrorIcon,
          statusText: error || 'Execution error',
          bgColor: '#fef2f2',
          borderColor: '#fecaca',
          dotColor: '#ef4444',
        };
      default:
        return {
          icon: WaitIcon,
          statusText: 'Waiting for execution',
          bgColor: '#f9fafb',
          borderColor: '#e5e7eb',
          dotColor: '#d1d5db',
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
      {/* Timeline Dot */}
      <div
        style={{
          width: 12,
          height: 12,
          borderRadius: '50%',
          backgroundColor: config.dotColor,
          marginRight: 10,
          flexShrink: 0,
          position: 'relative',
          zIndex: 2,
        }}
      />

      {/* Content Card */}
      <div
        onClick={onClick}
        style={{
          width: 280,
          height: 44,
          backgroundColor: config.bgColor,
          border: `1px solid ${config.borderColor}`,
          borderRadius: 8,
          padding: '8px 10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexShrink: 0,
          cursor: onClick ? 'pointer' : 'default',
          transition: 'all 0.2s ease',
        }}
        onMouseEnter={e => {
          if (onClick) {
            e.currentTarget.style.transform = 'translateY(-1px)';
            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
          }
        }}
        onMouseLeave={e => {
          if (onClick) {
            e.currentTarget.style.transform = 'translateY(0px)';
            e.currentTarget.style.boxShadow = 'none';
          }
        }}
      >
        {/* Left side: Status Icon + Text Content */}
        <div style={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
          {/* Status Icon */}
          <img
            src={config.icon}
            alt={status}
            style={{
              width: 16,
              height: 16,
              marginRight: 8,
              flexShrink: 0,
              animation: config.spinning ? 'spin 1s linear infinite' : 'none',
            }}
          />

          {/* Text Content */}
          <div style={{ flex: 1, minWidth: 0, overflow: 'hidden' }}>
            <div
              style={{
                fontSize: 12,
                fontWeight: 600,
                color: '#1f2937',
                lineHeight: 1.2,
                marginBottom: 1,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {title}
            </div>
            <div
              style={{
                fontSize: 10,
                color: '#6b7280',
                lineHeight: 1.1,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {config.statusText}
            </div>
          </div>
        </div>

        {/* Right side: Arrow Icon */}
        <img
          src={ArrowIcon}
          alt="arrow"
          style={{
            width: 12,
            height: 12,
            marginLeft: 8,
            opacity: 0.5,
            transform: 'rotate(90deg)',
            flexShrink: 0,
          }}
        />
      </div>

      <style>{`
        @keyframes spin { 100% { transform: rotate(360deg); } }
      `}</style>
    </div>
  );
};

export default TaskMessage;
