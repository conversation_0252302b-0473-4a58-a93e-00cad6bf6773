import React from 'react';
import { Globe } from 'lucide-react';
import { Tooltip, Dropdown } from 'antd';
import { SUPPORTED_LANGUAGES, SupportedLanguage, useLanguage } from '~/utils/i18n';

interface LanguageSelectorProps {
  onLanguageChange?: (language: SupportedLanguage) => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ onLanguageChange }) => {
  const { currentLanguage, changeLanguage, getMessage } = useLanguage();

  const handleLanguageChange = async (language: SupportedLanguage) => {
    await changeLanguage(language);
    onLanguageChange?.(language);
  };

  const dropdownMenu = (
    <div
      style={{
        background: '#fff',
        border: '1px solid #e5e7eb',
        borderRadius: 8,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        minWidth: 120,
        overflow: 'hidden',
      }}
    >
      {Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => (
        <button
          key={code}
          style={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '8px 12px',
            height: '38px',
            background: currentLanguage === code ? '#f3f4f6' : 'transparent',
            border: 'none',
            cursor: 'pointer',
            fontSize: 14,
            lineHeight: '20px',
            color: '#374151',
            transition: 'background 0.2s',
            boxSizing: 'border-box',
          }}
          onClick={() => handleLanguageChange(code as SupportedLanguage)}
          onMouseOver={e => {
            if (currentLanguage !== code) {
              e.currentTarget.style.backgroundColor = '#f9fafb';
            }
          }}
          onMouseOut={e => {
            e.currentTarget.style.backgroundColor =
              currentLanguage === code ? '#f3f4f6' : 'transparent';
          }}
        >
          <span style={{ lineHeight: '20px' }}>{name}</span>
          {currentLanguage === code && (
            <span style={{ color: '#10b981', fontSize: 12, lineHeight: '20px' }}>✓</span>
          )}
        </button>
      ))}
    </div>
  );

  return (
    <Dropdown popupRender={() => dropdownMenu} trigger={['click']} placement="bottomRight">
      <Tooltip title={getMessage('language')} placement="bottom">
        <button
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: 'none',
            borderRadius: '50%',
            padding: 0,
            cursor: 'pointer',
            width: 32,
            height: 32,
            backgroundColor: 'transparent',
            transition: 'background 0.2s',
          }}
          onMouseOver={e => {
            e.currentTarget.style.backgroundColor = '#E5E7EB';
          }}
          onMouseOut={e => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <Globe color="#374151" size={20} />
        </button>
      </Tooltip>
    </Dropdown>
  );
};

export default LanguageSelector;
