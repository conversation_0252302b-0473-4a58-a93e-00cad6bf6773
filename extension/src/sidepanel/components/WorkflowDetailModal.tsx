import React from 'react';
import { ChatStatus, Message as SMessage } from '@the-agent/shared';
import TaskCard from './TaskCard';
import Message from './Message';
import { getMessage } from '~/utils/i18n';
import { Tooltip } from 'antd';
import { X } from 'lucide-react';
import { TaskWithStatus } from '~/types/task';

interface WorkflowDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  taskMessage: TaskWithStatus;
  history: SMessage[];
}

const WorkflowDetailModal: React.FC<WorkflowDetailModalProps> = ({
  isOpen,
  onClose,
  taskMessage,
  history,
}) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        zIndex: 1000,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      onClick={onClose}
    >
      <div
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}
        onClick={e => e.stopPropagation()}
      >
        {/* Header */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 16px',
            height: '44px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
            {getMessage('workflowDetail')}
          </h2>
          <Tooltip title={getMessage('tooltipClose')} placement="bottom">
            <button
              type="button"
              onClick={onClose}
              className="close-button"
              aria-label={getMessage('tooltipClose')}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>

        {/* Body */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            minHeight: 0,
          }}
        >
          {/* Task Card Section - Fixed height */}
          <div
            style={{
              padding: '12px 12px 0 12px',
              flexShrink: 0,
            }}
          >
            <TaskCard task={taskMessage} />
          </div>

          {/* History Messages Section - Scrollable */}
          {history.length > 0 && (
            <div
              style={{
                flex: 1,
                padding: '12px',
                overflowY: 'auto',
                minHeight: 0,
              }}
            >
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {history.map((message, index) => {
                  const isLast =
                    (index === history.length - 1 && message.role === 'assistant') ||
                    message.status === 'error';
                  return (
                    <Message
                      key={`${message.id}-${index}`}
                      message={{ ...message, version: 1 } as any}
                      isLatestResponse={isLast}
                      status={message.status as ChatStatus}
                      workflowMode={true}
                      taskId={taskMessage.task.id}
                    />
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WorkflowDetailModal;
