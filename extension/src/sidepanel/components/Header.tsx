import { AlignLeft, User as UserIcon } from 'lucide-react';
import { db, UserInfo } from '~/storages/indexdb';
import { useState, useEffect } from 'react';
import { Modal, Dropdown, Tooltip } from 'antd';
import { ProviderGroup } from './ModelCascader';
import { useLiveQuery } from 'dexie-react-hooks';
import { Model } from '~/types';
import { API_KEY_TAG, SYSTEM_MODEL_ID } from '~/configs/common';
import betaImg from '~/assets/beta.png';
import newchatIcon from '~/assets/icons/newchat.svg';
import miniappIcon from '~/assets/icons/miniapp.svg';
import LanguageSelector from './LanguageSelector';
import { useLanguage } from '~/utils/i18n';
import remoteIcon from '~/assets/icons/remote.svg';
import { useNavigate } from 'react-router-dom';

interface HeaderProps {
  createNewConversation: () => void;
  user?: UserInfo | null;
  setShowConversationList: (show: boolean) => void;
}

interface ModelGroup {
  type: string;
  models: Model[];
}

const Header = ({ createNewConversation, user, setShowConversationList }: HeaderProps) => {
  const navigate = useNavigate();
  const [providerGroups, setProviderGroups] = useState<ProviderGroup[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [, setSelectedModelId] = useState<string>('');
  const [apiKeyInput, setApiKeyInput] = useState('');
  const [editingProvider] = useState<string | null>(null);
  const [apiModalOpen, setApiModalOpen] = useState(false);
  const { getMessage } = useLanguage();

  const models = useLiveQuery(() => (user?.id ? db.getUserModels(user.id) : []), [user?.id]);

  useEffect(() => {
    const init = async () => {
      // build fullProviderGroups (with full model information)
      const fullGroups: Record<string, ModelGroup> = {};
      (models ?? []).forEach(model => {
        if (!fullGroups[model.type]) {
          fullGroups[model.type] = {
            type: model.type,
            models: [],
          };
        }
        fullGroups[model.type].models.push(model);
      });
      const fullProviderGroups = Object.values(fullGroups);

      const providerGroups = fullProviderGroups.map((g: ModelGroup) => ({
        type: g.type === 'system' ? 'Default' : g.type,
        models: g.models,
      }));
      setProviderGroups(providerGroups);

      // default selected Default provider and systemModelId
      let defaultProvider = 'Default';
      let defaultModelId = SYSTEM_MODEL_ID;

      // if there is Default provider
      const defaultGroup = providerGroups.find(g => g.type === 'Default');
      if (defaultGroup) {
        defaultProvider = defaultGroup.type;
        const systemModel = defaultGroup.models.find((m: Model) => m.id === SYSTEM_MODEL_ID);
        if (systemModel) {
          defaultModelId = SYSTEM_MODEL_ID;
        } else if (defaultGroup.models.length > 0) {
          defaultModelId = defaultGroup.models[0].id;
        }
      } else if (providerGroups.length > 0) {
        defaultProvider = providerGroups[0].type;
        defaultModelId = providerGroups[0].models[0]?.id;
      }

      if (user?.selectedModelId) {
        providerGroups.forEach(group => {
          const match = group.models.find((m: Model) => m.id === user.selectedModelId);
          if (match) {
            defaultProvider = group.type;
            defaultModelId = match.id;
          }
        });
      }

      setSelectedProvider(defaultProvider);
      setSelectedModelId(defaultModelId);
    };
    init();
  }, [user, models]);

  // When provider changes, update model selection
  useEffect(() => {
    if (!selectedProvider && providerGroups.length > 0) {
      setSelectedProvider(providerGroups[0].type);
    }
    const group = providerGroups.find(g => g.type === selectedProvider);
    if (group && group.models.length > 0) {
      setSelectedModelId(group.models[0].id);
    }
  }, [selectedProvider, providerGroups]);

  const handleApiKeySave = async () => {
    try {
      // Update all models for this provider with the new API key
      const user = await db.getCurrentUser();
      if (user) {
        const userModels = await db.getUserModels(user.id);
        const modelsToUpdate = userModels.filter((m: Model) => m.type === editingProvider);
        for (const model of modelsToUpdate) {
          await db.addOrUpdateModel({
            ...model,
            apiKey: apiKeyInput,
            type: editingProvider || '',
            userId: user.id,
          });
        }
        // Refresh provider groups
        const groups: Record<string, ProviderGroup> = {};
        userModels.forEach((model: Model) => {
          if (!groups[model.type]) {
            groups[model.type] = {
              type: model.type,
              models: [],
            };
          }
          groups[model.type].models.push({ id: model.id, name: model.name });
        });
        setProviderGroups(Object.values(groups));
      }
      setApiModalOpen(false);
    } catch (error) {
      console.error('Failed to save API key:', error);
    }
  };

  // custom dropdown menu
  const dropdownMenu = (
    <div
      style={{
        minWidth: 200,
        background: '#fff',
        borderRadius: 18,
        boxShadow: '0 2px 12px 0 rgba(0,0,0,0.30)',
        padding: '18px 18px 12px 18px',
        display: 'flex',
        flexDirection: 'column',
        gap: 12,
      }}
    >
      <div
        style={{
          color: '#bdbdbd',
          fontSize: 14,
          marginBottom: 8,
          textAlign: 'center',
          wordBreak: 'break-all',
        }}
      >
        {user?.email || user?.username || getMessage('user')}
      </div>

      <button
        style={{
          width: '100%',
          background: '#232323',
          color: '#fff',
          border: '2px solid #232323',
          borderRadius: 12,
          fontSize: 14,
          padding: '8px 0',
          marginTop: 2,
          marginBottom: 2,
          cursor: 'pointer',
          boxShadow: '0 1px 2px 0 rgba(0,0,0,0.04)',
          transition: 'background 0.18s, color 0.18s',
          textAlign: 'center',
        }}
        onClick={() => {
          window.open(process.env.PLASMO_PUBLIC_WEB_URL || '', '_blank');
        }}
      >
        {getMessage('viewProfile')}
      </button>
      <button
        style={{
          width: '100%',
          background: '#232323',
          color: '#fff',
          border: '2px solid #232323',
          borderRadius: 12,
          fontSize: 14,
          padding: '8px 0',
          marginTop: 2,
          marginBottom: 2,
          cursor: 'pointer',
          boxShadow: '0 1px 2px 0 rgba(0,0,0,0.04)',
          transition: 'background 0.18s, color 0.18s',
          textAlign: 'center',
        }}
        onClick={async () => {
          if (!user) {
            return;
          }
          // Directly update user's active status without using saveOrUpdateUser
          await db.users.update(user.id, { active: false });
          await chrome.storage.local.remove(API_KEY_TAG);
          navigate('/');
        }}
      >
        {getMessage('logout')}
      </button>
    </div>
  );

  const buttonStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    border: 'none',
    borderRadius: '50%',
    padding: 0,
    cursor: 'pointer',
  };

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#ffffff',
        padding: '0 16px',
        height: '44px',
        borderBottom: '1px solid #f0f0f0',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          height: '44px',
          gap: '4px',
        }}
      >
        <Tooltip title={getMessage('conversations')} placement="bottom">
          <button
            style={{
              ...buttonStyle,
              width: 32,
              height: 32,
              backgroundColor: 'transparent',
              transition: 'background 0.2s',
            }}
            onClick={() => setShowConversationList(true)}
            onMouseOver={e => {
              e.currentTarget.style.backgroundColor = '#E5E7EB';
            }}
            onMouseOut={e => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <AlignLeft color="#374151" size={20} />
          </button>
        </Tooltip>
        <img
          src={betaImg}
          alt="BETA"
          style={{
            height: '14px',
            width: 'auto',
            display: 'inline-block',
            verticalAlign: 'middle',
          }}
        />
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
        <Tooltip title={getMessage('remoteConversation')} placement="bottom">
          <button
            style={{
              ...buttonStyle,
              width: 32,
              height: 32,
              backgroundColor: 'transparent',
              transition: 'background 0.2s',
            }}
            onMouseOver={e => {
              e.currentTarget.style.backgroundColor = '#E5E7EB';
            }}
            onMouseOut={e => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            onClick={() => navigate('/remote')}
          >
            <img src={remoteIcon} alt={'remote'} style={{ width: 20, height: 20 }} />
          </button>
        </Tooltip>

        <Tooltip title={getMessage('miniapp')} placement="bottom">
          <button
            style={{
              ...buttonStyle,
              width: 32,
              height: 32,
              backgroundColor: 'transparent',
              transition: 'background 0.2s',
            }}
            onClick={() => navigate('/miniapps')}
            onMouseOver={e => {
              e.currentTarget.style.backgroundColor = '#E5E7EB';
            }}
            onMouseOut={e => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <img src={miniappIcon} alt={getMessage('miniapp')} style={{ width: 20, height: 20 }} />
          </button>
        </Tooltip>

        <Tooltip title={getMessage('newChat')} placement="bottom">
          <button
            style={{
              ...buttonStyle,
              width: 32,
              height: 32,
              backgroundColor: 'transparent',
              transition: 'background 0.2s',
            }}
            onClick={createNewConversation}
            onMouseOver={e => {
              e.currentTarget.style.backgroundColor = '#E5E7EB';
            }}
            onMouseOut={e => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <img src={newchatIcon} alt={getMessage('newChat')} style={{ width: 20, height: 20 }} />
          </button>
        </Tooltip>

        <Dropdown popupRender={() => dropdownMenu} trigger={['click']} placement="bottomRight">
          <Tooltip title={getMessage('user')} placement="bottom">
            <button
              style={{
                ...buttonStyle,
                width: 32,
                height: 32,
                backgroundColor: 'transparent',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              {user?.photoURL ? (
                <img
                  src={user.photoURL}
                  alt={user.username || getMessage('user')}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: '50%',
                    display: 'block',
                  }}
                />
              ) : (
                <UserIcon color="#374151" size={20} />
              )}
            </button>
          </Tooltip>
        </Dropdown>
        <LanguageSelector />
      </div>

      {/* API Key Modal */}
      <Modal
        open={apiModalOpen}
        onCancel={() => setApiModalOpen(false)}
        footer={null}
        centered
        closable
        style={{
          background: '#fff',
          borderRadius: 10,
          color: '#111',
        }}
      >
        <div style={{ fontSize: 15, fontWeight: 700, marginBottom: 10 }}>
          {getMessage('setApiKey')}:
          {editingProvider
            ? editingProvider.charAt(0).toUpperCase() + editingProvider.slice(1)
            : ''}
        </div>

        <div
          style={{
            width: '100%',
            gap: 12,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <input
            placeholder={getMessage(
              'enterApiKey',
              editingProvider
                ? editingProvider.charAt(0).toUpperCase() + editingProvider.slice(1)
                : 'API'
            )}
            value={apiKeyInput}
            onChange={e => setApiKeyInput(e.target.value)}
            style={{
              width: '100%',
              height: 32,
              borderRadius: 6,
              border: '1px solid #e5e7eb',
              background: '#fafafa',
              color: '#111',
              padding: '0 8px',
              marginBottom: 10,
              fontSize: 13,
              boxSizing: 'border-box',
            }}
          />
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 0,
              width: '100%',
            }}
          >
            <button
              style={{
                background: '#22c55e',
                color: '#fff',
                border: 'none',
                borderRadius: 6,
                padding: '7px 0',
                fontWeight: 600,
                fontSize: 14,
                cursor: 'pointer',
                width: '100%',
                marginBottom: 8,
                boxSizing: 'border-box',
              }}
              onClick={handleApiKeySave}
            >
              {getMessage('save')}
            </button>
            <button
              style={{
                background: '#dc2626',
                color: '#fff',
                border: 'none',
                borderRadius: 6,
                padding: '7px 0',
                fontWeight: 600,
                fontSize: 14,
                cursor: 'pointer',
                width: '100%',
                boxSizing: 'border-box',
              }}
              onClick={() => setApiModalOpen(false)}
            >
              {getMessage('cancel')}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Header;
