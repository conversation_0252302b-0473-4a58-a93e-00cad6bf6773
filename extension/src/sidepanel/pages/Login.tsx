import { useEffect, useCallback, useState } from 'react';
import '../../style.css';
import bgImg from '~/assets/bg.png';
import bgLogo from '~/assets/bg-logo.png';
import { env } from '~/configs/env';
import RunIcon from '~/assets/icons/run.svg';

import { db } from '~/storages/indexdb';
import { ApiKey } from '~/types';
import { getUserInfo, syncUserData } from '~/services/user';
import { parseApiKey } from '~/services/api/key';
import { useLanguage } from '~/utils/i18n';
import { handleApiError } from '~/utils/conversation';
import { useNavigate } from 'react-router-dom';
import { API_KEY_TAG } from '~/configs/common';

const LoginPage = () => {
  const navigate = useNavigate();
  const { getMessage } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);

  const initializeUserAndData = useCallback(
    async (apiKeyToUse: ApiKey) => {
      setIsLoading(true);
      try {
        const existingUser = await db.getUserByApiKey(apiKeyToUse.key);

        if (existingUser) {
          await db.saveOrUpdateUser({
            ...existingUser,
            active: true,
          });
          await syncUserData({ userId: existingUser.id, apiKey: existingUser.api_key });
          navigate('/conversation');
          return;
        }

        const userInfo = await getUserInfo(apiKeyToUse);

        await db.initModels(userInfo.id);
        await db.saveOrUpdateUser(userInfo);

        // if indexdb has no user, new user, api key update when request endpoint
        await syncUserData({ userId: userInfo.id, apiKey: apiKeyToUse.key });
        navigate('/conversation');
      } catch (error) {
        handleApiError(error, -1, getMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [getMessage, navigate]
  );

  // Listen for API key changes
  useEffect(() => {
    const listener = async (
      changes: { [key: string]: chrome.storage.StorageChange },
      area: string
    ) => {
      const user = await db.getCurrentUser();
      if (user) {
        return;
      }
      if (area === 'local' && changes[API_KEY_TAG]) {
        const newApiKey = parseApiKey(changes[API_KEY_TAG].newValue);
        if (newApiKey) {
          await initializeUserAndData(newApiKey);
        }
      }
    };
    chrome.storage.onChanged.addListener(listener);
    return () => chrome.storage.onChanged.removeListener(listener);
  }, [initializeUserAndData]);

  // Initialize from storage on mount
  useEffect(() => {
    chrome.storage.local.get([API_KEY_TAG], async result => {
      const storedApiKey = parseApiKey(result[API_KEY_TAG]);
      if (storedApiKey) {
        await Promise.all([
          initializeUserAndData(storedApiKey),
          new Promise(res => setTimeout(res, 1000)),
        ]);
        await chrome.storage.local.remove(API_KEY_TAG);
      } else {
        await new Promise(res => setTimeout(res, 1000));
        const user = await db.getCurrentUser();
        if (user && user.active === true) {
          navigate('/conversation');
        }
      }
    });
  }, [initializeUserAndData, navigate]);

  return (
    <div
      style={{
        minHeight: '100vh',
        width: '100vw',
        backgroundImage: `url(${bgImg})`,
        backgroundSize: 'cover',
        backgroundPosition: 'bottom',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 10,
      }}
    >
      <div style={{ flex: 1 }} />
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          maxWidth: 400,
          margin: '0 auto',
          zIndex: 2,
        }}
      >
        <img src={bgLogo} alt="Mysta Logo" style={{ width: 80, height: 80, marginBottom: 32 }} />
        <div
          style={{
            fontSize: 26,
            fontWeight: 700,
            color: '#fff',
            marginBottom: 16,
            textAlign: 'center',
            lineHeight: 1.2,
          }}
        >
          {getMessage('letAiRunTheWeb')}
        </div>
        <div
          style={{
            fontSize: 18,
            fontWeight: 400,
            color: '#d1d5db',
            marginBottom: 32,
            textAlign: 'center',
          }}
        >
          {getMessage('askAnything')}
        </div>
        <button
          style={{
            width: '90%',
            maxWidth: 320,
            height: 50,
            borderRadius: 12,
            border: 'none',
            background: '#fff',
            color: '#232323',
            fontSize: 18,
            fontWeight: 500,
            marginBottom: 32,
            cursor: isLoading ? 'not-allowed' : 'pointer',
            boxShadow: '0 2px 8px 0 rgba(0,0,0,0.10)',
            transition: 'background 0.2s, color 0.2s',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 8,
          }}
          onClick={async () => {
            if (!isLoading) {
              setIsLoading(true);
              window.open(env.WEB_URL, '_blank');
            }
          }}
          disabled={isLoading}
        >
          {isLoading ? (
            <img
              src={RunIcon}
              alt="loading"
              style={{
                width: 18,
                height: 18,
                animation: 'spin 1s linear infinite',
              }}
            />
          ) : (
            getMessage('getStarted')
          )}
        </button>
      </div>
      <div style={{ flex: 2 }} />
      <div
        style={{
          position: 'absolute',
          bottom: 24,
          left: 0,
          width: '100%',
          textAlign: 'center',
          color: '#d1d5db',
          fontSize: 13,
          letterSpacing: 0.1,
          zIndex: 2,
          textShadow: '0 1px 4px rgba(0,0,0,0.25)',
        }}
      >
        {getMessage('privacyDisclaimer')}
      </div>

      <style>
        {`
          @keyframes spin { 100% { transform: rotate(360deg); } }
        `}
      </style>
    </div>
  );
};

export { LoginPage };
