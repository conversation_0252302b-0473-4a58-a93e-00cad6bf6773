import { useCallback, useEffect, useMemo, useState } from 'react';
import '../../style.css';
import { ConversationList, Header, InputArea, TopBanner } from '../components';

import { ChatStatus, Message as SMessage } from '@the-agent/shared';
import { useLiveQuery } from 'dexie-react-hooks';
import StarIcon from '~/assets/icons/star.svg';
import welcomeImg from '~/assets/imgs/prompt.png';
import { ChatHandler } from '~/chat/handler';
import { db } from '~/storages/indexdb';
import { useUser } from '~/hooks/useUser';
import { handleApiError } from '~/utils/conversation';
import { createNewConversation } from '~/services/conversation';
import { useLanguage } from '../../utils/i18n';
import TestWebToolkit from './TestWebToolkit';
import { MessageList } from '../components';
import { BANNER_STORAGE_KEY } from '~/configs/common';
import { useNavigate } from 'react-router-dom';

const WelcomeComponent = ({ chatHandler }: { chatHandler: ChatHandler | null }) => {
  const { getMessage } = useLanguage();
  const getPromptTemplates = () => [
    getMessage('summarizeElonTweet'),
    getMessage('postTweet'),
    getMessage('searchLinkedIn'),
  ];
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '0px 24px',
        minHeight: '100%',
      }}
    >
      <div
        style={{
          maxWidth: '480px',
          textAlign: 'center',
        }}
      >
        <img src={welcomeImg} alt="Welcome" style={{ maxWidth: '100%' }} />
        {/* Welcome text */}
        <p
          style={{
            fontSize: '25px',
            fontWeight: '600',
            color: '#374151',
            marginBottom: '0px',
            marginTop: '-30px',
          }}
        >
          {getMessage('askAnything').split('\n')[0]}
          <br />
          {getMessage('askAnything').split('\n')[1]}
        </p>
        <p
          style={{
            fontSize: '16px',
            color: '#6b7280',
            lineHeight: '1.6',
            marginBottom: '0',
          }}
        >
          {getMessage('startTyping')}
        </p>
        {/* Prompt templates */}
        <div style={{ marginTop: '20px' }}>
          {getPromptTemplates().map((template: string, index: number) => (
            <div
              key={index}
              onClick={() => {
                if (chatHandler) {
                  chatHandler.handleSubmit(template);
                }
              }}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '16px',
                marginBottom: '12px',
                borderRadius: '12px',
                border: '1px solid #e5e7eb',
                backgroundColor: '#f9fafb',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f3f4f6';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
                e.currentTarget.style.borderColor = '#e5e7eb';
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '24px',
                  height: '24px',
                  marginRight: '12px',
                  flexShrink: 0,
                }}
              >
                <img src={StarIcon} alt="star" />
              </div>
              <div style={{ textAlign: 'left', fontSize: '14px', color: '#4b5563' }}>
                {template}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const ConversationPage = () => {
  const { getMessage } = useLanguage();
  const navigate = useNavigate();

  const [prompt, setPrompt] = useState('');
  const [status, setStatus] = useState<ChatStatus>('idle');
  const [chatHandler, setChatHandler] = useState<ChatHandler | null>(null);

  const [hasBanner, setHasBanner] = useState(false);
  const [showTestWebToolkit, setShowTestWebToolkit] = useState(false);
  const [workflowMode, setWorkflowMode] = useState(false); // for now, force using chat mode
  const [showConversationList, setShowConversationList] = useState(false);

  const activeUser = useUser();

  const conversations = useLiveQuery(async () => {
    if (activeUser === null) {
      navigate('/');
      return [];
    }
    if (activeUser === undefined) {
      return [];
    }
    return await db.getAllConversations(activeUser.id, 'default');
  }, [activeUser?.id]);

  // Handle creating new conversation when none exists
  useEffect(() => {
    const createInitialConversation = async () => {
      if (activeUser?.id && conversations && conversations.length === 0) {
        try {
          await createNewConversation(activeUser.id);
        } catch (error) {
          console.error('Failed to create initial conversation:', error);
        }
      }
    };

    createInitialConversation();
  }, []);

  const convId = useMemo(() => conversations?.[0]?.id ?? -1, [conversations, activeUser?.id]);

  useEffect(() => {
    if (convId !== -1) {
      setChatHandler(
        new ChatHandler({
          currentConversationId: convId,
          workflowMode,
          conversationType: 'default',
          setStatus,
          onMessageUpdate: async (message: SMessage) => {
            await db.saveMessage(message);
          },
        })
      );
    }
  }, [convId, workflowMode, setStatus]);

  const abort = useCallback(() => {
    chatHandler?.abort();
  }, [chatHandler]);

  const handleCreateNewConversation = async () => {
    try {
      if (activeUser?.id) {
        await createNewConversation(activeUser.id);
      }
    } catch (error) {
      handleApiError(error, convId, getMessage);
    }
  };

  // Monitor TopBanner visibility state
  useEffect(() => {
    const checkBannerState = () => {
      chrome.storage.local.get([BANNER_STORAGE_KEY], result => {
        setHasBanner(!result[BANNER_STORAGE_KEY]);
      });
    };

    // Initial check
    checkBannerState();

    // Listen for storage changes
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes[BANNER_STORAGE_KEY]) {
        setHasBanner(!changes[BANNER_STORAGE_KEY].newValue);
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, []);

  const toggleConversationList = async (value?: boolean) => {
    const willShow = value !== undefined ? value : !showConversationList;
    setShowConversationList(willShow);
  };

  if (showTestWebToolkit) {
    return <TestWebToolkit onBack={() => setShowTestWebToolkit(false)} />;
  }

  return (
    <div
      style={{
        height: '100vh',
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          zIndex: 10,
        }}
      >
        <Header
          setShowConversationList={toggleConversationList}
          createNewConversation={handleCreateNewConversation}
          user={activeUser}
        />

        {/* Test WebToolkit Entry Button */}
        {process.env.NODE_ENV !== 'production' && (
          <div style={{ position: 'absolute', top: 50, right: 8 }}>
            <button
              onClick={() => setShowTestWebToolkit(true)}
              style={{
                fontSize: 12,
                padding: '4px 8px',
                borderRadius: 6,
                border: '1px solid #e5e7eb',
                backgroundColor: '#f9fafb',
                cursor: 'pointer',
                color: '#374151',
              }}
              title="Open Test WebToolkit"
            >
              open test web-toolkit
            </button>
          </div>
        )}
      </div>

      {/* Top Banner */}
      <TopBanner />

      {/* Messages Area */}
      <MessageList
        key={convId}
        convId={convId}
        workflowMode={workflowMode}
        welcomeComponent={<WelcomeComponent chatHandler={chatHandler} />}
        status={status}
        hasBanner={hasBanner}
      />

      {/* Input Area */}
      <div
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          zIndex: 10,
        }}
      >
        <InputArea
          prompt={prompt}
          setPrompt={setPrompt}
          onSubmitRich={chatHandler?.handleSubmit}
          status={status}
          abort={abort}
          workflowMode={workflowMode}
          onWorkflowModeChange={setWorkflowMode}
          allowWorkflowMode={false}
        />
      </div>

      {/* Conversation List */}
      {showConversationList && (
        <ConversationList
          conversations={conversations || []}
          setShowConversationList={(show: boolean) => toggleConversationList(show)}
        />
      )}
    </div>
  );
};

export { ConversationPage };
