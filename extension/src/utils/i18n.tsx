import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Storage } from '@plasmohq/storage';

declare const chrome: any;

// Import all translations
import { enMessages } from '../locales/en';
import { zhCNMessages } from '../locales/zh_CN';

const storage = new Storage();
const LANGUAGE_KEY = 'extension_language';

// Supported languages
export const SUPPORTED_LANGUAGES = {
  en: 'English',
  zh_CN: '中文',
} as const;

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;

// Message registry
const messageRegistry: Record<SupportedLanguage, Record<string, string>> = {
  en: enMessages,
  zh_CN: zhCNMessages,
};

// I18n Context type
interface I18nContextType {
  currentLanguage: SupportedLanguage;
  changeLanguage: (language: SupportedLanguage) => Promise<void>;
  getMessage: (key: string, substitutions?: string | string[]) => string;
  getMessageWithFallback: (
    key: string,
    fallback: string,
    substitutions?: string | string[]
  ) => string;
}

// Create Context
const I18nContext = createContext<I18nContextType | undefined>(undefined);

// Get the current language setting
export const getCurrentLanguage = async (): Promise<SupportedLanguage> => {
  const saved = await storage.get(LANGUAGE_KEY);
  if (saved && saved in SUPPORTED_LANGUAGES) {
    return saved as SupportedLanguage;
  }

  // Default to browser language if supported, otherwise English
  const browserLang = chrome.i18n.getUILanguage();
  if (browserLang.startsWith('zh')) {
    return 'zh_CN';
  }
  return 'en';
};

// I18n Provider Component
interface I18nProviderProps {
  children: ReactNode;
}

export const I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>('zh_CN');
  const [currentMessages, setCurrentMessages] = useState<Record<string, string>>(
    messageRegistry.zh_CN
  );

  // Initialize language on mount
  useEffect(() => {
    const initLanguage = async () => {
      const lang = await getCurrentLanguage();
      setCurrentLanguage(lang);
      setCurrentMessages(messageRegistry[lang]);
    };

    initLanguage();
  }, []);

  // Listen for language changes from other parts of the extension
  useEffect(() => {
    const handleMessage = (message: any) => {
      if (message.type === 'LANGUAGE_CHANGED' && message.language in SUPPORTED_LANGUAGES) {
        const lang = message.language as SupportedLanguage;
        setCurrentLanguage(lang);
        setCurrentMessages(messageRegistry[lang]);
      }
    };

    // Listen for custom events
    const handleLanguageChange = (event: CustomEvent) => {
      if (event.detail.language in SUPPORTED_LANGUAGES) {
        const lang = event.detail.language as SupportedLanguage;
        setCurrentLanguage(lang);
        setCurrentMessages(messageRegistry[lang]);
      }
    };

    chrome.runtime.onMessage.addListener(handleMessage);
    window.addEventListener('languageChanged', handleLanguageChange as EventListener);

    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage);
      window.removeEventListener('languageChanged', handleLanguageChange as EventListener);
    };
  }, []);

  const changeLanguage = async (language: SupportedLanguage): Promise<void> => {
    // Update storage
    await storage.set(LANGUAGE_KEY, language);

    // Update local state
    setCurrentLanguage(language);
    setCurrentMessages(messageRegistry[language]);

    // Notify other extension pages
    chrome.runtime.sendMessage({ type: 'LANGUAGE_CHANGED', language });

    // Trigger custom event for other components
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language } }));
    }
  };

  const getMessage = (key: string, substitutions?: string | string[]): string => {
    const message = currentMessages[key];
    if (!message) {
      console.warn(`Missing translation key: ${key}`);
      return key; // Return key as fallback
    }

    let result = message;

    // Handle substitutions
    if (substitutions) {
      const subs = Array.isArray(substitutions) ? substitutions : [substitutions];
      subs.forEach((sub, index) => {
        result = result.replace(`$${index + 1}`, sub);
      });
    }

    return result;
  };

  const getMessageWithFallback = (
    key: string,
    fallback: string,
    substitutions?: string | string[]
  ): string => {
    const message = getMessage(key, substitutions);
    return message === key ? fallback : message;
  };

  const contextValue: I18nContextType = {
    currentLanguage,
    changeLanguage,
    getMessage,
    getMessageWithFallback,
  };

  return <I18nContext.Provider value={contextValue}>{children}</I18nContext.Provider>;
};

// Hook to use I18n context
export const useLanguage = (): I18nContextType => {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useLanguage must be used within an I18nProvider');
  }
  return context;
};

// Standalone functions for compatibility (will use storage to get current language)
let globalMessages: Record<string, string> = messageRegistry.zh_CN;
let globalLanguage: SupportedLanguage = 'zh_CN';

// Initialize global state
const initGlobal = async () => {
  globalLanguage = await getCurrentLanguage();
  globalMessages = messageRegistry[globalLanguage];
};

// Global getMessage function for use outside React components
export const getMessage = (key: string, substitutions?: string | string[]): string => {
  const message = globalMessages[key];
  if (!message) {
    console.warn(`Missing translation key: ${key}`);
    return key;
  }

  let result = message;

  if (substitutions) {
    const subs = Array.isArray(substitutions) ? substitutions : [substitutions];
    subs.forEach((sub, index) => {
      result = result.replace(`$${index + 1}`, sub);
    });
  }

  return result;
};

// Global setCurrentLanguage function
export const setCurrentLanguage = async (language: SupportedLanguage): Promise<void> => {
  await storage.set(LANGUAGE_KEY, language);
  globalLanguage = language;
  globalMessages = messageRegistry[language];

  chrome.runtime.sendMessage({ type: 'LANGUAGE_CHANGED', language });

  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language } }));
  }
};

// Initialize on module load
if (typeof window !== 'undefined') {
  initGlobal();
}
