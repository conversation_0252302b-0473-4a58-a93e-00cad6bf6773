import { APIError, Conversation, extractTextFromContent } from '@the-agent/shared';
import { db } from '~/storages/indexdb';

export function getConversationTitle(conversation: Conversation, defaultTitle: string): string {
  if (conversation.title && conversation.title !== defaultTitle) {
    return conversation.title;
  }
  if (conversation.messages && conversation.messages.length > 0) {
    const message = conversation.messages[0];
    const messageText = extractTextFromContent(message.content);
    if (messageText.length > 0) {
      return messageText.slice(0, 50);
    }
  }
  return defaultTitle;
}

export const handleApiError = (
  error: unknown,
  currentConversationId: number,
  getMessage: (key: string) => string
) => {
  if (error instanceof APIError) {
    if (error.status === 401 || error.status === 403) {
      window.location.href = '/';
      if (currentConversationId !== -1) {
        db.saveErrorMessage(currentConversationId, getMessage('apiKeyDisabled'));
      }
      return;
    } else if (error.status === 402) {
      if (currentConversationId !== -1) {
        db.saveErrorMessage(currentConversationId, getMessage('insufficientCredits'));
      }
      return;
    }
  }
  let message = getMessage('errorOccurred');
  if (error instanceof Error) {
    message = error.message;
  } else if (typeof error === 'string') {
    message = error;
  }
  if (currentConversationId !== -1) {
    db.saveErrorMessage(currentConversationId, message);
  }
};
