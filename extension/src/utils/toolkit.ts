import { <PERSON><PERSON><PERSON><PERSON>, Tool<PERSON>allResult } from '@the-agent/shared';
import OpenAI from 'openai';
import { ToolDescription } from '~/types/tools';
import { getKeyInfo, getModifierFlags, isModifierKey, KeyInfo } from './key';

export interface SendKeysParams {
  keys: string;
}

export interface SendKeysResult {
  sent: boolean;
  keys: string;
}

// Command sender type for methods that need to send debugger commands
export type CommandSender = (method: string, params?: object) => Promise<object>;

/**
 * Send a key combination (e.g., Ctrl+C, Ctrl+Shift+T)
 */
export async function sendKeyCombo(keys: string[], sendCommand: CommandSender): Promise<void> {
  const modifiers: Array<{ key: string; code: string; keyCode: number }> = [];
  let mainKey: KeyInfo | null = null;

  // Separate modifiers from the main key
  for (const key of keys) {
    const keyInfo = getKeyInfo(key);
    if (isModifierKey(key)) {
      modifiers.push(keyInfo);
    } else {
      mainKey = keyInfo;
    }
  }

  try {
    // Press modifiers down
    for (const modifier of modifiers) {
      await sendCommand('Input.dispatchKeyEvent', {
        type: 'keyDown',
        key: modifier.key,
        code: modifier.code,
        windowsVirtualKeyCode: modifier.keyCode,
        nativeVirtualKeyCode: modifier.keyCode,
        modifiers: getModifierFlags(modifiers.map(m => m.key)),
      });
    }

    // Press main key down and up
    if (mainKey) {
      await sendCommand('Input.dispatchKeyEvent', {
        type: 'keyDown',
        key: mainKey.key,
        code: mainKey.code,
        windowsVirtualKeyCode: mainKey.keyCode,
        nativeVirtualKeyCode: mainKey.keyCode,
        modifiers: getModifierFlags(modifiers.map(m => m.key)),
        text: mainKey.text,
      });

      await sendCommand('Input.dispatchKeyEvent', {
        type: 'keyUp',
        key: mainKey.key,
        code: mainKey.code,
        windowsVirtualKeyCode: mainKey.keyCode,
        nativeVirtualKeyCode: mainKey.keyCode,
        modifiers: getModifierFlags(modifiers.map(m => m.key)),
      });
    }

    // Release modifiers
    for (let i = modifiers.length - 1; i >= 0; i--) {
      const modifier = modifiers[i];
      await sendCommand('Input.dispatchKeyEvent', {
        type: 'keyUp',
        key: modifier.key,
        code: modifier.code,
        windowsVirtualKeyCode: modifier.keyCode,
        nativeVirtualKeyCode: modifier.keyCode,
        modifiers: getModifierFlags(modifiers.slice(0, i).map(m => m.key)),
      });
    }
  } catch (error) {
    console.error('Error sending key combo:', error);
    throw error;
  }
}

/**
 * Send a single key press
 */
export async function sendSingleKey(key: string, sendCommand: CommandSender): Promise<void> {
  const keyInfo = getKeyInfo(key);

  await sendCommand('Input.dispatchKeyEvent', {
    type: 'keyDown',
    key: keyInfo.key,
    code: keyInfo.code,
    windowsVirtualKeyCode: keyInfo.keyCode,
    nativeVirtualKeyCode: keyInfo.keyCode,
    text: keyInfo.text,
  });

  await sendCommand('Input.dispatchKeyEvent', {
    type: 'keyUp',
    key: keyInfo.key,
    code: keyInfo.code,
    windowsVirtualKeyCode: keyInfo.keyCode,
    nativeVirtualKeyCode: keyInfo.keyCode,
  });
}

export function toOpenAITools(toolDescription: ToolDescription[]): OpenAI.ChatCompletionTool[] {
  return toolDescription.map(tool => ({
    type: 'function' as const,
    function: {
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters,
    },
    returns: tool.returns,
  }));
}

export function parseToolParams(toolCall: ToolCall): unknown {
  try {
    return toolCall.function.arguments ? JSON.parse(toolCall.function.arguments) : {};
  } catch (error) {
    console.error('Error parsing tool arguments:', error);
    return {};
  }
}

export async function executeWebToolkit(name: string, params: any): Promise<ToolCallResult> {
  const message = {
    name: 'execute-tool',
    body: { name, arguments: params },
  };

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, response => {
      if (chrome.runtime.lastError) {
        console.error('WebToolkit error:', chrome.runtime.lastError);
        reject(chrome.runtime.lastError);
        return;
      }

      if (!response) {
        const error = new Error('No response received from background script');
        console.error(error);
        reject(error);
        return;
      }

      if (!response.success) {
        const error = new Error(response.error || 'Unknown error');
        console.error('WebToolkit failed:', error);
        reject(error);
        return;
      }

      resolve(response);
    });
  });
}

export async function executeTabToolkit(name: string, params: any): Promise<ToolCallResult> {
  const message = {
    name: 'execute-tool',
    body: { name, arguments: params },
  };

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, response => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }

      if (!response) {
        const error = new Error('No response received from background script');
        console.error(error);
        reject(error);
        return;
      }

      if (!response.success) {
        const error = new Error(response.error || 'Unknown error');
        console.error('TabToolkit failed:', error);
        reject(error);
        return;
      }

      resolve(response);
    });
  });
}
