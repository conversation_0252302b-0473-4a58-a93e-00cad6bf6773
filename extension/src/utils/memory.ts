import { APIClient, MemoryMetadata, Message } from '@the-agent/shared';

export async function storeMemory(
  apiClient: APIClient,
  messages: Message[],
  metadata: MemoryMetadata
) {
  if (!apiClient || messages.length === 0) {
    return;
  }

  try {
    // separate semantic memory and procedural memory
    const semanticMessages: Message[] = [];
    const proceduralMessages: Message[] = [];

    messages.forEach(msg => {
      if (msg.role === 'user' || msg.role === 'assistant') {
        semanticMessages.push(msg);
      }

      if (msg.role === 'system' || msg.role === 'tool') {
        proceduralMessages.push(msg);
      }
    });

    // store semantic memory
    if (semanticMessages.length > 0) {
      await apiClient.addMemory({
        messages: semanticMessages,
        config: {
          filters: {
            conversationId: metadata.conversationId?.toString(),
          },
          metadata: {
            ...metadata,
            memoryType: 'semantic',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        },
      });
    }

    // store procedural memory
    if (proceduralMessages.length > 0) {
      await apiClient.addMemory({
        messages: proceduralMessages,
        config: {
          filters: {
            conversationId: metadata.conversationId?.toString(),
            agentId: metadata.agentId,
            taskId: metadata.taskId,
            workflowId: metadata.workflowId,
          },
          metadata: {
            ...metadata,
            memoryType: 'procedural',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        },
      });
    }
  } catch (error) {
    console.error('Failed to store memory:', error);
  }
}
