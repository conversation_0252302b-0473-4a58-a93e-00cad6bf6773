// Key sequence types
export interface KeySequence {
  isCombo: boolean;
  keys: string[];
}

export interface KeyInfo {
  key: string;
  code: string;
  keyCode: number;
  text?: string;
}

export const KEY_MAP: Record<string, KeyInfo> = {
  // Special keys
  Escape: { key: 'Escape', code: 'Escape', keyCode: 27 },
  Enter: { key: 'Enter', code: 'Enter', keyCode: 13, text: '\r' },
  Tab: { key: 'Tab', code: 'Tab', keyCode: 9, text: '\t' },
  Backspace: { key: 'Backspace', code: 'Backspace', keyCode: 8 },
  Delete: { key: 'Delete', code: 'Delete', keyCode: 46 },
  Insert: { key: 'Insert', code: 'Insert', keyCode: 45 },
  Home: { key: 'Home', code: 'Home', keyCode: 36 },
  End: { key: 'End', code: 'End', keyCode: 35 },
  PageUp: { key: 'PageUp', code: 'PageUp', keyCode: 33 },
  PageDown: { key: 'PageDown', code: 'PageDown', keyCode: 34 },
  ArrowLeft: { key: '<PERSON>Left', code: 'ArrowLeft', keyCode: 37 },
  ArrowUp: { key: 'ArrowUp', code: 'ArrowUp', keyCode: 38 },
  ArrowRight: { key: 'ArrowRight', code: 'ArrowRight', keyCode: 39 },
  ArrowDown: { key: 'ArrowDown', code: 'ArrowDown', keyCode: 40 },
  Space: { key: ' ', code: 'Space', keyCode: 32, text: ' ' },

  // Modifier keys
  Control: { key: 'Control', code: 'ControlLeft', keyCode: 17 },
  Ctrl: { key: 'Control', code: 'ControlLeft', keyCode: 17 },
  Shift: { key: 'Shift', code: 'ShiftLeft', keyCode: 16 },
  Alt: { key: 'Alt', code: 'AltLeft', keyCode: 18 },
  Meta: { key: 'Meta', code: 'MetaLeft', keyCode: 91 },
  Command: { key: 'Meta', code: 'MetaLeft', keyCode: 91 },
  Cmd: { key: 'Meta', code: 'MetaLeft', keyCode: 91 },

  // Function keys
  F1: { key: 'F1', code: 'F1', keyCode: 112 },
  F2: { key: 'F2', code: 'F2', keyCode: 113 },
  F3: { key: 'F3', code: 'F3', keyCode: 114 },
  F4: { key: 'F4', code: 'F4', keyCode: 115 },
  F5: { key: 'F5', code: 'F5', keyCode: 116 },
  F6: { key: 'F6', code: 'F6', keyCode: 117 },
  F7: { key: 'F7', code: 'F7', keyCode: 118 },
  F8: { key: 'F8', code: 'F8', keyCode: 119 },
  F9: { key: 'F9', code: 'F9', keyCode: 120 },
  F10: { key: 'F10', code: 'F10', keyCode: 121 },
  F11: { key: 'F11', code: 'F11', keyCode: 122 },
  F12: { key: 'F12', code: 'F12', keyCode: 123 },
};

/**
 * Parse key sequence string into individual key sequences
 */
export function parseKeySequence(keys: string): KeySequence[] {
  const sequences: KeySequence[] = [];

  // Split by spaces to handle multiple key sequences
  const keyParts = keys.split(' ');

  for (const part of keyParts) {
    if (part.includes('+')) {
      // This is a key combination
      sequences.push({
        isCombo: true,
        keys: part.split('+').map(k => k.trim()),
      });
    } else {
      // This is a single key
      sequences.push({
        isCombo: false,
        keys: [part.trim()],
      });
    }
  }

  return sequences;
}

/**
 * Check if a key is a modifier key
 */
export function isModifierKey(key: string): boolean {
  const modifiers = ['Control', 'Ctrl', 'Shift', 'Alt', 'Meta', 'Command', 'Cmd'];
  return modifiers.includes(key);
}

/**
 * Get modifier flags for key combinations
 */
export function getModifierFlags(modifierKeys: string[]): number {
  let flags = 0;

  for (const key of modifierKeys) {
    switch (key) {
      case 'Alt':
        flags |= 1; // Alt
        break;
      case 'Control':
      case 'Ctrl':
        flags |= 2; // Ctrl
        break;
      case 'Meta':
      case 'Command':
      case 'Cmd':
        flags |= 4; // Meta/Cmd
        break;
      case 'Shift':
        flags |= 8; // Shift
        break;
    }
  }

  return flags;
}

/**
 * Get key information including key code and other properties
 */
export function getKeyInfo(key: string): KeyInfo {
  // Check if it's a known special key
  if (KEY_MAP[key]) {
    return KEY_MAP[key];
  }

  // For regular characters, use the character itself
  if (key.length === 1) {
    const char = key.toLowerCase();
    const keyCode = char.charCodeAt(0);
    return {
      key: key,
      code: `Key${char.toUpperCase()}`,
      keyCode: keyCode >= 97 && keyCode <= 122 ? keyCode - 32 : keyCode,
      text: key,
    };
  }

  // Fallback
  return { key: key, code: key, keyCode: 0, text: key };
}
