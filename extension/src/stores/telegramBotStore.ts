import { create } from 'zustand';
import { logoutTelegramBotClient } from '~/services/gramjs';

export interface LocalBotParams {
  token: string;
  groupChatId?: string;
  targetUserId?: string;
}

export interface TelegramBotState {
  localBotParams: LocalBotParams | null;
}

export interface TelegramBotActions {
  setLocalBotParams: (setup: LocalBotParams | null) => void;
  logoutTelegramBot: () => Promise<void>;
}

export interface TelegramBotStore extends TelegramBotState, TelegramBotActions {}

// Storage key for persisting localBotParams
const LOCAL_BOT_PARAMS_STORAGE_KEY = 'telegram-bot-local-params';

// Helper function to load saved localBotParams from localStorage
const loadLocalBotParams = (): LocalBotParams | null => {
  try {
    const saved = localStorage.getItem(LOCAL_BOT_PARAMS_STORAGE_KEY);
    return saved ? JSON.parse(saved) : null;
  } catch (error) {
    console.error('Failed to load localBotParams from localStorage:', error);
    return null;
  }
};

// Helper function to save localBotParams to localStorage
const saveLocalBotParams = (params: LocalBotParams | null): void => {
  try {
    if (params) {
      localStorage.setItem(LOCAL_BOT_PARAMS_STORAGE_KEY, JSON.stringify(params));
    } else {
      localStorage.removeItem(LOCAL_BOT_PARAMS_STORAGE_KEY);
    }
  } catch (error) {
    console.error('Failed to save localBotParams to localStorage:', error);
  }
};

// Initial state with loaded data from localStorage
const initialState: TelegramBotState = {
  localBotParams: loadLocalBotParams(),
};

// Create store
export const useTelegramBotStore = create<TelegramBotStore>()(set => ({
  ...initialState,

  setLocalBotParams: (params: LocalBotParams | null) => {
    set({ localBotParams: params }, false);
    saveLocalBotParams(params);
  },
  logoutTelegramBot: async () => {
    await logoutTelegramBotClient();
  },
}));

// Selectors for better performance
export const useLocalBotParams = () => useTelegramBotStore(state => state.localBotParams);
