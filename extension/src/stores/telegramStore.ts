import { Api } from '@the-agent/telegram';
import { create } from 'zustand';
import {
  getInputHandlers,
  getTelegramApiCredentials,
  getTelegramClient,
  logoutTelegramClient,
} from '~/services/gramjs';
import { ApiUpdateAuthorizationStateType } from '~/services/gramjs/types';

// Types
export interface QrCodeData {
  token: string;
  expires: number;
}

export interface TelegramState {
  authState?: ApiUpdateAuthorizationStateType;
  telegramUser: Api.TypeUser | null;
  isConnected: boolean;
  qrCodeData: QrCodeData | null;
  hint: string | null;
  error: string | null;
}

export interface TelegramActions {
  updateAuthState: (state: ApiUpdateAuthorizationStateType | undefined) => void;
  setTelegramUser: (user: Api.TypeUser | null) => void;
  setIsConnected: (connected: boolean) => void;
  setQrCodeData: (data: QrCodeData | null) => void;
  setHint: (hint: string | null) => void;
  setError: (error: string | null) => void;
  signInTelegram: () => Promise<void>;
  logoutTelegram: () => Promise<void>;
}

export interface TelegramStore extends TelegramState, TelegramActions {}

// Initial state
const initialState: TelegramState = {
  authState: undefined,
  telegramUser: null,
  isConnected: false,
  qrCodeData: null,
  hint: null,
  error: null,
};

// Create store
export const useTelegramStore = create<TelegramStore>()((set, get) => ({
  ...initialState,

  // Actions
  updateAuthState: (authState: ApiUpdateAuthorizationStateType | undefined) => {
    const currentAuthState = get().authState;

    switch (authState) {
      case 'authorizationStateWaitQrCode':
        if (!currentAuthState) {
          set({ authState: 'authorizationStateWaitQrCode' }, false);
        }
        break;
      case 'authorizationStateWaitPassword':
        if (currentAuthState === 'authorizationStateWaitQrCode') {
          set({ authState: 'authorizationStateWaitPassword' }, false);
        }
        break;
      case 'authorizationStateReady':
        set({ authState: 'authorizationStateReady' }, false);
        break;
      case undefined:
        set({ authState: undefined }, false);
        break;
      default:
        break;
    }
  },

  setTelegramUser: (user: Api.TypeUser | null) => set({ telegramUser: user }, false),

  setIsConnected: (isConnected: boolean) => set({ isConnected }, false),

  setQrCodeData: (qrCodeData: QrCodeData | null) => set({ qrCodeData }, false),

  setHint: (hint: string | null) => set({ hint }, false),

  setError: (error: string | null) => set({ error }, false),

  signInTelegram: async () => {
    const telegramClient = getTelegramClient();
    try {
      await telegramClient.connect();

      const apiCredentials = getTelegramApiCredentials();
      const inputHandlers = getInputHandlers();
      const isAuthorized = await telegramClient.isUserAuthorized();
      if (isAuthorized) {
        get().updateAuthState('authorizationStateReady');
        return;
      }

      // await telegramClient.start(inputHandlers);
      const user = await telegramClient.signInUserWithQrCode(apiCredentials, inputHandlers);

      if (user) {
        localStorage.setItem('session', JSON.stringify(telegramClient.session.save()));
        get().updateAuthState('authorizationStateReady');
        get().setTelegramUser(user);
      }
    } catch (err) {
      get().updateAuthState(undefined);
      localStorage.removeItem('session');
      console.error('=========initTelegram err========', err);
      setTimeout(() => {
        get().signInTelegram();
      }, 1000);
    }
  },

  logoutTelegram: async () => {
    set(initialState, false);
    await logoutTelegramClient();
  },
}));

// Selectors for better performance
export const useAuthState = () => useTelegramStore(state => state.authState);
export const useIsConnected = () => useTelegramStore(state => state.isConnected);
export const useQrCodeData = () => useTelegramStore(state => state.qrCodeData);
export const useHint = () => useTelegramStore(state => state.hint);
export const useError = () => useTelegramStore(state => state.error);
