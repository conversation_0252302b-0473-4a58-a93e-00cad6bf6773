import {
  Agent,
  Message,
  RuntimeInput,
  Task,
  TaskAgentRunConfig,
  TaskNode,
  TaskResult,
} from '@the-agent/shared';
import { GlobalContext } from '~/types/task';

const MAX_RETRY = 3;

export interface TaskNodeConfig {
  id?: string;
  parent?: TaskNode | null;
  runtimeInput?: string;
  atomic?: boolean;
}

export class MystaTaskNode implements TaskNode {
  private c: GlobalContext;

  public id: string;
  public task: Task;
  public nested_tasks: TaskNode[] = [];
  public atomic: boolean = true;
  public history: Message[] = [];
  public parent: TaskNode | null = null;
  public depth: number = 0;

  public started_at?: string;
  public completed_at?: string;
  public runtimeInput?: string;
  public result: TaskResult | null = null;

  constructor(c: GlobalContext, task: Task, config?: TaskNodeConfig) {
    this.id = config?.id ?? task.id;
    this.c = c;
    this.task = task;
    this.parent = config?.parent ?? null;
    this.depth = config?.parent?.depth ?? 0;
    this.runtimeInput = config?.runtimeInput;
    this.atomic = config?.atomic ?? true;
    this.c.tasks[this.id] = this;
  }

  async run(onTaskComplete?: (result: TaskResult[]) => void): Promise<TaskResult> {
    this.started_at = new Date().toISOString();
    this.c.processing.push(this.id);
    if (this.nested_tasks.length > 0) {
      return await this.executeNestedTasks(onTaskComplete);
    }

    if (this.task.repeat) {
      for (let i = 0; i < this.task.repeat; i++) {
        const node = new MystaTaskNode(this.c, this.task, {
          parent: this,
          id: `${this.id}.run_${i}`,
          atomic: false,
          runtimeInput: this.runtimeInput,
        });
        this.nested_tasks.push(node);
      }
      return await this.executeNestedTasks(onTaskComplete);
    } else if (this.task.foreach) {
      return await this.executeWithAgent(this.c.agents.foreach!, {
        user: 'Please execute the task provided',
        continue: 'Please call ForeachToolkit_iterate tool with proper inputs to finish the task',
        error: 'Failed to execute the task, please check the error message',
      });
    } else if (!this.atomic && this.nested_tasks.length === 0) {
      return await this.executeWithAgent(this.c.agents.planner!, {
        user: 'Please resolve the task provided.',
        continue: 'Please call PlannerAgent_run or BrowserAgent_run tool to finish the task.',
        error: 'Failed to execute the task, please check the error message',
      });
    } else if (this.nested_tasks.length > 0) {
      for (const subtask of this.nested_tasks) {
        const node = new MystaTaskNode(this.c, subtask.task, {
          parent: this,
          id: `${this.id}.${subtask.id}`,
        });
        this.nested_tasks.push(node);
      }
      return await this.executeNestedTasks(onTaskComplete);
    } else {
      return await this.executeWithAgent(this.c.agents.browser!, {
        user: 'Please execute the task provided',
        continue:
          'Please call TaskToolkit_finishTask tool to finish the task if you cannot make progress, or continue to solve the task if you can.',
        error: 'Failed to execute the task, please check the error message',
      });
    }
  }

  async executeNestedTasks(onTaskComplete?: (result: TaskResult[]) => void): Promise<TaskResult> {
    if (this.nested_tasks.length === 0) {
      return this.finish({
        id: this.id,
        status: 'error',
        output: 'No nested tasks to execute',
      });
    }

    const results = [];
    for (const task of this.nested_tasks) {
      const result = await task.run();
      results.push(result);
      onTaskComplete?.(results);
    }
    if (this.task.foreach || this.task.repeat) {
      return this.finish({
        id: this.id,
        status: 'completed',
        output: results.map(r => JSON.stringify(r)).join('\n'),
      });
    } else {
      return this.finish(results[results.length - 1]!);
    }
  }

  async executeWithAgent(agent: Agent, config: TaskAgentRunConfig): Promise<TaskResult> {
    let message = this.buildMessage(agent, config.user);
    let retry = MAX_RETRY;
    while (retry >= 0) {
      if (this.c.aborted) {
        return this.abort();
      }
      const history = await agent.run(message, {
        chatOptions: this.c.chatOptions,
        taskOptions: { task: this },
      });

      this.history.push(...history);

      const last = history[history.length - 1];
      if (last?.status === 'error') {
        return {
          id: this.id,
          status: 'error',
          output: last.content ?? 'no output',
        };
      }
      if (this.result) {
        return this.result;
      }
      message = this.buildMessage(agent, config.continue);
      retry = retry - 1;
    }
    return { id: this.id, status: 'error', output: config.error };
  }

  finish(result: TaskResult): TaskResult {
    this.result = result;
    this.completed_at = new Date().toISOString();
    this.c.processing.splice(this.c.processing.indexOf(this.id), 1);
    this.c.processed.push(this.id);
    return result;
  }

  getTaskInputs(): RuntimeInput[] {
    return (this.task.inputFromTasks ?? [])
      .map(taskId => {
        const task = this.searchTask(taskId);
        if (task && task.result) {
          return {
            ...task.result,
            runtimeInput: task.runtimeInput,
          } as RuntimeInput;
        }
      })
      .filter(t => t != undefined);
  }

  searchTask(taskId: string): TaskNode | null {
    if (this.c.tasks[taskId]) {
      return this.c.tasks[taskId];
    }
    // check sibling task
    if (this.parent) {
      const siblingId = `${this.parent.id}.${taskId}`;
      if (this.c.tasks[siblingId]) {
        return this.c.tasks[siblingId];
      }
    }
    // check parent sibling task, for foreach or repeat tasks
    if (this.parent?.parent) {
      const grandpa = this.parent.parent;
      const uncleId = `${grandpa.id}.${taskId}`;
      if (this.c.tasks[uncleId]) {
        return this.c.tasks[uncleId];
      }
    }
    return null;
  }

  private abort(): TaskResult {
    return this.finish({
      id: this.id,
      status: 'error',
      output: 'Task aborted by user',
    });
  }

  private buildMessage(agent: Agent, content: string): Message {
    return {
      id: Date.now(),
      conversation_id: this.c.chatOptions.conversationId,
      role: 'user',
      content,
      status: 'pending',
      actor: this.id === 'root' ? 'user' : 'system',
      run_id: this.c.workflowId,
      agent_id: agent.getConfig().id,
      task_id: this.id,
    };
  }
}
