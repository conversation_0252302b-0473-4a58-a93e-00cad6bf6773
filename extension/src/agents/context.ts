import {
  APIClient,
  ContextBuilder,
  MemoryOptions,
  Message,
  SearchMemoryResponse,
  MemoryItem,
  SearchMemoryRequestV2,
  ContextBuilderConfig,
} from '@the-agent/shared';
import {
  buildMemoryContextChunk,
  buildProceduralMemoryContextChunk,
  buildTaskContextPrompt,
} from '~/chat/prompt';
import { ContextChunk } from '~/types';
import { db } from '~/storages/indexdb';
import { GlobalContext } from '~/types/task';

const SIMPLE_MEMORY_OPTIONS: MemoryOptions = {
  recent: 10,
  related: 0,
  site: 0,
  graph: 0,
  tab: false,
};

const BROWSER_MEMORY_OPTIONS: MemoryOptions = {
  recent: 5,
  related: 5,
  site: 5,
  graph: 0,
  tab: true,
};

function buildContext(chunks: ContextChunk[], message: Message): Message {
  const prompt = chunks
    .map(chunk => {
      const title = chunk.title ? `## ${chunk.title}\n` : '';
      const footer = chunk.footer ? `\n${chunk.footer}` : '';
      return `${title}${chunk.content}${footer}`;
    })
    .join('\n\n---\n\n');
  return {
    id: Date.now(),
    role: 'user',
    content: prompt,
    conversation_id: message.conversation_id,
    actor: 'system',
    status: 'completed',
    task_id: message.task_id,
    agent_id: message.agent_id,
    run_id: message.run_id,
  };
}

async function getValidRecentMessages(message: Message, limit: number): Promise<Message[]> {
  const filter = message.task_id
    ? {
        runId: message.run_id,
        taskId: message.task_id,
      }
    : undefined;
  const messages = await db.getRecentMessages(message.conversation_id, limit, filter);

  const result: Message[] = [];
  let hasSeenNonToolMessage = false;

  for (const message of messages) {
    // skip system messages
    if (message.role === 'system') {
      continue;
    }

    // skip error user message since it's not useful for context
    if (message.role === 'user' && message.status === 'error') {
      continue;
    }

    // Skip leading tool messages (tool messages before any user/assistant messages)
    if (message.role === 'tool' && !hasSeenNonToolMessage) {
      continue;
    }

    // Mark that we've seen a non-tool message
    if (message.role !== 'tool') {
      hasSeenNonToolMessage = true;
    }

    if (message.role === 'tool') {
      result.push({
        ...message,
        content: message.status === 'completed' ? 'Tool call success' : 'Tool call failed',
        tool_calls: undefined,
      });
    } else {
      result.push(message);
    }
  }

  // Remove trailing user messages from the end
  while (result.length > 0 && result[result.length - 1].role === 'user') {
    result.pop();
  }

  return result;
}

export class DummyContextBuilder implements ContextBuilder {
  async build(message: Message): Promise<Message[]> {
    return [message];
  }
}

export class TaskContextBuilder implements ContextBuilder {
  async build(message: Message, config?: ContextBuilderConfig): Promise<Message[]> {
    const memoryOptions = { ...SIMPLE_MEMORY_OPTIONS, ...(config?.memoryOptions ?? {}) };
    const recentMessages = await getValidRecentMessages(message, memoryOptions.recent);
    if (!config?.taskOptions) {
      throw new Error('Missing task info');
    }
    const { task } = config.taskOptions;
    const chunks: ContextChunk[] = [buildTaskContextPrompt(task)];
    const contextMessage = buildContext(chunks, message);
    return [...recentMessages, contextMessage, message];
  }
}

export class DevContextBuilder implements ContextBuilder {
  async build(message: Message, config?: ContextBuilderConfig): Promise<Message[]> {
    const memoryOptions = { ...SIMPLE_MEMORY_OPTIONS, ...(config?.memoryOptions ?? {}) };
    const results = await getValidRecentMessages(message, memoryOptions.recent);
    const lastScriptMessage = await db.findLastToolCall(
      message.conversation_id,
      'DevToolkit_render'
    );
    // Create a new message with script context instead of modifying the original
    if (lastScriptMessage) {
      const included = results.some(msg => msg.id === lastScriptMessage?.id);
      if (!included) {
        const { tool_call_arguments } = (lastScriptMessage?.metadata ?? {}) as {
          tool_call_arguments?: string;
        };
        const { script } = JSON.parse(tool_call_arguments || '{}') as { script: string };
        results.push(
          buildContext(
            [
              {
                title: 'Latest Script Version:',
                content: `\`\`\`${script}\`\`\``,
              },
            ],
            message
          )
        );
      }
    }
    return [...results, message];
  }
}

export class BrowserAgentContextBuilder implements ContextBuilder {
  private apiClient: APIClient;
  private c?: GlobalContext;

  constructor(apiClient: APIClient, c?: GlobalContext) {
    this.apiClient = apiClient;
    this.c = c;
  }

  async build(message: Message, config?: ContextBuilderConfig): Promise<Message[]> {
    const memoryOptions = { ...BROWSER_MEMORY_OPTIONS, ...(config?.memoryOptions ?? {}) };
    const recentMessages = await getValidRecentMessages(message, memoryOptions.recent);
    const relatedMemories = await this.searchMemory(message, memoryOptions.related);

    const semanticMemories = relatedMemories.results.filter(
      (mem: MemoryItem) => mem.metadata?.memoryType === 'semantic'
    );
    const proceduralMemories = relatedMemories.results.filter(
      (mem: MemoryItem) => mem.metadata?.memoryType === 'procedural'
    );

    const chunks: ContextChunk[] = [
      buildMemoryContextChunk(semanticMemories),
      buildProceduralMemoryContextChunk(proceduralMemories),
    ].filter(chunk => chunk !== undefined);
    if (this.c && message.task_id) {
      const task = this.c.tasks[message.task_id];
      chunks.push(buildTaskContextPrompt(task!));
    }

    const contextMessage = buildContext(chunks, message);
    return [...recentMessages, contextMessage, message];
  }

  private async searchMemory(message: Message, limit: number = 3): Promise<SearchMemoryResponse> {
    if (!message.content || limit <= 0) {
      return {
        results: [],
        relations: [],
      };
    }

    const newFilters: SearchMemoryRequestV2['config']['filters'] = {};

    if (message.task_id) {
      newFilters.taskId = message.task_id.toString();
    }

    if (message.conversation_id) {
      newFilters.conversationId = message.conversation_id.toString();
    }

    if (message.agent_id) {
      newFilters.agentId = message.agent_id.toString();
    }

    if (message.run_id) {
      newFilters.runId = message.run_id.toString();
    }

    // Get workflow_id from context if available
    if (this.c?.workflowId) {
      newFilters.workflowId = this.c.workflowId;
    }
    return await this.apiClient.searchMemoryV2({
      text: message.content || '',
      config: {
        limit,
        filters: newFilters,
      },
    });
  }
}
