import { Message, Task, TaskResult } from '@the-agent/shared';
import { db } from '~/storages/indexdb';

export interface TaskState {
  id: string;
  runId: string;
  task: Task;
  atomic: boolean;
  nestedTasks: TaskState[];
  result?: TaskResult | null;
}

export async function buildTaskState(message: Message, task: TaskState) {
  const { name, metadata } = message;
  const { tool_call_arguments, tool_call_result } = metadata as {
    tool_call_arguments: string;
    tool_call_result: TaskResult[];
  };
  if (name === 'PlannerAgent_run') {
    parsePlannerAgentRun(tool_call_arguments, tool_call_result, task);
  } else if (name === 'ForeachAgent_run') {
    await parseForeachAgentRun(tool_call_arguments!, tool_call_result!, task);
  }
}

function parsePlannerAgentRun(args: string, results: TaskResult[], task: TaskState) {
  const resultMap = buildTaskResultMap(results);
  const { tasks: subtasks } = JSON.parse(args ?? '{}') as { tasks: Task[] };
  for (const subtask of subtasks) {
    const subTaskId = `${task.id}.${subtask.id}`;
    const subtaskState: TaskState = {
      id: subTaskId,
      runId: task.runId,
      task: subtask,
      atomic: true,
      nestedTasks: [],
      result: resultMap[subTaskId],
    };
    task.nestedTasks.push(subtaskState);

    const { repeat, ...nested } = subtask;
    if (repeat) {
      for (let i = 0; i < repeat; i++) {
        const nestedTaskId = `${subtaskState.id}.run_${i}`;
        subtaskState.nestedTasks.push({
          id: nestedTaskId,
          runId: task.runId,
          task: nested,
          atomic: false,
          nestedTasks: [],
          result: resultMap[nestedTaskId],
        });
      }
    }
  }
}

async function parseForeachAgentRun(args: string, results: TaskResult[], task: TaskState) {
  const resultMap = buildTaskResultMap(results);
  const { inputs } = JSON.parse(args ?? '{}') as { inputs: string[] };
  const foreachTask = task.task.foreach as Task;
  for (let i = 0; i < inputs.length; i++) {
    const nestedTaskId = `${task.id}.${foreachTask.id}_run_${i}`;
    const nestedTaskState: TaskState = {
      id: nestedTaskId,
      runId: task.runId,
      task: foreachTask,
      nestedTasks: [],
      atomic: false,
      result: resultMap[nestedTaskId],
    };
    task.nestedTasks.push(nestedTaskState);
    const { repeat, ...nested } = foreachTask;
    if (repeat) {
      for (let i = 0; i < repeat; i++) {
        const nestedTaskId = `${nestedTaskState.id}.run_${i}`;
        nestedTaskState.nestedTasks.push({
          id: nestedTaskId,
          runId: task.runId,
          task: nested,
          atomic: false,
          nestedTasks: [],
          result: resultMap[nestedTaskId],
        });
      }
    }
    await queryNestedTasks(nestedTaskState);
  }
}

async function queryNestedTasks(task: TaskState) {
  const messages = await db.getMessagesByRunIdAndTaskId(task.runId, task.id);
  const msg = messages.find(msg => msg.role === 'tool' && msg.name === 'PlannerAgent_run');
  const { tool_call_arguments, tool_call_result } = (msg?.metadata ?? {}) as {
    tool_call_arguments?: string;
    tool_call_result?: TaskResult[];
  };
  if (msg && tool_call_arguments && tool_call_result) {
    parsePlannerAgentRun(tool_call_arguments, tool_call_result, task);
    return;
  }
  const msg2 = messages.find(msg => msg.role === 'tool' && msg.name === 'BrowserAgent_run');
  if (msg2) {
    task.atomic = true;
    return;
  }
}

function buildTaskResultMap(results: TaskResult[]): Record<string, TaskResult> {
  const resultMap: Record<string, TaskResult> = {};
  for (const result of results) {
    resultMap[result.id] = result;
  }
  return resultMap;
}
