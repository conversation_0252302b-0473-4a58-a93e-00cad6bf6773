import { db } from '~/storages/indexdb';
import {
  APIClient,
  Message,
  ChatOptions,
  Agent,
  ChatStatus,
  MemoryMetadata,
  Task,
  MessageContent,
  extractTextFromContent,
} from '@the-agent/shared';
import { createApiClient } from '~/services/api/client';
import { createBrowserAgent, createTaskBrowserAgent } from '~/agents/browser';
import { GlobalContext } from '~/types/task';
import OpenAI from 'openai';
import { createForeachAgent } from '~/agents/foreach';
import { createPlannerAgent } from '~/agents/planner';
import { storeMemory } from '~/utils/memory';
import { MystaTaskNode } from '~/agents/node';
import { getMessage } from '~/utils/i18n';
import { handleApiError } from '~/utils/conversation';
import { backupConversation } from '~/services/conversation';
import { createDevAgent } from '~/agents/dev';
import { MiniAppLocal as MiniApp } from '~/types/miniapp';

export interface ChatHandlerOptions {
  currentConversationId: number;
  workflowMode: boolean;
  conversationType: 'default' | 'remote' | 'miniapp';
  setStatus: (status: ChatStatus) => void;
  onMessageUpdate?: (message: Message) => void;
  onMessageComplete?: (message: Message) => void;
}

export class ChatHandler {
  private apiClient: APIClient | null = null;
  private options: ChatHandlerOptions;
  private agents: Agent[] = [];
  private globalContext: GlobalContext | null = null;

  constructor(options: ChatHandlerOptions) {
    this.options = options;
    // Ensure 'this' context is preserved when methods are passed as callbacks
    this.handleSubmit = this.handleSubmit.bind(this);
  }

  async handleSubmit(content: MessageContent) {
    if (!content) {
      return;
    }

    const userInput = extractTextFromContent(content);
    this.options.setStatus?.('running');
    if (!this.apiClient) {
      this.apiClient = await createApiClient();
    }

    try {
      const model = await db.getSelectModel();
      const llmClient = new OpenAI({
        baseURL: model.apiUrl,
        apiKey: model.apiKey,
        dangerouslyAllowBrowser: true,
      });
      const chatOptions = this.buildChatOptions(this.apiClient);
      if (this.options.workflowMode) {
        const workflowId = 'workflow_' + Date.now();
        const task: Task = {
          id: 'root',
          goal: userInput,
        };
        const c: GlobalContext = {
          workflowId,
          processing: [],
          processed: [],
          chatOptions: this.buildChatOptions(this.apiClient),
          tasks: {},
          agents: {},
          aborted: false,
        };
        c.agents.browser = createTaskBrowserAgent(model.name, llmClient, this.apiClient, c);
        c.agents.planner = createPlannerAgent(model.name, llmClient, c);
        c.agents.foreach = createForeachAgent(model.name, llmClient, c);

        // Store references for abort functionality
        this.globalContext = c;

        this.agents = [c.agents.browser, c.agents.planner, c.agents.foreach];
        const node = new MystaTaskNode(c, task, { atomic: false });
        await node.run();
      } else {
        if (this.agents.length > 0) {
          console.error('Agent is still running');
          throw new Error('Agent is still running');
        }

        const userMessage: Message = {
          id: Date.now(),
          conversation_id: chatOptions.conversationId,
          role: 'user',
          content: JSON.stringify(content),
          status: 'pending',
          actor: 'user',
        };
        chatOptions.onMessageUpdate?.(userMessage);
        let agent: Agent;
        if (this.options.conversationType === 'miniapp') {
          agent = createDevAgent(model.name, llmClient);
        } else {
          agent = createBrowserAgent(model.name, llmClient, this.apiClient);
        }
        this.agents = [agent];
        await agent.run(userMessage, { chatOptions });
      }
    } catch (error: unknown) {
      handleApiError(error, this.options.currentConversationId, getMessage);
    } finally {
      this.abort();
      this.agents = [];

      // Clean up workflow mode references
      this.globalContext = null;

      this.options.setStatus?.('idle');
    }
  }

  private buildChatOptions(apiClient: APIClient): ChatOptions {
    return {
      conversationId: this.options.currentConversationId,
      onMessageUpdate: async (message: Message) => {
        await db.saveMessage(message);
        this.options.onMessageUpdate?.(message);
      },
      onMessageComplete: async (message: Message) => {
        if (message.status !== 'error') {
          message.status = 'completed';
        }
        await db.saveMessage(message);
        this.options.onMessageComplete?.(message);
        backupMessage(apiClient, message);
      },
      onStatusChange: async (status: ChatStatus) => {
        this.options.setStatus(status);
      },
      onChatComplete: async (messages: Message[]) => {
        if (this.apiClient && messages.length > 0) {
          const metadata = buildMemoryMetadata(messages[0]);
          await storeMemory(this.apiClient, messages, metadata);
        }
      },
    };
  }

  abort() {
    // Abort single agent mode
    this.agents.forEach(agent => agent.abort());

    // Abort workflow mode agents and set abort flag
    if (this.globalContext) {
      this.globalContext.aborted = true;
    }
  }
}

function buildMemoryMetadata(message: Message): MemoryMetadata {
  const metadata: MemoryMetadata = {
    conversationId: message.conversation_id.toString(),
  };

  if (message.task_id) {
    metadata.taskId = message.task_id;
  }
  if (message.agent_id) {
    metadata.agentId = message.agent_id;
  }
  if (message.run_id) {
    metadata.runId = message.run_id;
  }
  return metadata;
}

// preprocess message for remote storage, only omit large data for analyzePageDOM tool
function preprocessMessageForRemote(message: Message): Message {
  const processedMessage = { ...message };
  const metadata = processedMessage.metadata as Record<string, unknown>;
  if (processedMessage.name === 'WebToolkit_analyzePageDOM' && metadata) {
    const toolCallResult = metadata.tool_call_result as Record<string, unknown> | undefined;
    if (toolCallResult && toolCallResult.data) {
      toolCallResult.data = '[omitted due to size]';
      processedMessage.content = JSON.stringify(toolCallResult);
    }
  }

  return processedMessage;
}

async function backupMessage(apiClient: APIClient, message: Message) {
  try {
    const conversation = await db.getConversation(message.conversation_id);
    await backupConversation(apiClient, conversation!);
    if (conversation?.type === 'miniapp') {
      const miniapp = await db.getMiniappByConversationId(conversation.id);
      await backupMiniApp(apiClient, miniapp!);
    }

    // preprocess message, omit large data for remote storage
    const processedMessage = preprocessMessageForRemote(message);
    await apiClient.saveMessageV2({ message: processedMessage });
  } catch (error) {
    console.log('failed to save message to server', JSON.stringify(error));
    message.status = 'warning';
    message.error = 'failed to sync with backend';
    await db.saveMessage(message);
  }
}

async function backupMiniApp(apiClient: APIClient, miniapp: MiniApp) {
  if (miniapp.sync_status !== 'local') {
    return;
  }

  try {
    await db.updateMiniapp(miniapp.id, { sync_status: 'syncing' });
    const conversation = await db.getConversation(miniapp.conversation_id);
    await backupConversation(apiClient, conversation!);
    await apiClient.saveMiniApp({ miniapp });
    await db.updateMiniapp(miniapp.id, { sync_status: 'remote' });
  } catch (error) {
    console.log('failed to save message to server', JSON.stringify(error));
    await db.updateMiniapp(miniapp.id, { sync_status: 'local' });
  }
}
