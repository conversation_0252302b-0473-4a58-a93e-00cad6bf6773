import { <PERSON><PERSON><PERSON><PERSON> } from '~/types';

import type { PlasmoCSConfig } from 'plasmo';
import { env } from '../configs/env';
import { API_KEY_TAG } from '~/configs/common';

export const config: PlasmoCSConfig = {
  matches: ['https://*.mysta.ai/*', 'http://localhost/*'],
  all_frames: true,
};

// Handle messages from web page
window.addEventListener('message', event => {
  if (event.source !== window) return;
  if (event.data && event.data.type === 'FROM_WEB_TO_EXTENSION') {
    const hostname = new URL(env.WEB_URL).hostname;

    // Handle API key setting (existing functionality)
    if (hostname === event.data.data?.host) {
      chrome.storage.local.set({
        [API_KEY_TAG]: {
          key: event.data.data.apiKey,
          enabled: event.data.data.apiKeyEnabled,
        } as <PERSON><PERSON><PERSON><PERSON>,
      });
      return;
    }

    // Handle script sync operations
    if (event.data.action && event.data.requestId) {
      // Forward message to background script
      chrome.runtime.sendMessage(
        {
          name: event.data.action,
          body: event.data.data,
          requestId: event.data.requestId,
        },
        response => {
          // Send response back to web page
          window.postMessage(
            {
              type: 'FROM_EXTENSION_TO_WEB',
              success: response?.success || false,
              data: response?.data,
              error: response?.error,
              requestId: event.data.requestId,
            },
            '*'
          );
        }
      );
    }
  }
});

// Listen for sync events from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // Only accept messages from our extension's background script
  if (sender.id !== chrome.runtime.id) {
    return;
  }

  // Forward sync events to web page
  if (message.type === 'script-sync-event') {
    window.postMessage(
      {
        type: 'FROM_EXTENSION_TO_WEB',
        syncEvent: message.event,
      },
      '*'
    );
  }

  sendResponse({ success: true });
});
