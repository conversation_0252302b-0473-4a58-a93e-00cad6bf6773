// TurndownService implementation - simplified version without external dependencies
export interface TurndownRule {
  filter: string | string[] | ((node: HTMLElement) => boolean);
  replacement: (content: string, node: HTMLElement) => string;
}

export class SimpleTurndownService {
  private rules: TurndownRule[] = [];

  constructor() {
    this.initializeDefaultRules();
  }

  private initializeDefaultRules() {
    // Remove script, style, and other non-content elements
    this.addRule({
      filter: ['script', 'style', 'noscript', 'meta', 'link', 'head'],
      replacement: () => '',
    });

    // Convert line breaks
    this.addRule({
      filter: 'br',
      replacement: () => '\n',
    });

    // Convert headings
    this.addRule({
      filter: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
      replacement: (content, node) => {
        const level = parseInt(node.tagName.charAt(1));
        return '\n' + '#'.repeat(level) + ' ' + content.trim() + '\n\n';
      },
    });

    // Convert paragraphs
    this.addRule({
      filter: 'p',
      replacement: content => '\n' + content.trim() + '\n\n',
    });

    // Convert links
    this.addRule({
      filter: 'a',
      replacement: (content, node) => {
        const href = (node as HTMLAnchorElement).href;
        return href && href !== '#' ? `[${content.trim()}](${href})` : content;
      },
    });

    // Convert strong/bold
    this.addRule({
      filter: ['strong', 'b'],
      replacement: content => `**${content.trim()}**`,
    });

    // Convert emphasis/italic
    this.addRule({
      filter: ['em', 'i'],
      replacement: content => `_${content.trim()}_`,
    });

    // Convert code
    this.addRule({
      filter: 'code',
      replacement: content => `\`${content.trim()}\``,
    });

    // Convert lists
    this.addRule({
      filter: 'ul',
      replacement: content => '\n' + content + '\n',
    });

    this.addRule({
      filter: 'ol',
      replacement: content => '\n' + content + '\n',
    });

    this.addRule({
      filter: 'li',
      replacement: (content, node) => {
        const parent = node.parentElement;
        if (parent?.tagName === 'OL') {
          const index = Array.from(parent.children).indexOf(node) + 1;
          return `${index}. ${content.trim()}\n`;
        } else {
          return `- ${content.trim()}\n`;
        }
      },
    });

    // Convert blockquotes
    this.addRule({
      filter: 'blockquote',
      replacement: content => {
        return (
          content
            .split('\n')
            .map(line => (line.trim() ? `> ${line.trim()}` : '>'))
            .join('\n') + '\n\n'
        );
      },
    });

    // Convert horizontal rules
    this.addRule({
      filter: 'hr',
      replacement: () => '\n---\n\n',
    });

    // Clean up navigation and UI elements
    this.addRule({
      filter: (node: HTMLElement) => {
        const className = node.className || '';
        const id = node.id || '';
        return (
          className.includes('nav') ||
          className.includes('menu') ||
          className.includes('sidebar') ||
          className.includes('header') ||
          className.includes('footer') ||
          id.includes('nav') ||
          id.includes('menu') ||
          id.includes('header') ||
          id.includes('footer')
        );
      },
      replacement: () => '',
    });
  }

  addRule(rule: TurndownRule) {
    this.rules.push(rule);
  }

  turndown(htmlString: string): string {
    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlString;
      return this.processNode(tempDiv);
    } catch (error) {
      throw new Error(
        `Turndown conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private processNode(node: Node): string {
    if (node.nodeType === Node.TEXT_NODE) {
      return node.textContent || '';
    }

    if (node.nodeType !== Node.ELEMENT_NODE) {
      return '';
    }

    const element = node as HTMLElement;
    const tagName = element.tagName.toLowerCase();

    // Apply rules
    for (const rule of this.rules) {
      let matches = false;

      if (typeof rule.filter === 'function') {
        matches = rule.filter(element);
      } else if (Array.isArray(rule.filter)) {
        matches = rule.filter.includes(tagName);
      } else {
        matches = rule.filter === tagName;
      }

      if (matches) {
        const content = Array.from(element.childNodes)
          .map(child => this.processNode(child))
          .join('');
        return rule.replacement(content, element);
      }
    }

    // Default processing for unmatched elements
    const content = Array.from(element.childNodes)
      .map(child => this.processNode(child))
      .join('');

    // For block elements, add spacing
    const blockElements = ['div', 'section', 'article', 'aside', 'main', 'header', 'footer', 'nav'];
    if (blockElements.includes(tagName)) {
      return content ? '\n' + content.trim() + '\n' : '';
    }

    return content;
  }
}
