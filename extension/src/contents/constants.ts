export type InteractionType = 'click' | 'input' | 'select' | 'submit' | 'navigate';

export const SKIP_TAGS = new Set(['svg', 'path', 'span', 'i']);

export const COMMON_TAGS = [
  'div',
  'span',
  'a',
  'p',
  'img',
  'ul',
  'li',
  'button',
  'input',
  'form',
  'section',
  'article',
  'header',
  'footer',
  'nav',
  'h1',
  'h2',
  'h3',
  'h4',
  'table',
  'tr',
  'td',
  'label',
  'select',
];

// Check for inherently interactive HTML elements
export const INTERACTIVE_ELEMENTS: Record<string, InteractionType> = {
  a: 'navigate',
  button: 'click',
  input: 'input',
  select: 'select',
  textarea: 'input',
  details: 'click',
  summary: 'click',
  option: 'select',
  dialog: 'click',
  audio: 'click',
  video: 'click',
  form: 'submit',
};

export const INTERACTIVE_ROLES: Record<string, InteractionType> = {
  button: 'click',
  link: 'navigate',
  checkbox: 'click',
  menuitem: 'click',
  menuitemcheckbox: 'click',
  menuitemradio: 'click',
  option: 'select',
  radio: 'click',
  slider: 'click',
  switch: 'click',
  tab: 'click',
  textbox: 'input',
  treeitem: 'click',
  combobox: 'select',
  searchbox: 'input',
};

export const INTERACTIVE_ATTRIBUTES: [string, InteractionType][] = [
  ['onclick', 'click'],
  ['onmousedown', 'click'],
  ['ontouchstart', 'click'],
  ['onkeydown', 'click'],
  ['onchange', 'input'],
  ['oninput', 'input'],
  ['contenteditable', 'input'],
];

export const INTERACTIVE_CLASS_PATTERNS: [RegExp, InteractionType][] = [
  [/\bbutton\b/i, 'click'],
  [/\bclick/i, 'click'],
  [/\blink\b/i, 'navigate'],
  [/\bicon\b/i, 'click'],
  [/\bmenu/i, 'click'],
  [/\btab\b/i, 'click'],
  [/\bclose\b/i, 'click'],
  [/\bopen\b/i, 'click'],
  [/\btoggle\b/i, 'click'],
  [/\bselect/i, 'select'],
  [/\bsearch/i, 'input'],
  [/\bsubmit\b/i, 'submit'],
  [/\bplay\b/i, 'click'],
  [/\bpause\b/i, 'click'],
  [/\bnext\b/i, 'click'],
  [/\bprev/i, 'click'],
  [/\bcard\b/i, 'click'],
  [/\bitem\b/i, 'click'],
];

export const INTERACTIVE_DATA_ATTRIBUTES: [string, InteractionType][] = [
  ['data-action', 'click'],
  ['data-click', 'click'],
  ['data-toggle', 'click'],
  ['data-target', 'click'],
  ['data-href', 'navigate'],
  ['data-url', 'navigate'],
  ['data-link', 'navigate'],
  ['data-tab', 'click'],
  ['data-menu', 'click'],
  ['data-dismiss', 'click'],
  ['data-close', 'click'],
  ['data-open', 'click'],
  ['data-submit', 'submit'],
  ['data-search', 'input'],
];

export const INTERACTIVE_ID_PATTERNS: [RegExp, InteractionType][] = [
  [/\bbutton\b/i, 'click'],
  [/\bclick/i, 'click'],
  [/\blink\b/i, 'navigate'],
  [/\bmenu/i, 'click'],
  [/\bsearch/i, 'input'],
  [/\bsubmit\b/i, 'submit'],
  [/\bclose\b/i, 'click'],
  [/\bopen\b/i, 'click'],
  [/\btoggle\b/i, 'click'],
];

export const POTENTIAL_INTERACTIVE_SELECTORS: string[] = [
  '[class*="button"]',
  '[class*="btn"]',
  '[class*="click"]',
  '[class*="link"]',
  '[class*="icon"]',
  '[class*="menu"]',
  '[class*="tab"]',
  '[class*="close"]',
  '[class*="open"]',
  '[class*="toggle"]',
  '[class*="select"]',
  '[class*="search"]',
  '[class*="submit"]',
  '[class*="card"]',
  '[class*="item"]',
  '[id*="button"]',
  '[id*="btn"]',
  '[id*="click"]',
  '[id*="link"]',
  '[id*="menu"]',
  '[id*="search"]',
  '[id*="submit"]',
  '[data-action]',
  '[data-click]',
  '[data-toggle]',
  '[data-target]',
  '[data-href]',
  '[data-url]',
];

export const INTERACTIVE_SELECTORS: Record<string, InteractionType> = {
  button: 'click',
  input: 'input',
  select: 'select',
  textarea: 'input',
  'a[href]': 'navigate',
  summary: 'click',
  details: 'click',
  '[role="button"]': 'click',
  '[role="link"]': 'navigate',
  '[role="checkbox"]': 'click',
  '[role="radio"]': 'click',
  '[role="menuitem"]': 'click',
  '[role="tab"]': 'click',
  '[role="switch"]': 'click',
  '[role="combobox"]': 'select',
  '[role="slider"]': 'click',
  '[role="searchbox"]': 'input',
  '[role="textbox"]': 'input',
  '[role="option"]': 'select',
  '[tabindex]:not([tabindex="-1"])': 'click',
  '[contenteditable="true"]': 'input',
  '[onclick]': 'click',
  '[onmousedown]': 'click',
  '[onkeydown]': 'click',
  '[onchange]': 'input',
  '[oninput]': 'input',
  '[style*="cursor: pointer"]': 'click',
  '[style*="cursor:pointer"]': 'click',
};
