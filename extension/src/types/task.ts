import { Agent, ChatOptions, Task, TaskNode, TaskStatus } from '@the-agent/shared';

export interface TaskWithStatus {
  task: Task;
  status: TaskStatus;
}

export interface GlobalContext {
  tasks: Record<string, TaskNode>;
  workflowId: string;
  processing: string[];
  processed: string[];
  chatOptions: ChatOptions;
  agents: {
    browser?: Agent;
    planner?: Agent;
    foreach?: Agent;
  };
  aborted: boolean;
}
