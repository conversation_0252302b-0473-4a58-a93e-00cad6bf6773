type Property = {
  type: string;
  items?: Property;
  description?: string;
  enum?: string[];
  properties?: PropertyMap;
};

type PropertyMap = Record<string, Property>;

export interface ToolDescription {
  name: string;
  description: string;
  parameters?: {
    type: string;
    properties: PropertyMap;
    required?: string[];
  };
  returns?: {
    type: string;
    description: string;
    properties?: PropertyMap;
  };
}

export interface WebContextInput {
  tabId: number;
  pageId: number;
}

export interface WebContext extends WebContextInput {
  title: string;
  url: string;
  sourceTabId?: number;
  createdAt: number;
}

export interface PageChangeEvent {
  type: 'pageChange';
  before: number;
  after: number;
}

export type ChangeType = 'added' | 'removed' | 'text' | 'attr' | 'mixed';

export interface CompactChangeItem {
  type: ChangeType;
  selector: string;
  role?: string;
  hint?: string; // short human/LLM-readable line
}

export interface DomChangeEvent {
  type: 'domChange';
  summary?: {
    added: number;
    removed: number;
    textChanged: number;
    attrChanged: number;
  };
  highlights?: CompactChangeItem[];
}

export interface TabClosedEvent {
  type: 'tabClosed';
  tabId: number;
}

export interface TabChangeEvent {
  type: 'tabChange';
  before?: number | null;
  after?: number | null;
}

export type DeltaEvent = TabClosedEvent | TabChangeEvent | PageChangeEvent | DomChangeEvent;

export interface ActionEcho {
  tool: string;
  args: object;
}

export type WebInteractionResult<T> = {
  success: boolean;
  context?: WebContext | null;
  delta?: DeltaEvent[];
  data?: T;
  action?: ActionEcho;
  error?: string;
};

export type WebToolkitArgumentsBase = {
  context?: WebContextInput | null;
};

export type TabToolKitArguments =
  | (WebToolkitArgumentsBase & {
      url: string;
    })
  | (WebToolkitArgumentsBase & {
      tabId: number;
    });

export type WebToolKitArguments =
  | (WebToolkitArgumentsBase & {
      selectorOrIndex: string;
    })
  | (WebToolkitArgumentsBase & {
      selectorOrIndex: string;
      value: string;
      clearFirst?: boolean;
      pressEnterAfterInput?: boolean;
    })
  | (WebToolkitArgumentsBase & {
      keys: string;
    })
  | (WebToolkitArgumentsBase & {
      selectorOrIndex?: string;
      generateDiff?: boolean;
    });

export interface DebuggerState {
  attached: boolean;
  tabId: number | null;
  targetId: string | null;
}

export interface ScreenshotResult {
  url: string;
}
