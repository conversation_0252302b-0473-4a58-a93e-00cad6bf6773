import { db } from '~/storages/indexdb';
import { MiniApp, Developing } from '@the-agent/shared';
import { deleteConversation } from './conversation';
import { createApiClient } from './api/client';

export const getMiniapp = async (id: number): Promise<MiniApp> => {
  const miniapp = await db.getMiniapp(id);
  if (!miniapp) {
    throw new Error('MiniApp not found');
  }
  return miniapp;
};

export const deleteMiniapp = async (id: number): Promise<void> => {
  // Find miniapp first to locate its conversation
  const miniapp = await db.getMiniapp(id);
  if (!miniapp) return;

  // Delete associated conversation and messages using the conversationId
  if (miniapp.conversation_id != null) {
    await deleteConversation(miniapp.conversation_id);
  }

  // Remove the miniapp record itself
  await db.apps.delete(id);
};

export const updateMiniapp = async (
  id: number,
  update: Partial<Pick<MiniApp, 'name' | 'installation' | 'status'>>
): Promise<void> => {
  const client = await createApiClient();
  await client.updateMiniApp({ id, ...update });
  await db.updateMiniapp(id, update);
};

// Update miniapp developing field and trigger sync
export const updateMiniappDeveloping = async (
  id: number,
  developing: Developing
): Promise<void> => {
  await db.updateMiniapp(id, { developing });

  // Send sync message to web via background script
  try {
    await chrome.runtime.sendMessage({
      name: 'trigger-script-sync',
      body: {
        type: 'script-updated',
        miniappId: id,
        data: {
          code: developing.code,
          version: developing.version,
          updated_at: developing.updated_at,
        },
      },
    });
  } catch (error) {
    console.warn('Failed to trigger script sync:', error);
  }
};
