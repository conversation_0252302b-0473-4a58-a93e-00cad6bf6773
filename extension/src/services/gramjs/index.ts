import { Api, TelegramClient } from '@the-agent/telegram';
import { StringSession } from '@the-agent/telegram/browser/sessions';
import { NewMessage } from '@the-agent/telegram/browser/events';

let client: TelegramClient | null = null;
let botClient: TelegramClient | null = null;

// Track the current message handler to ensure only one exists at a time
let currentMessageHandler: any = null;
let currentMessageCallback:
  | ((message: { text: string; from: string; timestamp: number; chatId: string }) => void)
  | null = null;

export const getTelegramApiCredentials = (): { apiId: number; apiHash: string } => {
  return {
    apiId: Number(process.env.PLASMO_PUBLIC_TELEGRAM_API_ID || ''),
    apiHash: process.env.PLASMO_PUBLIC_TELEGRAM_API_HASH || '',
  };
};

export const getTelegramClient = (): TelegramClient => {
  if (client) {
    return client;
  }
  const SESSION = new StringSession(JSON.parse(localStorage.getItem('session') as string));
  const { apiId, apiHash } = getTelegramApiCredentials();
  client = new TelegramClient(SESSION, apiId, apiHash, { connectionRetries: 5 });
  return client;
};

export const logoutTelegramClient = async () => {
  if (client) {
    try {
      if (client.connected) {
        await client.invoke(new Api.auth.LogOut());
        await client.disconnect();
      }
      await client.destroy();
      client = null;
      localStorage.removeItem('session');
    } catch (err) {
      console.error('=========logoutTelegramClient err========', err);
    }
  }
};

export const getTelegramBotClient = (): TelegramClient => {
  if (botClient) {
    return botClient;
  }
  const SESSION = new StringSession('');
  const { apiId, apiHash } = getTelegramApiCredentials();
  botClient = new TelegramClient(SESSION, apiId, apiHash, { connectionRetries: 5 });
  return botClient;
};

export const addMessageListener = (
  callback: (message: { text: string; from: string; timestamp: number; chatId: string }) => void,
  onErrorCallback: (error: string) => void,
  targetGroupChatId?: string,
  targetUserId?: string
) => {
  if (!botClient) {
    console.error('Bot client not initialized');
    return;
  }

  // Remove any existing message handler first
  if (currentMessageHandler) {
    removeMessageListener(currentMessageHandler);
  }

  const messageHandler = async (event: any) => {
    try {
      if (event.type === 'error') {
        onErrorCallback(event.error);
        return;
      }
      const message = event.message;
      if (!message) return;

      // Get chat information
      const chatId = message.chatId?.toString() || message.peerId?.chatId?.toString() || '';

      // Filter by target group if specified
      if (targetGroupChatId && chatId !== targetGroupChatId) {
        return;
      }

      if (targetUserId) {
        const peerId = message.peerId?.userId?.toString();
        if (peerId !== targetUserId) {
          return;
        }
      }

      // Get sender information
      let senderName = 'Unknown';
      try {
        const sender = await message.getSender();
        if (sender) {
          senderName = sender.firstName || sender.username || `User ${sender.id}`;
        }
      } catch (error) {
        console.error('Could not get sender info:', error);
        // Fallback to message sender ID
        senderName = `User ${message.senderId || 'Unknown'}`;
      }

      // Extract message text
      const messageText = message.text || message.message || '';

      if (messageText) {
        callback({
          text: messageText,
          from: senderName,
          timestamp: message.date ? message.date * 1000 : Date.now(),
          chatId: chatId,
        });
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  };

  // Store the current handler and callback
  currentMessageHandler = messageHandler;
  currentMessageCallback = callback;

  // Add event handler for incoming messages
  botClient.addEventHandler(messageHandler, new NewMessage({ incoming: true }));

  return messageHandler;
};

export const removeMessageListener = (messageHandler?: any) => {
  if (!botClient) {
    return;
  }

  // If no specific handler is provided, remove the current one
  const handlerToRemove = messageHandler || currentMessageHandler;

  if (!handlerToRemove) {
    return;
  }

  try {
    botClient.removeEventHandler(handlerToRemove, new NewMessage({ incoming: true }));

    // Clear the current handler tracking if it's the one being removed
    if (handlerToRemove === currentMessageHandler) {
      currentMessageHandler = null;
      currentMessageCallback = null;
    }
  } catch (error) {
    console.error('Error removing message listener:', error);
  }
};

// Add a function to get the current message handler status
export const getCurrentMessageHandler = () => {
  return {
    hasHandler: !!currentMessageHandler,
    callback: currentMessageCallback,
  };
};

export const getBotStatus = async (): Promise<{
  isPolling: boolean;
  botUsername: string;
} | null> => {
  if (!botClient) {
    return null;
  }

  try {
    const isConnected = botClient.connected || false;
    let botUsername = 'Unknown Bot';

    if (isConnected) {
      try {
        const me = await botClient.getMe();
        if (me && 'username' in me) {
          botUsername = (me as any).username || (me as any).firstName || 'Bot';
          return {
            isPolling: true,
            botUsername: botUsername,
          };
        }
      } catch (error) {
        console.error('Could not get bot info:', error);
        return null;
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting bot status:', error);
    return null;
  }
};

export const logoutTelegramBotClient = async () => {
  if (botClient) {
    try {
      if (botClient.connected) {
        await botClient.invoke(new Api.auth.LogOut());
        await botClient.disconnect();
      }
      await botClient.destroy();
      botClient = null;
      localStorage.removeItem('session');
    } catch (err) {
      console.error('=========logoutTelegramBotClient err========', err);
    }
  }
};

export { getInputHandlers } from './authController';
