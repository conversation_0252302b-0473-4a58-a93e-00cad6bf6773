import { Buff<PERSON> } from 'buffer/';
import { QrCodeData, useTelegramStore } from '~/stores/telegramStore';
import { isTelegramAuthError } from '~/utils/telegram';

interface AuthInputHandlers {
  phoneNumber: () => Promise<string>;
  password: (hint?: string) => Promise<string>;
  phoneCode: () => Promise<string>;
  qrCode: (qrCode: { token: Buffer; expires: number }) => Promise<void>;
  onError: (error: any) => void;
}

class AuthController {
  private phoneNumberPromise: Promise<string> | null = null;
  private passwordPromise: Promise<string> | null = null;
  private phoneCodePromise: Promise<string> | null = null;
  private qrCodePromise: Promise<string> | null = null;
  private phoneNumberResolve: ((value: string) => void) | null = null;
  private passwordResolve: ((value: string) => void) | null = null;
  private phoneCodeResolve: ((value: string) => void) | null = null;
  private qrCodeResolve: ((value: string) => void) | null = null;

  // Method to request phone number input
  requestPhoneNumber(): Promise<string> {
    if (!this.phoneNumberPromise) {
      this.phoneNumberPromise = new Promise<string>(resolve => {
        this.phoneNumberResolve = resolve;
      });
    }
    return this.phoneNumberPromise;
  }

  // Method to provide phone number from UI component
  providePhoneNumber(phoneNumber: string): void {
    if (this.phoneNumberResolve) {
      this.phoneNumberResolve(phoneNumber);
      this.phoneNumberPromise = null;
      this.phoneNumberResolve = null;
    }
  }

  // Method to request password input
  requestPassword(hint?: string): Promise<string> {
    useTelegramStore.getState().updateAuthState('authorizationStateWaitPassword');
    useTelegramStore.getState().setHint(hint || null);

    if (!this.passwordPromise) {
      this.passwordPromise = new Promise<string>(resolve => {
        this.passwordResolve = resolve;
      });
    }
    return this.passwordPromise;
  }

  // Method to provide password from UI component
  providePassword(password: string): void {
    if (this.passwordResolve) {
      this.passwordResolve(password);
      this.passwordPromise = null;
      this.passwordResolve = null;
    }
  }

  // Method to request phone code input
  requestPhoneCode(): Promise<string> {
    if (!this.phoneCodePromise) {
      this.phoneCodePromise = new Promise<string>(resolve => {
        this.phoneCodeResolve = resolve;
      });
    }
    return this.phoneCodePromise;
  }

  // Method to provide phone code from UI component
  providePhoneCode(code: string): void {
    if (this.phoneCodeResolve) {
      this.phoneCodeResolve(code);
      this.phoneCodePromise = null;
      this.phoneCodeResolve = null;
    }
  }

  // Method to request QR code input
  requestQrCode(qrCode: { token: Buffer; expires: number }): Promise<void> {
    useTelegramStore.getState().updateAuthState('authorizationStateWaitQrCode');

    const qrCodeData: QrCodeData = {
      token: qrCode.token.toString('base64'),
      expires: qrCode.expires,
    };
    useTelegramStore.getState().setQrCodeData(qrCodeData);

    if (!this.qrCodePromise) {
      this.qrCodePromise = new Promise<string>(resolve => {
        this.qrCodeResolve = resolve;
      });
    }
    return Promise.resolve();
  }

  // Method to provide QR code from UI component
  provideQrCode(code: string): void {
    if (this.qrCodeResolve) {
      this.qrCodeResolve(code);
      this.qrCodePromise = null;
      this.qrCodeResolve = null;
    }
  }

  // Get input handlers for telegramClient.start
  getInputHandlers(): AuthInputHandlers {
    return {
      phoneNumber: () => this.requestPhoneNumber(),
      password: (hint?: string) => this.requestPassword(hint),
      phoneCode: () => this.requestPhoneCode(),
      qrCode: (qrCode: { token: Buffer; expires: number }) => this.requestQrCode(qrCode),
      onError: (error: any) => {
        console.error('Telegram auth error:', error);
        if (isTelegramAuthError(error)) {
          useTelegramStore.getState().logoutTelegram();
          useTelegramStore.getState().signInTelegram();
        } else {
          useTelegramStore.getState().setError(error?.message || 'Authentication error');
        }
      },
    };
  }

  // Reset all pending promises
  reset(): void {
    this.phoneNumberPromise = null;
    this.passwordPromise = null;
    this.phoneCodePromise = null;
    this.qrCodePromise = null;
    this.phoneNumberResolve = null;
    this.passwordResolve = null;
    this.phoneCodeResolve = null;
    this.qrCodeResolve = null;
  }
}

// Create singleton instance
export const authController = new AuthController();

// Export convenience functions
export const requestPhoneNumber = () => authController.requestPhoneNumber();
export const providePhoneNumber = (phoneNumber: string) =>
  authController.providePhoneNumber(phoneNumber);
export const requestPassword = () => authController.requestPassword();
export const providePassword = (password: string) => authController.providePassword(password);
export const requestPhoneCode = () => authController.requestPhoneCode();
export const providePhoneCode = (code: string) => authController.providePhoneCode(code);
export const requestQrCode = (qrCode: { token: Buffer; expires: number }) =>
  authController.requestQrCode(qrCode);
export const provideQrCode = (code: string) => authController.provideQrCode(code);
export const getInputHandlers = () => authController.getInputHandlers();
export const resetAuthController = () => authController.reset();
