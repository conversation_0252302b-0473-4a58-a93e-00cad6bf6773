import { APIClient } from '@the-agent/shared';
import { env } from '../../configs/env';
import { db } from '~/storages/indexdb';

/**
 * Create API client instance with authentication
 */
export const createApiClient = async (apiKey?: string): Promise<APIClient> => {
  if (!apiKey) {
    const activeUser = await db.getCurrentUser();
    if (!activeUser) {
      throw new Error('Authentication required');
    }
    apiKey = activeUser.api_key;
  }
  return new APIClient({
    baseUrl: env.BACKEND_URL,
    apiKey,
  });
};
