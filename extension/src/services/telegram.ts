// Direct Telegram Bot API implementation for browser extensions

interface TelegramUpdate {
  update_id: number;
  message?: TelegramMessage;
  channel_post?: TelegramMessage;
  edited_message?: TelegramMessage;
}

interface TelegramMessage {
  message_id: number;
  from?: TelegramUser;
  chat: TelegramChat;
  text?: string;
  date: number;
}

interface TelegramUser {
  id: number;
  username?: string;
  first_name: string;
  last_name?: string;
}

interface TelegramChat {
  id: number;
  type: string;
  title?: string;
  username?: string;
}

interface TelegramResponse<T> {
  ok: boolean;
  result: T;
  description?: string;
}

export class TelegramBotClient {
  private botToken: string;
  private lastUpdateId: number = 0;
  private isPolling: boolean = false;
  private pollInterval: number = 1000; // 1 second

  constructor(botToken: string) {
    this.botToken = botToken;
  }

  // Send a message to a chat
  async sendMessage(chatId: string, text: string): Promise<TelegramResponse<TelegramMessage>> {
    const response = await fetch(`https://api.telegram.org/bot${this.botToken}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: text,
        parse_mode: 'HTML', // or 'Markdown'
      }),
    });

    return response.json();
  }

  // Get updates (messages) from Telegram
  async getUpdates(offset?: number, timeout?: number): Promise<TelegramResponse<TelegramUpdate[]>> {
    const response = await fetch(`https://api.telegram.org/bot${this.botToken}/getUpdates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        offset: offset || this.lastUpdateId + 1,
        timeout: timeout || 30,
        allowed_updates: ['message', 'channel_post', 'edited_message'],
      }),
    });

    return response.json();
  }

  // Get updates for a specific chat
  async getUpdatesForChat(
    chatId: string,
    offset?: number,
    timeout?: number
  ): Promise<TelegramResponse<TelegramUpdate[]>> {
    const response = await fetch(`https://api.telegram.org/bot${this.botToken}/getUpdates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        offset: offset || this.lastUpdateId + 1,
        timeout: timeout || 30,
        allowed_updates: ['message', 'channel_post', 'edited_message'],
      }),
    });

    const result = await response.json();

    // Filter updates for the specific chat
    if (result.ok && result.result) {
      result.result = result.result.filter((update: TelegramUpdate) => {
        const message = update.message || update.channel_post || update.edited_message;
        return message && message.chat.id.toString() === chatId;
      });
    }

    return result;
  }

  // Start polling for messages
  startPolling(onMessage: (message: TelegramMessage) => void, onError?: (error: Error) => void) {
    if (this.isPolling) {
      console.warn('Already polling for messages');
      return;
    }

    this.isPolling = true;
    this.pollMessages(onMessage, onError);
  }

  // Stop polling for messages
  stopPolling() {
    this.isPolling = false;
  }

  private async pollMessages(
    onMessage: (message: TelegramMessage) => void,
    onError?: (error: Error) => void
  ) {
    while (this.isPolling) {
      try {
        const response = await this.getUpdates();

        if (response.ok && response.result.length > 0) {
          for (const update of response.result) {
            this.lastUpdateId = update.update_id;

            // Handle different types of messages
            if (update.message) {
              onMessage(update.message);
            } else if (update.channel_post) {
              onMessage(update.channel_post);
            } else if (update.edited_message) {
              onMessage(update.edited_message);
            }
          }
        }
      } catch (error) {
        console.error('Error polling Telegram messages:', error);
        onError?.(error as Error);
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, this.pollInterval));
    }
  }

  // Get bot information
  async getMe(): Promise<TelegramResponse<TelegramUser>> {
    const response = await fetch(`https://api.telegram.org/bot${this.botToken}/getMe`);
    return response.json();
  }

  // Set webhook (for reference, but not usable in extensions)
  async setWebhook(url: string): Promise<TelegramResponse<boolean>> {
    const response = await fetch(`https://api.telegram.org/bot${this.botToken}/setWebhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: url,
      }),
    });

    return response.json();
  }
}

// Initialize Telegram bot via background script
export async function initTelegramBot(botToken: string, groupChatId: string) {
  try {
    // Send message to background script to initialize Telegram bot
    const response = await new Promise<{ success: boolean; message?: string; error?: string }>(
      resolve => {
        chrome.runtime.sendMessage(
          {
            name: 'init-telegram-bot',
            body: { botToken, groupChatId },
          },
          resolve
        );
      }
    );

    if (response.success) {
      console.info('Telegram bot initialized successfully:', response.message);
      return response;
    } else {
      throw new Error(response.error || 'Failed to initialize Telegram bot');
    }
  } catch (error) {
    console.error('Failed to initialize Telegram bot:', error);
    throw error;
  }
}

// Stop Telegram bot polling
export async function stopTelegramBot() {
  try {
    const response = await new Promise<{ success: boolean; message?: string; error?: string }>(
      resolve => {
        chrome.runtime.sendMessage(
          {
            name: 'stop-telegram-bot',
          },
          resolve
        );
      }
    );

    if (response.success) {
      console.info('Telegram bot stopped successfully:', response.message);
      return response;
    } else {
      throw new Error(response.error || 'Failed to stop Telegram bot');
    }
  } catch (error) {
    console.error('Failed to stop Telegram bot:', error);
    throw error;
  }
}

// Get Telegram bot status
export async function getTelegramBotStatus() {
  try {
    const response = await new Promise<{
      success: boolean;
      data?: {
        isPolling: boolean;
        targetGroupChatId: string | null;
        botUsername: string;
      };
      error?: string;
    }>(resolve => {
      chrome.runtime.sendMessage(
        {
          name: 'get-telegram-status',
        },
        resolve
      );
    });

    if (response.success) {
      return response;
    } else {
      throw new Error(response.error || 'Failed to get Telegram bot status');
    }
  } catch (error) {
    console.error('Failed to get Telegram bot status:', error);
    throw error;
  }
}
