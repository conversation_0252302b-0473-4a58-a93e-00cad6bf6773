import 'dotenv/config';
import OpenAI from 'openai';

export const openai = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: process.env.OPENROUTER_API_URL,
});

export const MODEL_GERMINI_PRO = 'google/gemini-2.5-pro';
export const MODEL_GERMINI_FLASH = 'google/gemini-2.5-flash';

export type MessageInput = OpenAI.Chat.ChatCompletionMessageParam;
export type MessageResponse = OpenAI.Chat.Completions.ChatCompletionMessage;
