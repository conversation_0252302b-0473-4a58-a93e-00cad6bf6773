import 'dotenv/config';

import { ChatAgent, ChatOptions, Message, TaskNode, ToolCallResult } from '@the-agent/shared';
import { openai, MODEL_GERMINI_PRO } from './common';

import { createForeachAgent } from '~/agents/foreach';
import { createPlannerAgent } from '~/agents/planner';

import { TaskContextBuilder } from '~/agents/context';
import { taskTools } from '~/tools/task-toolkit';
import { DEFAULT_MAX_TOOL_CALLS } from '~/configs/common';
import { TaskToolExecutor } from '~/tools/task-executor';
import { ToolDescription } from '~/types';
import { GlobalContext } from '~/types/task';
import { MystaTaskNode } from '~/agents/node';

const root: TaskDefinition = {
  id: 'root',
  goal: `Given following twitter names, please summary their recent activities in twitter:
    "Vitalik Buterin",
    "Gavin Wood",
    "Naval Ravikant"`,
  atomic: false,
  subtasks: [],
};

class DummyToolExecutor extends TaskToolExecutor {
  constructor(c: GlobalContext) {
    super(c);
  }

  async executeInternal(_task: TaskNode, toolName: string, _params: any): Promise<ToolCallResult> {
    throw new Error(`Unsupported tool call ${toolName}`);
  }

  getToolDescriptions(): ToolDescription[] {
    return taskTools;
  }

  getPostToolcallMessageInternal(): string {
    return '';
  }
}

const prettyPrint = (content: string) => {
  try {
    const json = JSON.parse(content);
    return JSON.stringify(json, null, 2);
  } catch (e) {
    // if (content.length > 50) {
    //   return content.slice(0, 20) + '...' + content.slice(-20);
    // }
    return content;
  }
};

const chatOptions: ChatOptions = {
  conversationId: 1,
  onMessageUpdate: async () => {},
  onMessageComplete: async (message: Message, printTokenUsage: boolean = false) => {
    const prefix = `[Task: ${message.task_id}][Agent: ${message.agent_id}]`;
    if (message.role === 'user') {
      console.log(`\n${prefix}[User]: ${prettyPrint(message.content)}`);
    } else if (message.role === 'assistant') {
      console.log(`\n${prefix}[Assistant]: ${prettyPrint(message.content)}`);
      if (printTokenUsage) {
        console.log(
          `\n${prefix}[Assistant] (usage): ${JSON.stringify(message.token_usage, null, 2)}`
        );
      }
      if (message.tool_calls && message.tool_calls.length > 0) {
        for (const tool of message.tool_calls) {
          const t = JSON.parse(JSON.stringify(tool));
          const args = t.function.arguments;
          if (args && typeof args === 'string') {
            try {
              t.function.arguments = JSON.parse(args);
            } catch (e) {
              console.error(`Failed to parse tool call arguments: ${args}`);
            }
            const tasks = t.function.arguments.tasks;
            if (tasks && typeof tasks === 'string') {
              try {
                t.function.arguments.tasks = JSON.parse(tasks);
              } catch (e) {
                console.error(`Failed to parse tool call arguments: ${tasks}`);
              }
            }
          }
          console.log(`\n${prefix}[Assistant] (tool): ${prettyPrint(JSON.stringify(t, null, 2))}`);
        }
      }
    } else if (message.role === 'tool') {
      console.log(`\n${prefix}[Tool] (content): ${prettyPrint(message.content)}`);
    }
  },
};

const context: GlobalContext = {
  chatOptions,
  processing: [],
  processed: [],
  agents: {},
  workflowId: 'workflow_' + Date.now(),
  tasks: {},
  aborted: false,
};

const FAKE_AGENT_SYSTEM_PROMPT = `
You are an chat bot pretending to be a browser controller.
If user asks for something, you simply return some faked data to them and pretend you have done the task.

You will be provided with the following information:
- **Task history memory**: A summary of what has been done so far.
- **Site-specific Q&A Knowledge**: Relevant knowledge base for the current action, formatted as Q: question / A: answer pairs.
- **Current URL**: The URL of the active browser tab.

--

## 📥 Task Result Finalization

- You must call \`TaskToolkit_finishTask\` to save the final output of the task.

### ✅ Stopping Rule:

After calling \`TaskToolkit_finishTask\`, you must:
- **Stop execution immediately**
- Do **not** call any further tools
- Do **not** print or summarize the output, simply respond with "Task completed." or "Task failed."

You must NEVER call \`TaskToolkit_finishTask\` more than once for the same task. Doing so is an error.
-- 

## ❌ Common Mistakes to Avoid

- Do not call any tools after a successful result has been stored
- Do not print the output again after calling \`TaskToolkit_finishTask\`
`;

const fakeAgent = new ChatAgent({
  id: 'fake',
  llmClient: openai,
  model: MODEL_GERMINI_PRO,
  systemPrompt: FAKE_AGENT_SYSTEM_PROMPT,
  contextBuilder: new TaskContextBuilder(),
  maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
  toolExecutor: new DummyToolExecutor(context),
});

async function main() {
  context.agents.planner = createPlannerAgent(MODEL_GERMINI_PRO, openai, context);
  context.agents.foreach = createForeachAgent(MODEL_GERMINI_PRO, openai, context);
  context.agents.browser = fakeAgent;

  const node = new MystaTaskNode(context, root, { atomic: false });
  await node.run();

  console.log(`Task ${node.id}: `);
  console.log(`    Goal: ${node.task.goal}`);
  console.log(`    Result: ${JSON.stringify(node.result)}`);
}

main().then(() => {
  console.log('Done');
});
