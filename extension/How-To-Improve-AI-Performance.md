# How To Improve AI Performance

## Overview

Web Toolkit provides an enhanced web page analysis system designed specifically for AI agents to understand and interact with web pages efficiently. It consists of two main components: **DOM Analyzer** for deep page analysis and **AI Page Understanding Tools** for practical agent interaction.

## Key Features

### 🎯 Core Functionality

- **Semantic Segmentation**: Automatically identifies semantic HTML5 sections (header, nav, main, footer, etc.)
- **Interactive Element Detection**: Finds all clickable, interactive elements on a page
- **Highlight Index System**: Assigns unique indices to interactive elements for AI reference
- **Element Mapping**: Creates bidirectional mapping between indices and DOM elements
- **AI-Friendly Format**: Converts elements to structured format optimized for AI processing
- **Reliable Element Interaction**: Index-based clicking instead of fragile CSS selectors

### 🔍 Element Detection Capabilities

- Native HTML elements: `button`, `input`, `select`, `textarea`, `a[href]`
- ARIA roles: `button`, `link`, `checkbox`, `radio`, `menuitem`, `tab`, etc.
- Interactive attributes: `[tabindex]`, `[onclick]`, `[contenteditable]`
- Visual cues: `cursor: pointer` style detection
- Form relationships: label associations, form context
- Accessibility states: disabled, hidden, required, checked

## API Reference

### Tools Overview

#### 1. WebToolkit_analyzePage

Analyzes the current page to extract actionable parts for AI agent understanding.

**Usage:**

```javascript
const result = await analyzePage();
```

**Returns:**

```typescript
{
  success: boolean;
  data?: {
    segments: SemanticSegment[];
    highlightedElements: HighlightedElement[];
    totalElements: number;
    clickableElementsString: string;    // AI-friendly format: [index]<tag>text</tag>
  };
  error?: string;
}
```

#### 2. WebToolkit_clickElementByIndex

Clicks an element using its highlight index from analyzePage results.

**Usage:**

```javascript
const clickResult = await clickElementByIndex({ highlightIndex: 5 });
```

**Returns:**

```typescript
{
  success: boolean;
  data?: {
    clicked: boolean;
    elementFound: boolean;
    elementStillExists: boolean;
    elementInfo?: {
      tagName: string;
      textContent?: string;
      attributes: Record<string, string>;
    };
  };
  error?: string;
}
```

### Core Interfaces

#### `HighlightedElement`

```typescript
interface HighlightedElement {
  highlight_index: number;
  tagName: string;
  type: string;
  interactionType: 'click' | 'input' | 'select' | 'submit' | 'navigate';
  selector: string;
  xpath: string;
  textContent?: string;
  value?: string;
  placeholder?: string;
  ariaLabel?: string;
  role?: string;
  disabled?: boolean;
  hidden?: boolean;
  required?: boolean;
  checked?: boolean;
  formId?: string;
  labelText?: string;
  associatedLabels?: string[];
  attributes: Record<string, string>;
}
```

#### `SemanticSegment`

```typescript
interface SemanticSegment {
  type: 'header' | 'nav' | 'main' | 'section' | 'article' | 'aside' | 'footer' | 'container';
  selector: string;
  element: Element;
  highlightedElements: HighlightedElement[];
  textContent?: string;
  depth: number;
}
```

### Direct API (DOMAnalyzerV2)

For advanced usage, the underlying DOMAnalyzerV2 class is available:

```typescript
import { DOMAnalyzerV2, analyzeDOMV2, getClickableElementsString } from './tools/dom-analyzer';

// Method 1: Using the class directly
const analyzer = new DOMAnalyzerV2(htmlContent);
const result = analyzer.analyze();

// Method 2: Using factory function
const result = analyzeDOMV2(htmlContent);

// Method 3: Get just the string representation
const clickableString = getClickableElementsString(htmlContent);
```

## Usage Examples

### Basic Page Analysis

```javascript
// Analyze the current page
const analysis = await analyzePage();

if (analysis.success) {
  console.log(`Found ${analysis.data.totalElements} interactive elements`);
  console.log(`Page has ${analysis.data.segments.length} semantic segments`);

  // AI-friendly string representation
  console.log(analysis.data.clickableElementsString);
  // Output:
  // [0]<a href="/home">Home</a>
  // [1]<button type="submit">Login</button>
  // [2]<input type="text" placeholder="Username">Username</input>
}
```

### Finding and Clicking Elements

```javascript
// 1. Analyze page first
const analysis = await analyzePage();

// 2. Find submit button
const submitButton = analysis.data.highlightedElements.find(
  element => element.type.includes('submit') || element.textContent?.includes('Submit')
);

if (submitButton) {
  // 3. Click by index
  const clickResult = await clickElementByIndex({
    highlightIndex: submitButton.highlight_index,
  });

  if (clickResult.success) {
    console.log('Successfully clicked submit button');
  } else {
    console.error('Click failed:', clickResult.error);
  }
}
```

### Form Interaction Workflow

```javascript
// Analyze page to understand form structure
const analysis = await analyzePage();

// Find form elements
const formElements = analysis.data.highlightedElements.filter(
  element => element.formId === 'loginForm'
);

// Find username input and submit button
const usernameInput = formElements.find(
  element => element.interactionType === 'input' && element.placeholder?.includes('username')
);
const submitButton = formElements.find(element => element.interactionType === 'submit');

// Fill form and submit
if (usernameInput && submitButton) {
  // Use traditional inputElement for text input
  await inputElement({
    selector: usernameInput.selector,
    value: 'myusername',
  });

  // Use index-based clicking for submission
  await clickElementByIndex({
    highlightIndex: submitButton.highlight_index,
  });
}
```

## String Format Specification

The AI-friendly string format follows this pattern:

```
[index]<tagName attributes>displayText</tagName>
```

### Examples

- `[0]<a href="/home">Home</a>`
- `[1]<button type="submit" aria-label="Submit form">Login</button>`
- `[2]<input type="text" placeholder="Enter username" required>Enter username</input>`
- `[3]<select>Category Selection</select>`
- `[4]<div role="button">Custom Button</div>`

### Attribute Priority

1. **aria-label** - Highest priority for accessibility
2. **labelText** - Associated form labels
3. **placeholder** - Input placeholders
4. **textContent** - Visible text content
5. **value** - Input values
6. **title** - Title attributes
7. **alt** - Alt text for images

## Performance Comparison

### analyzePage vs listElements

Performance testing shows the following comparison:

| Metric             | listElements | analyzePage      | Ratio              |
| ------------------ | ------------ | ---------------- | ------------------ |
| **Execution Time** | ~0.84ms      | ~24.57ms         | 29x slower         |
| **Element Count**  | Variable     | Higher detection | +12% more elements |
| **Output Size**    | ~15.9KB      | ~3.0KB           | 5x smaller         |
| **AI Readability** | 40/100       | 75/100           | +35 points better  |

### Key Performance Benefits

- **Significantly Smaller Output**: 5x reduction in data size
- **Better AI Readability**: 35-point improvement in readability score
- **More Elements Detected**: 12% more interactive elements found
- **Trade-off**: 29x slower execution, but still under 25ms

### When to Use Which

**Use `analyzePage` when:**

- AI agent needs to understand page structure
- Working with complex forms or multi-section pages
- Need reliable element identification across page changes
- Output size and AI readability are priorities

**Use `listElements` when:**

- Need fastest possible execution
- Working with simple, static pages
- Don't need semantic page understanding

## AI Agent Integration

### Prompt Template

```
You are helping a user interact with a webpage. First, analyze the page structure:

ANALYZE_PAGE_COMMAND

Based on the analysis results, you can:
1. Understand the page structure from segments
2. See all interactive elements with their indices
3. Use the clickableElementsString for quick element overview

To click an element, use its highlight_index:
CLICK_ELEMENT_BY_INDEX_COMMAND: {highlightIndex: 5}

Available elements:
{clickableElementsString}
```

## Best Practices

### 1. Always Analyze First

```javascript
// ❌ Don't click without analysis
await clickElementByIndex({ highlightIndex: 5 }); // May fail

// ✅ Analyze first, then click
const analysis = await analyzePage();
const targetElement = analysis.data.highlightedElements.find(/* criteria */);
await clickElementByIndex({ highlightIndex: targetElement.highlight_index });
```

### 2. Check Element States

```javascript
const element = analysis.data.highlightedElements[index];
if (element.disabled) {
  console.log('Element is disabled, cannot click');
  return;
}
if (element.hidden) {
  console.log('Element is hidden, may not be clickable');
  return;
}
```

### 3. Handle Dynamic Content

```javascript
// Re-analyze if page content changes significantly
if (clickResult.data?.elementStillExists === false) {
  console.log('Page content changed, re-analyzing...');
  const newAnalysis = await analyzePage();
  // Find element again in new analysis
}
```

## Advantages Over Traditional Methods

### vs. CSS Selectors

- **Reliability**: Elements tracked by analysis, not fragile selectors
- **Contextual Info**: Rich metadata about each element
- **State Awareness**: Knows about element relationships and form context

### vs. Full HTML

- **Efficiency**: Significantly smaller data transfer (5x reduction)
- **Focused Content**: Only actionable elements, no noise
- **Processing Speed**: Pre-analyzed structure for faster AI decisions

### vs. Basic Element Lists

- **Semantic Understanding**: Provides page structure context
- **Persistent References**: Highlight indices remain valid across page state
- **AI-Optimized Format**: String representation designed for AI consumption

## Technical Implementation

### Browser Compatibility

- **DOM Parser**: Works in all modern browsers
- **ES6+ Features**: Uses modern JavaScript features
- **TypeScript**: Full type safety and IntelliSense support

### Error Handling

Comprehensive error reporting for:

- Analysis failures (no DOM access, parsing errors)
- Element not found (index doesn't exist)
- Element disappeared (DOM changed after analysis)
- Interaction failures (element not clickable, disabled)

## Testing

Run comprehensive tests with:

```bash
# Performance comparison test
pnpm test -- --testPathPattern=web-toolkit-performance.test.ts --verbose

# DOM analyzer tests
npm test src/tests/dom-analyzer.test.ts
```

Test coverage includes:

- ✅ Semantic segment detection
- ✅ Interactive element extraction
- ✅ Highlight index assignment
- ✅ Element mapping functionality
- ✅ String format generation
- ✅ Form and label associations
- ✅ Accessibility attribute handling
- ✅ Performance benchmarking

## Troubleshooting

### Common Issues

1. **"No DOM analysis data found"**

   - Run `analyzePage` before `clickElementByIndex`
   - Analysis data is stored per tab, ensure you're on the correct tab

2. **"Element no longer exists"**

   - Page content changed after analysis
   - Re-run `analyzePage` to get updated element indices

3. **"Element is not visible"**
   - Element may be hidden by CSS or outside viewport
   - Check `element.hidden` property in analysis results

## Future Enhancements

- **Visual Element Detection**: Detect elements by visual cues
- **Dynamic Content Handling**: Support for SPA and dynamic content
- **Performance Monitoring**: Track analysis performance metrics
- **Multi-frame Support**: Handle iframes and nested contexts
- **Action Recording**: Track and replay interaction sequences

## License

This implementation is part of the Chrome Extension project and follows the same licensing terms.

## Contributing

1. Follow the existing code style and patterns
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure TypeScript compatibility
5. Test across different HTML structures

For questions or contributions, please refer to the main project documentation.
