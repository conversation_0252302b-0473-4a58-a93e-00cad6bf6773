# Memory Server

A high-performance memory management system for AI agents, built with TypeScript and following mem0-ts patterns.

## Features

- **Enhanced Search Memory**: Refactored search functionality based on mem0-ts implementation
- **Vector Storage**: PostgreSQL with pgvector for efficient similarity search
- **Graph Memory**: Neo4j integration for relationship-based memory
- **Multi-modal Support**: Text and vision message processing
- **Batch Operations**: Efficient batch memory operations
- **Comprehensive Logging**: Detailed monitoring and debugging capabilities

## Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL with pgvector extension
- Neo4j database (optional, for graph memory)
- Railway account for deployment

### Installation

```bash
npm install
npm run build
```

### Database Setup

#### Supabase/PostgreSQL Setup

Execute the following SQL in your Supabase SQL Editor:

```sql
-- Enable the vector extension
create extension if not exists vector;

-- Create the memories table
create table if not exists memories (
  id text primary key,
  embedding vector(1024),
  metadata jsonb,
  created_at timestamp with time zone default timezone('utc', now()),
  updated_at timestamp with time zone default timezone('utc', now())
);

-- Create the memory migrations table
create table if not exists memory_migrations (
  user_id text primary key,
  created_at timestamp with time zone default timezone('utc', now())
);

-- Create the vector similarity search function with enhanced filtering and min_score
create or replace function match_vectors(
  query_embedding vector(1024),
  match_count int,
  filter jsonb default '{}'::jsonb,
  min_score float default 0.0
)
returns table (
  id text,
  similarity float,
  metadata jsonb
)
language plpgsql
as $$
declare
  filter_key text;
  filter_value text;
  filter_condition text := '';
begin
  -- Build dynamic filter conditions
  for filter_key, filter_value in
    select key, value::text
    from jsonb_each_text(filter)
    where key != 'memoryType'
  loop
    if filter_condition != '' then
      filter_condition := filter_condition || ' and ';
    end if;
    filter_condition := filter_condition || format('t.metadata->>%L = %L', filter_key, filter_value);
  end loop;

  -- Handle memoryType filter separately
  if filter ? 'memoryType' then
    if filter_condition != '' then
      filter_condition := filter_condition || ' and ';
    end if;
    filter_condition := filter_condition || format('t.metadata->>%L = %L', 'memoryType', filter->>'memoryType');
  end if;

  -- Build the final query
  if filter_condition = '' then
    filter_condition := 'true';
  end if;

  return query execute format('
    select
      t.id::text,
      1 - (t.embedding <=> $1) as similarity,
      t.metadata
    from memories t
    where %s
      and (1 - (t.embedding <=> $1)) >= $2
    order by t.embedding <=> $1
    limit $3
  ', filter_condition)
  using query_embedding, min_score, match_count;
end;
$$;


```

#### Neo4j Setup (Optional)

For graph memory functionality, deploy Neo4j on Railway and configure the connection.

## Configuration

Create a `.env` file with the following variables:

```env
# Database Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
SUPABASE_TABLE=memories

# Neo4j Configuration (Optional)
NEO4J_URI=your_neo4j_uri
NEO4J_USERNAME=your_neo4j_username
NEO4J_PASSWORD=your_neo4j_password

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key
EMBEDDING_API_KEY=your_embedding_api_key
EMBEDDING_MODEL=deepinfra/embedding-001

# Server Configuration
PORT=3000
NODE_ENV=production
```

## API Endpoints

### Search Memory

```http
POST /api/memory/search
Content-Type: application/json

{
  "query": "What do you know about my birthday?",
  "config": {
    "userId": "user123",
    "agentId": "agent456",
    "runId": "run789",
    "limit": 10,
    "threshold": 0.7
  }
}
```

### Add Memory

```http
POST /api/memory/add
Content-Type: application/json

{
  "message": [
    {
      "role": "user",
      "content": "My birthday is July 1st"
    }
  ],
  "config": {
    "userId": "user123",
    "metadata": {
      "conversationId": "conv_123"
    }
  }
}
```

### Batch Add Memory

```http
POST /api/memory/add_batch
Content-Type: application/json

{
  "batch": [
    {
      "message": [{"role": "user", "content": "Memory 1"}],
      "config": {"userId": "user123"}
    },
    {
      "message": [{"role": "user", "content": "Memory 2"}],
      "config": {"userId": "user123"}
    }
  ]
}
```

## Enhanced Search Memory Features

The search memory functionality has been completely refactored based on mem0-ts patterns:

### Key Improvements

1. **Enhanced Validation**: Comprehensive input validation for all parameters
2. **Better Error Handling**: Detailed error messages and logging
3. **Improved Result Formatting**: Consistent result structure following mem0-ts patterns
4. **Advanced Filtering**: Support for complex filter expressions
5. **Performance Optimization**: Better SQL queries and indexing

### Search Tool Features

- **Natural Language Queries**: Search using human-readable queries
- **Configurable Limits**: Set custom result limits (default: 10)
- **Flexible Filtering**: Filter by userId, agentId, runId, or custom metadata
- **Score-based Ranking**: Results ranked by similarity score
- **Metadata Support**: Rich metadata for each memory item

### Example Usage

```typescript
// Search for birthday-related memories
const result = await searchMemoryTool.execute(
  {
    query: 'What do you know about my birthday?',
    limit: 5,
    filters: { userId: 'user123' },
  },
  context
);

// Result format
{
  results: [
    {
      id: 'mem_001',
      text: "User's birthday is July 1st",
      score: 0.95,
      metadata: {
        conversationId: 'conv_123',
        timestamp: '2024-01-15T10:30:00Z',
      },
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z',
    },
  ];
}
```

## Testing

Run the test suite to verify the refactored functionality:

```bash
# Run the search memory test
node test-search-refactor.js

# Run all tests
npm test
```

## Deployment

### Railway Deployment

1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy the application

### Environment Variables for Railway

- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Your Supabase service role key
- `NEO4J_URI`: Your Neo4j database URI (optional)
- `OPENAI_API_KEY`: Your OpenAI API key
- `EMBEDDING_API_KEY`: Your embedding service API key

## Cost Estimation

Memory server operations cost breakdown:

- **Add Memory**:
  - Embedding generation: 0.1 tokens
  - Vector storage and indexing: 0.1 tokens
  - Graph relationship generation: 0.2 tokens
  - **Total**: 0.4 tokens

- **Search Memory**:
  - Query embedding generation: 0.1 tokens
  - Vector retrieval: 0.1 tokens
  - Graph search: 0.1 tokens
  - **Total**: 0.3 tokens

## Architecture

The memory server follows a modular architecture:

```
memory-server/
├── src/
│   ├── agents/          # Memory agent implementation
│   ├── api/             # REST API endpoints
│   ├── services/        # Core services (LLM, Vector Store)
│   ├── tools/           # Memory tools (Add, Search, Delete)
│   └── types/           # TypeScript type definitions
├── test-search-refactor.js  # Test script
└── SEARCH_MEMORY_REFACTOR.md # Detailed refactor documentation
```

## Migration Guide

If upgrading from a previous version:

1. **Database Schema**: No changes required - uses existing table structure
2. **API Changes**: Update client code to use new validation requirements
3. **Error Handling**: Implement proper error handling for new validation rules
4. **Data Storage**: Text and timestamps are now stored in the metadata JSONB field

## Known Issues

**Duplicate Embedding Billing**: Due to compatibility requirements during Chrome extension review, both old and new embedding billing systems are active. Remove the old embedding billing logic in `api-cf` once the extension is approved.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
