import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { VectorInsertPayload, VectorStoreInterface } from '../types/memory';
import { MemoryItem, MemoryMetadata, SearchFilters } from '@the-agent/shared';
import { config } from '../config';
import crypto from 'crypto';

// Database query result type
interface VectorSearchResult {
  id: string;
  similarity: number;
  metadata?: MemoryMetadata;
}

/*
SQL Migration to run in Supabase SQL Editor:

--  enable vector extension (can be repeated if not enabled)
create extension if not exists vector;

-- create/upgrade vector similarity search function with min_score
create or replace function match_vectors(
  query_embedding vector(1024),
  match_count int,
  filter jsonb default '{}'::jsonb,
  min_score float default 0.0
)
returns table (
  id text,
  similarity float,
  metadata jsonb
)
language plpgsql
as $$
declare
  filter_key text;
  filter_value text;
  filter_condition text := '';
begin
  -- Handle memoryType filter first
  if filter ? 'memoryType' then
    filter_condition := format('t.metadata->>%L = %L', 'memoryType', filter->>'memoryType');
    
    -- For site memory, don't apply user-specific filters
    if filter->>'memoryType' = 'site' then
      -- Build dynamic filter conditions excluding user-specific filters
      for filter_key, filter_value in 
        select key, value::text 
        from jsonb_each_text(filter) 
        where key != 'memoryType' 
          and key != 'userId' 
          and key != 'conversationId'
          and key != 'agentId'
          and key != 'runId'
          and key != 'taskId'
          and key != 'workflowId'
      loop
        if filter_condition != '' then
          filter_condition := filter_condition || ' and ';
        end if;
        filter_condition := filter_condition || format('t.metadata->>%L = %L', filter_key, filter_value);
      end loop;
    else
      -- For non-site memory, apply all filters including userId
      for filter_key, filter_value in 
        select key, value::text 
        from jsonb_each_text(filter) 
        where key != 'memoryType'
      loop
        if filter_condition != '' then
          filter_condition := filter_condition || ' and ';
        end if;
        filter_condition := filter_condition || format('t.metadata->>%L = %L', filter_key, filter_value);
      end loop;
    end if;
  else
    -- No memoryType filter, apply all filters
    for filter_key, filter_value in 
      select key, value::text 
      from jsonb_each_text(filter)
    loop
      if filter_condition != '' then
        filter_condition := filter_condition || ' and ';
      end if;
      filter_condition := filter_condition || format('t.metadata->>%L = %L', filter_key, filter_value);
    end loop;
  end if;
  
  -- Build the final query
  if filter_condition = '' then
    filter_condition := 'true';
  end if;
  
  return query execute format('
    select
      t.id::text,
      1 - (t.embedding <=> $1) as similarity,
      t.metadata
    from memories t
    where %s
      and (1 - (t.embedding <=> $1)) >= $2
    order by t.embedding <=> $1
    limit $3
  ', filter_condition)
  using query_embedding, min_score, match_count;
end;
$$;
*/
export class VectorStore implements VectorStoreInterface {
  private client: SupabaseClient;
  private table: string;
  private embeddingCol: string;
  private metadataCol: string;

  constructor() {
    const supabaseConfig = config.supabase;
    this.client = createClient(supabaseConfig.supabaseUrl, supabaseConfig.supabaseKey);
    this.table = supabaseConfig.tableName;
    this.embeddingCol = supabaseConfig.embeddingColumnName || 'embedding';
    this.metadataCol = supabaseConfig.metadataColumnName || 'metadata';
  }

  // insert vectors and payloads
  async insert(vectors: number[][], payloads: VectorInsertPayload[]): Promise<string[]> {
    const rows = vectors.map((embedding, i) => ({
      id: payloads[i].id || crypto.randomUUID(),
      [this.embeddingCol]: embedding,
      [this.metadataCol]: {
        ...payloads[i].metadata,
        data: payloads[i].text,
        created_at: payloads[i].created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    }));

    const { data, error } = await this.client.from(this.table).insert(rows).select('id');
    if (error) throw error;
    return (data || []).map((row: { id: string }) => row.id);
  }

  // similarity search with enhanced filtering and result formatting
  async search(
    vector: number[],
    limit: number = 10,
    filters?: SearchFilters,
    scoreThreshold?: number
  ): Promise<MemoryItem[]> {
    try {
      console.log('🔍 Vector search with filters:', JSON.stringify(filters, null, 2));

      const { data, error } = await this.client.rpc('match_vectors', {
        query_embedding: vector,
        match_count: limit,
        filter: filters || {},
        min_score: typeof scoreThreshold === 'number' ? scoreThreshold : 0.0,
      });

      if (error) {
        console.error('Vector search query error:', error);
        throw error;
      }

      return (data || []).map((item: VectorSearchResult) => ({
        id: item.id,
        memory: item.metadata?.data || '',
        score: item.similarity,
        metadata: item.metadata || {},
        created_at: item.metadata?.created_at,
        updated_at: item.metadata?.updated_at,
      }));
    } catch (error) {
      console.error('Vector search error:', error);
      throw new Error(
        `Vector search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // delete memories by IDs
  async delete(ids: string[]): Promise<void> {
    try {
      if (!ids.length) return;
      const { error } = await this.client.from(this.table).delete().in('id', ids);
      if (error) throw error;
    } catch (error) {
      console.error('Vector delete error:', error);
      throw new Error(`Delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // update memory by ID
  async update(id: string, newContent: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(this.table)
        .update({
          [`${this.metadataCol}`]: this.client.rpc('jsonb_set', {
            target: `${this.metadataCol}`,
            path: '{memory}',
            value: JSON.stringify(newContent),
            create_missing: true,
          }),
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);
      if (error) throw error;
    } catch (error) {
      console.error('Vector update error:', error);
      throw new Error(`Update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
