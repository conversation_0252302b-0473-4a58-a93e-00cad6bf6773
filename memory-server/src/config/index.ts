import dotenv from 'dotenv';
import { z } from 'zod';
dotenv.config();

const configSchema = z.object({
  OPENROUTER_MODEL: z.string().min(1, 'OPENROUTER_MODEL is required'),
  OPENROUTER_API_KEY: z.string().min(1, 'OPENROUTER_API_KEY is required'),
  EMBEDDING_API_KEY: z.string().min(1, 'EMBEDDING_API_KEY is required'),
  EMBEDDING_MODEL: z.string().min(1, 'EMBEDDING_MODEL is required'),
  SUPABASE_URL: z.string().url('SUPABASE_URL must be a valid URL'),
  SUPABASE_KEY: z.string().min(1, 'SUPABASE_KEY is required'),
  SUPABASE_TABLE: z.string().default('memories'),
  SUPABASE_EMBEDDING_COL: z.string().default('embedding'),
  SUPABASE_METADATA_COL: z.string().default('metadata'),
  MEMORY_API_KEY: z.string().min(1, 'MEMORY_API_KEY is required'),
  PORT: z.string().default('3000'),
  NODE_ENV: z.string().default('development'),
  LOG_LEVEL: z.string().default('info'),
});

const env = configSchema.parse(process.env);

export const config = {
  openrouter: {
    model: env.OPENROUTER_MODEL,
    apiKey: env.OPENROUTER_API_KEY,
  },
  embedding: {
    apiKey: env.EMBEDDING_API_KEY,
    model: env.EMBEDDING_MODEL,
  },
  supabase: {
    supabaseUrl: env.SUPABASE_URL,
    supabaseKey: env.SUPABASE_KEY,
    tableName: env.SUPABASE_TABLE,
    embeddingColumnName: env.SUPABASE_EMBEDDING_COL,
    metadataColumnName: env.SUPABASE_METADATA_COL,
  },
  apiKey: env.MEMORY_API_KEY,
  port: parseInt(env.PORT, 10),
  nodeEnv: env.NODE_ENV,
  logLevel: env.LOG_LEVEL,
};
