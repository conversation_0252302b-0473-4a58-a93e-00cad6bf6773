import { Router } from 'express';
import { MemoryService } from '../services/memory';

export const createMemoryRoutes = (service: MemoryService): Router => {
  const router = Router();

  router.post('/', service.addMemory.bind(service));
  router.post('/add', service.addMemory.bind(service));
  router.post('/search', service.searchMemories.bind(service));
  router.post('/add_raw', service.addRawMemory.bind(service));

  return router;
};
