import OpenAI from 'openai';
import { Chat<PERSON><PERSON>, AgentConfig } from '@the-agent/shared';
import type { ContextBuilder } from '@the-agent/shared';
import { MEMORY_SYSTEM_PROMPT } from './prompt';
import { MemoryToolExecutor } from '../tools/executor';
import { VectorStoreInterface, LLMRuntimeInterface } from '../types/memory';
import { DEFAULT_MAX_TOOL_CALLS } from '../config/constants';

export async function createMemoryAgent(
  model: string,
  client: OpenAI,
  contextBuilder: ContextBuilder,
  vectorStore: VectorStoreInterface,
  llmRuntime: LLMRuntimeInterface
): Promise<ChatAgent> {
  const systemPrompt = MEMORY_SYSTEM_PROMPT;
  const toolExecutor = new MemoryToolExecutor(vectorStore, llmRuntime);

  const config: AgentConfig = {
    id: 'memory-agent',
    model,
    llmClient: client,
    contextBuilder,
    toolExecutor,
    systemPrompt,
    maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
  };

  return new ChatAgent(config);
}
