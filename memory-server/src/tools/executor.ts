import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AgentRuntimeConfig } from '@the-agent/shared';
import { memoryTools } from '.';
import { MemoryContext } from '../types/memory';
import { TOOL_NAME_PROCESS_MEMORY_OPERATIONS, TOOL_NAME_SEARCH_MEMORY } from '../config/constants';
import { VectorStoreInterface, LLMRuntimeInterface } from '../types/memory';

export class MemoryToolExecutor implements ToolExecutor {
  constructor(
    private vectorStore: VectorStoreInterface,
    private llmRuntime: LLMRuntimeInterface
  ) {}

  getTools() {
    return memoryTools.map(t => ({
      type: 'function' as const,
      function: {
        name: t.name,
        description: t.description,
        parameters: t.parameters,
      },
    }));
  }

  getPostToolcallMessage(toolCall: ToolCall): string {
    switch (toolCall.function.name) {
      case TOOL_NAME_SEARCH_MEMORY:
        return `You have called SearchMemory. Before adding any new memory, you MUST:
- Analyze the search results and your reasoning.
- If any memory is exactly the same, call ProcessMemoryOperations to delete ALL such memories, then add the new fact.
- If multiple memories are highly similar (score > 0.75) but not exactly the same, you MUST aggregate all similar facts (including the new one) into a single concise fact, call ProcessMemoryOperations to delete ALL similar old memories, then add the aggregation.
- Only if there is no similar or identical memory, add directly with ProcessMemoryOperations.
- Always provide explicit reasoning before making any tool call.

**Example for aggregation:**
- Memory A: "User will attend a conference in Berlin in September"
- Memory B: "User will attend a conference in Berlin in Sept."
- Memory C: "User will attend a conference in Berlin"
If A, B, and C are all highly similar (score > 0.75), aggregate them into one fact, e.g. "User will attend a conference in Berlin (September/Sep.)", delete all old memories, and add the aggregation.
`;
      case TOOL_NAME_PROCESS_MEMORY_OPERATIONS:
        return 'After ProcessMemoryOperations is executed, you do not need to continue the conversation or generate any further output. The process ends after the ProcessMemoryOperations tool call.';
      default:
        return '';
    }
  }

  isFinalToolCall(toolCall: ToolCall): boolean {
    return toolCall.function.name === TOOL_NAME_PROCESS_MEMORY_OPERATIONS;
  }

  async execute(toolCall: ToolCall, options?: AgentRuntimeConfig): Promise<ToolCallResult> {
    const tool = memoryTools.find(t => t.name === toolCall.function.name);
    if (!tool) return { success: false, error: 'Unknown tool' };

    let params: any;
    try {
      params = JSON.parse(toolCall.function.arguments);
    } catch (e) {
      throw new Error('Invalid JSON in toolCall.function.arguments');
    }
    if (!params || typeof params !== 'object') {
      throw new Error('Tool call arguments must be an object');
    }

    const metadata = options?.toolOptions?.metadata;
    const staticFilters = options?.toolOptions?.filters;

    function injectMetadataToFacts(facts: any[], metadata: Record<string, unknown> | undefined) {
      if (!Array.isArray(facts) || !metadata) return facts;
      if (typeof facts[0] === 'string') return facts;
      return facts.map(fact => ({
        ...fact,
        metadata: { ...fact.metadata, ...metadata },
      }));
    }

    // only use static filters, do not process LLM/tool call filters
    if (tool.name === TOOL_NAME_PROCESS_MEMORY_OPERATIONS) {
      if (!Array.isArray(params.operations)) {
        throw new Error('operations must be an array');
      }
      params.operations = params.operations.map((op: any) => {
        return {
          ...op,
          facts: injectMetadataToFacts(op.facts, metadata),
          filters: staticFilters,
        };
      });
    } else {
      if (params.facts) params.facts = injectMetadataToFacts(params.facts, metadata);
      params.filters = staticFilters;
    }

    const context: MemoryContext = {
      vectorStore: this.vectorStore,
      llmRuntime: this.llmRuntime,
    };

    const result = await tool.execute(params, context, {
      metadata,
      filters: staticFilters as Record<string, unknown> | undefined,
    });
    return { success: true, data: result };
  }
}
