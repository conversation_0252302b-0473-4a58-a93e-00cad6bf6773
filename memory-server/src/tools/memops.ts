import { <PERSON><PERSON><PERSON>x<PERSON>, Add<PERSON><PERSON>ory<PERSON><PERSON>ult, DeleteMemoryR<PERSON>ult } from '../types/memory';
import { Tool<PERSON>allResult } from '@the-agent/shared';
import { TOOL_NAME_PROCESS_MEMORY_OPERATIONS } from '../config/constants';
import { searchMemories } from './search';

// Define MemoryOperation type
export interface MemoryOperation {
  action: 'add' | 'update' | 'delete';
  facts?: string[];
  ids?: string[];
}

export const processMemoryOperationsTool = {
  name: TOOL_NAME_PROCESS_MEMORY_OPERATIONS,
  description:
    'MANDATORY: You are STRICTLY FORBIDDEN from calling this tool to add any fact unless you have just called SearchMemory for that fact and explained your deduplication reasoning. Your reasoning MUST explicitly reference the search results and explain why each fact is new or not a duplicate. Never add facts that already exist in memory. If you skip deduplication, or do not output reasoning and search results, your output will be rejected. The argument MUST be an object with an "operations" field, which is an array. Always call this tool with the following format:\n' +
    '{ "operations": [ { "action": "add", "facts": ["fact1", "fact2"] }, { "action": "delete", "ids": ["id1"] } ] }',
  parameters: {
    type: 'object',
    properties: {
      operations: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            action: {
              type: 'string',
              enum: ['add', 'update', 'delete'],
              description: 'Operation type: add, update, or delete',
            },
            facts: {
              type: 'array',
              items: { type: 'string' },
              description: 'Facts to be added or used in update (required for add/update)',
            },
            ids: {
              type: 'array',
              items: { type: 'string' },
              description: 'IDs to delete or update (required for delete/update)',
            },
          },
          required: ['action'],
        },
        description:
          'Array of memory operations to perform (add, update, delete). Example: { "operations": [ { "action": "add", "facts": ["fact1", "fact2"] }, { "action": "delete", "ids": ["id1"] } ] }',
      },
    },
    required: ['operations'],
    description:
      'Batch memory operations. Example: { "operations": [ { "action": "add", "facts": ["fact1", "fact2"] }, { "action": "delete", "ids": ["id1"] } ] }',
  },

  async execute(
    params: any,
    _context: MemoryContext,
    options?: { metadata?: Record<string, unknown>; filters?: Record<string, unknown> }
  ): Promise<ToolCallResult> {
    const metadata = options?.metadata;
    const filters = options?.filters;
    if (!params || !Array.isArray(params.operations)) {
      throw new Error('[memops] Invalid operations format: operations must be an array');
    }
    try {
      const results: Array<AddMemoryResult | DeleteMemoryResult> = [];
      for (const op of params.operations) {
        const { action, facts, ids } = op;
        if (action === 'add') {
          if (!facts || facts.length === 0) {
            results.push({ success: true, factIds: [] });
            continue;
          }
          const factIds: string[] = [];
          for (const fact of facts) {
            // Deduplication: skip if exact same memory already exists
            const searchResult = await searchMemories({ text: fact, filters, limit: 5 }, _context);
            const exact = searchResult.results.find(r => {
              try {
                if (!r.metadata?.data) return false;
                return r.metadata.data === fact;
              } catch {
                return false;
              }
            });
            if (exact) {
              // Skip duplicate
              continue;
            }
            const vector = await _context.llmRuntime.embed(fact);
            const payload = {
              text: fact,
              metadata: {
                ...(metadata ?? {}),
                ...(filters ?? {}),
                data: fact,
              },
            };
            const [insertedId] = await _context.vectorStore.insert([vector], [payload]);
            factIds.push(insertedId);
          }
          results.push({ success: true, factIds });
          continue;
        }
        if (action === 'delete') {
          if (!ids || ids.length === 0) {
            throw new Error('No ids provided for deletion.');
          }
          await _context.vectorStore.delete(ids);
          results.push({ success: true, deleted: ids.length });
          continue;
        }
        if (action === 'update') {
          if (!ids || !facts || ids.length !== facts.length) {
            throw new Error('Update requires matching ids and facts arrays.');
          }
          const vectors = await Promise.all(facts.map((f: string) => _context.llmRuntime.embed(f)));
          await _context.vectorStore.delete(ids);
          const payloads = facts.map((fact: string) => ({
            text: fact,
            metadata: {
              ...(filters ?? {}),
              updatedAt: new Date().toISOString(),
            },
          }));
          const newIds = await _context.vectorStore.insert(vectors, payloads);
          results.push({ success: true, factIds: newIds });
          continue;
        }
        throw new Error(`Unknown action: ${action}`);
      }
      return { success: true, data: results };
    } catch (err) {
      console.error('[memops] execute error:', err);
      throw err;
    }
  },
};
