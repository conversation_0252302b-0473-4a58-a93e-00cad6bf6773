import {
  DEFAULT_SEARCH_LIMIT,
  DEFAULT_SEARCH_THRESHOLD,
  TOOL_NAME_SEARCH_MEMORY,
} from '../config/constants';
import { MemoryItem, SearchResult, SearchFilters } from '@the-agent/shared';
import { MemoryContext } from '../types/memory';

export async function searchMemories(
  params: { text: string; limit?: number; filters?: SearchFilters; scoreThreshold?: number },
  context: MemoryContext
): Promise<SearchResult> {
  const { text, limit, filters, scoreThreshold } = params;
  const mergedFilters = { ...(filters ?? {}) };
  if (!mergedFilters.userId && !mergedFilters.hostname && !mergedFilters.conversationId) {
    throw new Error('At least one of userId, hostname, or conversationId filter is required!');
  }
  const queryEmbedding = await context.llmRuntime.embed(text);
  const memories = await context.vectorStore.search(
    queryEmbedding,
    limit ?? DEFAULT_SEARCH_LIMIT,
    mergedFilters,
    scoreThreshold ?? DEFAULT_SEARCH_THRESHOLD
  );
  const results: MemoryItem[] = memories.map(mem => ({
    id: mem.id,
    memory: mem.memory,
    score: mem.score,
    metadata: mem.metadata,
  }));
  return { results };
}

export const searchMemoryTool = {
  name: TOOL_NAME_SEARCH_MEMORY,
  description:
    'Search for relevant memories using natural language queries with userId (required) and context filters. Returns a list of similar or related memory facts for deduplication or aggregation.',
  parameters: {
    type: 'object',
    properties: {
      text: {
        type: 'string',
        description: 'Natural language query to search for relevant memories',
      },
      filters: {
        type: 'object',
        description: 'Filters for search (userId, conversationId, etc.)',
        additionalProperties: true,
      },
      limit: {
        type: 'number',
        description: 'Max results',
        default: 5,
      },
    },
    required: ['text'],
  },
  execute: async (params: any, context: MemoryContext) => {
    return searchMemories(
      { text: params.text, filters: params.filters, limit: params.limit },
      context
    );
  },
};
