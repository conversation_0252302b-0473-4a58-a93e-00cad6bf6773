const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testSupabase() {
  console.log('Testing Supabase connection...');

  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;
  const tableName = process.env.SUPABASE_TABLE || 'memories';

  console.log('Supabase URL:', supabaseUrl);
  console.log('Table name:', tableName);
  console.log('Has API key:', !!supabaseKey);

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase configuration');
    return;
  }

  try {
    const client = createClient(supabaseUrl, supabaseKey);

    // Test basic connection
    console.log('Testing basic connection...');
    const { data, error } = await client.from(tableName).select('count').limit(1);

    if (error) {
      console.error('Connection error:', error);
    } else {
      console.log('Connection successful!');
      console.log('Data:', data);
    }

    // 测试表结构
    console.log('Testing table structure...');
    const { data: tableData, error: tableError } = await client
      .from(tableName)
      .select('*')
      .limit(5);

    if (tableError) {
      console.error('Table query error:', tableError);
    } else {
      console.log('Table query successful!');
      console.log('Records found:', tableData.length);
      if (tableData.length > 0) {
        console.log('Sample record:', tableData[0]);
      }
    }
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testSupabase();
