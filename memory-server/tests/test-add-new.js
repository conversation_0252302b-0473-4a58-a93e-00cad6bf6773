const axios = require('axios');
const assert = require('assert');

const BASE = 'http://localhost:3000/memory';

async function run() {
  console.log('🧪 Testing Memory API (Refactored)...\n');

  // Test 1: Basic memory addition
  console.log('1. Testing basic memory addition...');
  let res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 1,
        conversation_id: 1753172351646,
        role: 'user',
        content: 'I will attend a conference in Paris next month',
        status: 'pending',
      },
    ],
    config: {
      filters: {
        userId: 'test-user-1',
        conversationId: 1753172351646,
      },
      metadata: {},
    },
  });
  assert.strictEqual(res.status, 201);
  assert.deepStrictEqual(res.data.results, []);
  assert.deepStrictEqual(res.data.relations, []);
  console.log('✅ Basic memory addition passed');

  // Test 2: Memory search
  console.log('\n2. Testing memory search...');
  res = await axios.post(`${BASE}/search`, {
    text: 'conference Paris travel',
    config: {
      filters: {
        userId: 'test-user-1',
        conversationId: 1753172351646,
      },
      limit: 10,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  console.log('✅ Memory search passed');

  // Test 3: Missing userId in filters (should fail)
  console.log('\n3. Testing missing userId validation...');
  try {
    await axios.post(`${BASE}/add`, {
      messages: [
        {
          id: 2,
          conversation_id: 1753172351647,
          role: 'user',
          content: 'This should fail',
          status: 'pending',
        },
      ],
      config: {
        filters: {
          conversationId: 1753172351647,
        },
        metadata: {},
      },
    });
    assert.fail('Should throw validation error');
  } catch (e) {
    assert(e.response.status === 400, 'Should return 400 status');
    assert(
      e.response.data.error.includes('userId is required'),
      'Should mention userId requirement'
    );
    console.log('✅ Missing userId validation passed');
  }

  // Test 4: Empty messages array (should fail)
  console.log('\n4. Testing empty messages validation...');
  try {
    await axios.post(`${BASE}/add`, {
      messages: [],
      config: {
        filters: {
          userId: 'test-user-2',
          conversationId: 1753172351648,
        },
        metadata: {},
      },
    });
    assert.fail('Should throw validation error');
  } catch (e) {
    assert(e.response.status === 400, 'Should return 400 status');
    assert(
      e.response.data.error.includes('messages array is required'),
      'Should mention messages requirement'
    );
    console.log('✅ Empty messages validation passed');
  }

  // Test 5: Missing conversation_id in messages (should fail)
  console.log('\n5. Testing missing conversation_id validation...');
  try {
    await axios.post(`${BASE}/add`, {
      messages: [
        {
          id: 3,
          role: 'user',
          content: 'Missing conversation_id',
          status: 'pending',
        },
      ],
      config: {
        filters: {
          userId: 'test-user-3',
          conversationId: 1753172351649,
        },
        metadata: {},
      },
    });
    assert.fail('Should throw validation error');
  } catch (e) {
    assert(e.response.status === 400, 'Should return 400 status');
    assert(
      e.response.data.error.includes('conversation_id is required'),
      'Should mention conversation_id requirement'
    );
    console.log('✅ Missing conversation_id validation passed');
  }

  // Test 6: Different conversation_ids in same request (should fail)
  console.log('\n6. Testing mixed conversation_ids validation...');
  try {
    await axios.post(`${BASE}/add`, {
      messages: [
        {
          id: 4,
          conversation_id: 1753172351650,
          role: 'user',
          content: 'Message 1',
          status: 'pending',
        },
        {
          id: 5,
          conversation_id: 1753172351651,
          role: 'user',
          content: 'Message 2',
          status: 'pending',
        },
      ],
      config: {
        filters: {
          userId: 'test-user-4',
          conversationId: 1753172351650,
        },
        metadata: {},
      },
    });
    assert.fail('Should throw validation error');
  } catch (e) {
    assert(e.response.status === 400, 'Should return 400 status');
    assert(
      e.response.data.error.includes('same conversation_id'),
      'Should mention same conversation_id requirement'
    );
    console.log('✅ Mixed conversation_ids validation passed');
  }

  // Test 7: Missing text in search (should fail)
  console.log('\n7. Testing missing text in search...');
  try {
    await axios.post(`${BASE}/search`, {
      config: {
        filters: {
          userId: 'test-user-1',
        },
        limit: 10,
      },
    });
    assert.fail('Should throw validation error');
  } catch (e) {
    assert(e.response.status === 400, 'Should return 400 status');
    assert(e.response.data.error.includes('text is required'), 'Should mention text requirement');
    console.log('✅ Missing text in search validation passed');
  }

  // Test 8: Missing userId in search (should fail)
  console.log('\n8. Testing missing userId in search...');
  try {
    await axios.post(`${BASE}/search`, {
      text: 'test search',
      config: {
        filters: {},
        limit: 10,
      },
    });
    assert.fail('Should throw validation error');
  } catch (e) {
    assert(e.response.status === 400, 'Should return 400 status');
    assert(
      e.response.data.error.includes('userId is required'),
      'Should mention userId requirement'
    );
    console.log('✅ Missing userId in search validation passed');
  }

  // Test 9: Site message addition
  console.log('\n9. Testing site message addition...');
  res = await axios.post(`${BASE}/add_site`, {
    messages: [
      {
        id: 10,
        conversation_id: 0,
        role: 'user',
        content: 'Site memory test content',
        status: 'pending',
      },
    ],
    config: {
      filters: {
        userId: 'test-user-5',
        siteId: 'site-1',
        faqId: 'faq-1',
      },
      metadata: {
        siteUrl: 'https://example.com',
        pageTitle: 'Test Page',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  assert.deepStrictEqual(res.data.results, []);
  assert.deepStrictEqual(res.data.relations, []);
  console.log('✅ Site message addition passed');

  // Test 10: Missing userId in site message (should fail)
  console.log('\n10. Testing missing userId in site message...');
  try {
    await axios.post(`${BASE}/add_site`, {
      messages: [
        {
          id: 11,
          conversation_id: 0,
          role: 'user',
          content: 'Site message without userId',
          status: 'pending',
        },
      ],
      config: {
        filters: {
          siteId: 'site-2',
          faqId: 'faq-2',
        },
        metadata: {},
      },
    });
    assert.fail('Should throw validation error');
  } catch (e) {
    assert(e.response.status === 400, 'Should return 400 status');
    assert(
      e.response.data.error.includes('userId is required'),
      'Should mention userId requirement'
    );
    console.log('✅ Missing userId in site message validation passed');
  }

  // Test 11: Procedural memory with required metadata
  console.log('\n11. Testing procedural memory with required metadata...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 12,
        conversation_id: 1753172351652,
        role: 'user',
        content: 'Procedural memory test',
        status: 'pending',
      },
    ],
    config: {
      filters: {
        userId: 'test-user-6',
        conversationId: 1753172351652,
      },
      metadata: {
        workflowId: 'workflow-1',
        agentId: 'agent-1',
        taskId: 'task-1',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  assert.deepStrictEqual(res.data.results, []);
  assert.deepStrictEqual(res.data.relations, []);
  console.log('✅ Procedural memory with metadata passed');

  // Test 12: Procedural memory missing required metadata (should fail)
  console.log('\n12. Testing procedural memory missing required metadata...');
  try {
    await axios.post(`${BASE}/add`, {
      messages: [
        {
          id: 13,
          conversation_id: 1753172351653,
          role: 'user',
          content: 'Procedural memory missing metadata',
          status: 'pending',
        },
      ],
      config: {
        filters: {
          userId: 'test-user-7',
          conversationId: 1753172351653,
        },
        metadata: {
          memoryType: 'procedural',
          workflowId: 'workflow-2',
          // Missing agentId and taskId
        },
      },
    });
    assert.fail('Should throw validation error');
  } catch (e) {
    console.log('Error response:', e.response?.data);
    assert(e.response?.status === 400, 'Should return 400 status');
    assert(
      e.response?.data?.error?.includes('workflowId, agentId, and taskId are required'),
      'Should mention required fields'
    );
    console.log('✅ Procedural memory missing metadata validation passed');
  }

  console.log('\n🎉 All tests passed! Memory API is working correctly.');
}

run().catch(e => {
  console.error('❌ Test failed:', e.message);
  if (e.response) {
    console.error('Response status:', e.response.status);
    console.error('Response data:', e.response.data);
  }
  process.exit(1);
});
