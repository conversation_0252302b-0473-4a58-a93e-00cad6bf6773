#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get all test files
const testFiles = fs
  .readdirSync(__dirname)
  .filter(file => file.startsWith('test-') && file.endsWith('.js'))
  .map(file => path.basename(file, '.js'));

function runTest(testName) {
  const testFile = path.join(__dirname, `${testName}.js`);
  if (!fs.existsSync(testFile)) {
    console.error(`❌ Test file not found: ${testFile}`);
    return false;
  }

  console.log(`\n🧪 Running ${testName}...`);
  console.log('='.repeat(50));

  try {
    execSync(`node ${testFile}`, {
      stdio: 'inherit',
      cwd: path.dirname(__dirname),
    });
    console.log(`\n✅ ${testName} completed successfully`);
    return true;
  } catch (error) {
    console.log(`\n❌ ${testName} failed`);
    return false;
  }
}

function runAllTests() {
  console.log('🚀 Running all memory server tests...\n');

  const results = [];

  for (const testFile of testFiles) {
    const success = runTest(testFile);
    results.push({ test: testFile, success });
  }

  console.log('\n📊 Test Results Summary:');
  console.log('='.repeat(50));

  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;

  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.test}`);
  });

  console.log(`\n📈 Summary: ${passed} passed, ${failed} failed`);

  if (failed > 0) {
    process.exit(1);
  }
}

// Command line argument processing
const args = process.argv.slice(2);

if (args.length === 0) {
  // Display help information
  console.log('🧪 Memory Server Test Runner\n');
  console.log('Usage:');
  console.log('  node run-tests.js                    # Run all tests');
  console.log('  node run-tests.js <test-name>        # Run specific test');
  console.log('\nAvailable tests:');
  testFiles.forEach(file => {
    console.log(`  - ${file}`);
  });
  console.log('\nExamples:');
  console.log('  node run-tests.js test-search-simple');
  console.log('  node run-tests.js test-all-apis');
} else if (args[0] === 'all') {
  runAllTests();
} else {
  // Run the specified test
  const testName = args[0];
  if (testFiles.includes(testName)) {
    runTest(testName);
  } else {
    console.error(`❌ Test not found: ${testName}`);
    console.log('Available tests:', testFiles.join(', '));
    process.exit(1);
  }
}
