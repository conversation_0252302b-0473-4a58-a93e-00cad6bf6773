const http = require('http');

function testSearch() {
  console.log('Testing simple search...');

  const postData = JSON.stringify({
    text: 'TypeScript',
    config: {
      limit: 100,
      filters: {
        userId: '48a02574f02e8dad07b7c3478ab81a042a59054a46a50fa15bed63e8270cec52',
      },
    },
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/memory/search',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
    },
    timeout: 10000, // 10 second timeout
  };

  const req = http.request(options, res => {
    console.log('Status:', res.statusCode);
    console.log('Headers:', res.headers);

    let data = '';
    res.on('data', chunk => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('Response:', data);
    });
  });

  req.on('error', error => {
    console.error('Request failed:', error.message);
  });

  req.on('timeout', () => {
    console.error('Request timed out');
    req.destroy();
  });

  req.write(postData);
  req.end();
}

testSearch();
