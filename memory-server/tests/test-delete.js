const http = require('http');

function testDelete() {
  console.log('Testing delete memory...');

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/memories/831b4186-985e-4dc5-ab21-28eaaad17680',
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const req = http.request(options, res => {
    console.log('Status:', res.statusCode);
    console.log('Headers:', res.headers);

    let data = '';
    res.on('data', chunk => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('Response:', data);
    });
  });

  req.on('error', error => {
    console.error('Request failed:', error.message);
  });

  req.end();
}

testDelete();
