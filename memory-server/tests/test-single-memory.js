const axios = require('axios');

// 测试单条数据
const testMemory = {
  data: `Q: How to scroll down on https://x.com

A: To scroll down the page, use the WebToolkit_sendKeys tool with the Space key. This will scroll down one page at a time. WebToolkit_sendKeys({"keys":"Space"})`,
  metadata: {
    hostname: 'x.com',
    memoryType: 'site',
  },
};

// 配置
const MEMORY_SERVER_URL = 'http://localhost:3000/memory';

// 发送测试请求
async function testSingleMemory() {
  console.log('🧪 Testing single memory addition...');
  console.log('\n📤 Sending data:');
  console.log(JSON.stringify(testMemory, null, 2));

  try {
    const response = await axios.post(`${MEMORY_SERVER_URL}/add_raw`, testMemory);

    console.log(`\n📥 Response status: ${response.status}`);
    console.log('\n✅ Success response:');
    console.log(JSON.stringify(response.data, null, 2));

    // 测试搜索功能，验证数据是否正确存储
    console.log('\n🔍 Testing search functionality...');
    await testSearch();
  } catch (error) {
    console.error('❌ Failed to add memory:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// 测试搜索功能
async function testSearch() {
  try {
    const searchData = {
      text: 'scroll down',
      config: {
        limit: 5,
        filters: {
          userId: 'test-user-site',
          hostname: 'x.com',
        },
      },
    };

    const response = await axios.post(`${MEMORY_SERVER_URL}/search`, searchData);

    console.log(`\n📥 Search response status: ${response.status}`);
    console.log('\n🔍 Search results:');
    console.log(JSON.stringify(response.data, null, 2));

    if (response.data.results && response.data.results.length > 0) {
      console.log('\n✅ Data successfully stored and searchable!');
      console.log('📊 Stored memory structure:');
      console.log(JSON.stringify(response.data.results[0], null, 2));
    } else {
      console.log('\n⚠️ No search results found');
    }
  } catch (error) {
    console.error('❌ Search test failed:', error.message);
    if (error.response) {
      console.error('Search response status:', error.response.status);
      console.error('Search response data:', error.response.data);
    }
  }
}

// 运行测试
testSingleMemory().catch(console.error);
