# Memory Server Tests

This directory contains all test files for the memory-server.

## Test File Descriptions

### API Tests

- `test-all-apis.js` - Comprehensive test for all API endpoints
- `test-search-simple.js` - Test search functionality
- `test-add-new.js` - Test add memory functionality
- `test-delete.js` - Test delete memory functionality
- `test-get-memory.js` - Test get single memory functionality
- `test-get-all.js` - Test get all memories functionality
- `test-search-after-delete.js` - Test search after delete functionality

### Infrastructure Tests

- `test-supabase.js` - Test Supabase connection and table structure

## Running Tests

### Run All Tests

```bash
cd tests
node run-tests.js all
```

### Run a Single Test

```bash
cd tests
node run-tests.js test-search-simple
node run-tests.js test-all-apis
```

### List Available Tests

```bash
cd tests
node run-tests.js
```

## Test Environment Requirements

1. **Server Running**: Ensure memory-server is running on port 3000
2. **Environment Variables**: Ensure `.env` file is configured correctly
3. **Database Connection**: Ensure Supabase connection is working

## Test Data

The following user ID is used for testing:

```
48a02574f02e8dad07b7c3478ab81a042a59054a46a50fa15bed63e8270cec52
```

## Test Coverage

- ✅ Search memory API
- ✅ Add memory API
- ✅ Delete memory API
- ✅ Get single memory API
- ✅ Get all memories API
- ✅ User permission validation
- ✅ Error handling
- ✅ Supabase connection test

## Troubleshooting

If a test fails, please check:

1. Whether the server is running
2. Whether environment variables are configured correctly
3. Whether Supabase connection is working
4. Whether the network connection is normal
