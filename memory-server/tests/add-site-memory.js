const axios = require('axios');

const siteJson = [
  {
    site: 'https://x.com',
    faqs: [
      {
        question: 'How to scroll down on https://x.com',
        answer:
          'To scroll down the page, use the WebToolkit_sendKeys tool with the Space key. This will scroll down one page at a time. WebToolkit_sendKeys({"keys":"Space"})',
      },
      {
        question: 'How to search for content on https://x.com/home',
        answer: `To search for content on the home page, use the WebToolkit_input tool with the search field selector. WebToolkit_input({"selectorOrIndex":"[data-testid='SearchBox_Search_Input']","value":"?","options":{"pressEnterAfterInput":true}})`,
      },
      {
        question: 'How to navigate to explore page',
        answer: `To navigate to the explore page, use the WebToolkit_click tool with the explore button selector in the left sidebar. WebToolkit_click({"selectorOrIndex":"[data-testid='AppTabBar_Explore_Link']"})`,
      },
      {
        question: 'How to search for content on https://x.com/explore',
        answer: `To search for content on the explore page, use the WebToolkit_input tool with the search field selector. WebToolkit_input({"selectorOrIndex":"[data-testid='SearchBox_Search_Input']","value":"?","options":{"pressEnterAfterInput":true}})`,
      },
      {
        question: 'How to write a post on https://x.com/home',
        answer: `To write a post on the home page, use the WebToolkit_input tool with the tweet textarea selector. WebToolkit_input({"selectorOrIndex":"textarea[data-testid='tweetTextarea_0']","value":"?"})`,
      },
      {
        question: 'How to write a post in compose popup',
        answer: `To write a post in the compose popup (opened by clicking the Post button), use the WebToolkit_input tool with the popup textarea selector. WebToolkit_input({"selectorOrIndex":"textarea[data-testid='tweetTextarea_0']","value":"?"})`,
      },
      {
        question: 'How to write a post on https://x.com/compose/post',
        answer: `To write a post on the compose page (accessed via direct URL), use the WebToolkit_input tool with the compose textarea selector. WebToolkit_input({"selectorOrIndex":"textarea[data-testid='tweetTextarea_0']","value":"?"})`,
      },
      {
        question: 'How to click post on https://x.com/home',
        answer: `To click the post button on the home page, use the WebToolkit_click tool with the css selector of the post button. Must be executed after input. WebToolkit_click({"selectorOrIndex":"[data-testid='tweetButtonInline']"})`,
      },
      {
        question: 'How to click post in compose popup',
        answer: `To click the post button in the compose popup (opened by clicking the Post button), use the WebToolkit_click tool with the css selector of the post button. Must be executed after input. WebToolkit_click({"selectorOrIndex":"[data-testid='tweetButton']"})`,
      },
      {
        question: 'How to click post on https://x.com/compose/post',
        answer: `To click the post button on the compose page (accessed via direct URL), use the WebToolkit_click tool with the css selector of the post button. Must be executed after input. WebToolkit_click({"selectorOrIndex":"[data-testid='tweetButton']"})`,
      },
      {
        question: 'How to follow a user on their profile page',
        answer: `To follow a user on their profile page, use the WebToolkit_click tool with the css selector of the follow button. WebToolkit_click({"selectorOrIndex":"[aria-label^='Follow @']"})`,
      },
      {
        question: 'How to write a reply on https://x.com/{user_id}/status/{post_id}',
        answer: `To write a reply to a post, use the WebToolkit_input tool with the reply input field selector. WebToolkit_input({"selectorOrIndex":"textarea[data-testid='tweetTextarea_0']","value":"?"})`,
      },
      {
        question: 'How to click reply button on https://x.com/{user_id}/status/{post_id}',
        answer: `To click the reply button, use the WebToolkit_click tool with the css selector of the reply button. Must be executed after input. WebToolkit_click({"selectorOrIndex":"[data-testid='tweetButtonInline']"})`,
      },
      {
        question: 'How to like a post on https://x.com/{user_id}/status/{post_id}',
        answer: `To like a post, use the WebToolkit_click tool with the css selector of the like button. WebToolkit_click({"selectorOrIndex":"[data-testid='like']"})`,
      },
      {
        question: 'How to bookmark a post on https://x.com/{user_id}/status/{post_id}',
        answer: `To bookmark a post, use the WebToolkit_click tool with the css selector of the bookmark button. WebToolkit_click({"selectorOrIndex":"[data-testid='bookmark']"})`,
      },
      {
        question: 'How to find Posts on https://x.com/home',
        answer: `To find Posts, use the WebToolkit_analyzePageDOM tool with the posts section selector. WebToolkit_analyzePageDOM({"selector":"[aria-label='Timeline: Your Home Timeline']"})`,
      },
      {
        question: 'How to find Comments on https://x.com/{user_id}/status/{post_id}',
        answer: `To find Comments, use the WebToolkit_analyzePageDOM tool with the comments section selector. WebToolkit_analyzePageDOM({"selector":"[aria-label='Timeline: Conversation']"})`,
      },
      {
        question: 'How to find Messages on https://x.com/messages',
        answer: `To find Messages, use the WebToolkit_analyzePageDOM tool with the messages section selector. WebToolkit_analyzePageDOM({"selector":"[aria-label='Timeline: Messages']"})`,
      },
      {
        question: 'How to open a new tab for https://x.com',
        answer: `To open a new tab for X.com, use the TabToolkit_openTab tool with the URL. TabToolkit_openTab({"url":"https://x.com"})`,
      },
      {
        question: 'How to open compose popup',
        answer: `To open the compose popup, use the WebToolkit_click tool with the Post button selector in the left sidebar. WebToolkit_click({"selectorOrIndex":"[data-testid='SideNav_NewTweet_Button']"})`,
      },
      {
        question: 'How to navigate to compose post page',
        answer: `To navigate to the compose post page, use the TabToolkit_openTab tool with the compose URL. TabToolkit_openTab({"url":"https://x.com/compose/post"})`,
      },
      {
        question: 'How to navigate to explore page via URL',
        answer: `To navigate to the explore page directly, use the TabToolkit_openTab tool with the explore URL. TabToolkit_openTab({"url":"https://x.com/explore"})`,
      },
      {
        question: 'How to close the current tab',
        answer: `To close the current tab, first get the current tab ID using TabToolkit_getCurrentActiveTab, then use TabToolkit_closeTab with the tab ID. TabToolkit_closeTab({"tabId":tabId})`,
      },
      {
        question: 'How to extract text content from the current page',
        answer: `To extract text content from the current page, use the WebToolkit_extractText tool. This will convert the page content to Markdown format. WebToolkit_extractText({})`,
      },
      {
        question: 'How to take a screenshot of the current page',
        answer: `To take a screenshot of the current page, use the WebToolkit_screenshot tool. You can specify fullPage option for full page capture. WebToolkit_screenshot({"fullPage":true})`,
      },
      {
        question: 'How to refresh the current page',
        answer: `To refresh the current page, use the WebToolkit_refreshPage tool. You can specify waitForLoad and timeout options. WebToolkit_refreshPage({"waitForLoad":true,"timeout":5000})`,
      },
      {
        question: 'How to scroll to a specific element',
        answer: `To scroll to a specific element, use the WebToolkit_scrollTo tool with the element selector or index. WebToolkit_scrollTo({"selectorOrIndex":"[data-testid='tweetButtonInline']"})`,
      },
    ],
  },
  {
    site: 'https://www.xiaohongshu.com/',
    faqs: [
      {
        question: 'How to scroll down on https://www.xiaohongshu.com',
        answer: `To scroll down the page, use the WebToolkit_sendKeys tool with the Space key to scroll down one screen. WebToolkit_sendKeys({"keys":"Space"})`,
      },
      {
        question: 'How to search for content on https://www.xiaohongshu.com/explore',
        answer: `To search for content, use the WebToolkit_input tool with the search field selector. WebToolkit_input({"selectorOrIndex":"#search-input","value":"?","options":{"pressEnterAfterInput":true}})`,
      },
      {
        question:
          'How to view user profile from post detail page on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To view the user profile from a post detail page, use the WebToolkit_click tool with the css selector of the profile button. WebToolkit_click({"selectorOrIndex":".author-wrapper a"})`,
      },
      {
        question: 'How to follow a user on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To follow a user on the post detail page, use the WebToolkit_click tool with the css selector of the follow button. WebToolkit_click({"selectorOrIndex":".note-detail-follow-btn button"})`,
      },
      {
        question: 'How to close post detail page on https://www.xiaohongshu.com/explore/{post_id}',
        answer:
          'To close the post detail page, use the WebToolkit_click tool with the css selector of the close button. WebToolkit_click({"selectorOrIndex":".close-circle"})',
      },
      {
        question: 'How to write a reply on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To write a reply to a post, use the WebToolkit_input tool with the reply input field selector. WebToolkit_input({"selectorOrIndex":"#content-textarea","value":"?"})`,
      },
      {
        question: 'How to submit reply on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To submit the reply, use the WebToolkit_click tool with the css selector of the reply button. Must be executed after input. WebToolkit_click({"selectorOrIndex":".btn.submit"})`,
      },
      {
        question: 'How to like a post on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To like a post, use the WebToolkit_click tool with the css selector of the like button. WebToolkit_click({"selectorOrIndex":".like-wrapper.like-active"})`,
      },
      {
        question: 'How to collect a post on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To collect a post, use the WebToolkit_click tool with the css selector of the collect button. WebToolkit_click({"selectorOrIndex":"#note-page-collect-board-guide"})`,
      },
      {
        question: 'How to view comments on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To view comments on a post, use the WebToolkit_click tool with the css selector of the view comment button. WebToolkit_click({"selectorOrIndex":".chat-wrapper"})`,
      },
      {
        question: 'How to find Posts on https://www.xiaohongshu.com/explore',
        answer: `To find Posts, use the WebToolkit_analyzePageDOM tool. You'll need the selector of the post field. WebToolkit_analyzePageDOM({"selector":"#mfContainer"})`,
      },
      {
        question: 'How to find Comments on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To find Comments, use the WebToolkit_analyzePageDOM tool. You'll need the selector of the comment field. WebToolkit_analyzePageDOM({"selector":".comment-item"})`,
      },
      {
        question: 'How to find Notifications on https://www.xiaohongshu.com/notification',
        answer: `To find Notifications, use the WebToolkit_analyzePageDOM tool. You'll need the selector of the notification field. WebToolkit_analyzePageDOM({"selector":".tabs-content-container"})`,
      },
      {
        question: 'How to open a new tab for https://www.xiaohongshu.com',
        answer: `To open a new tab for Xiaohongshu, use the TabToolkit_openTab tool with the URL. TabToolkit_openTab({"url":"https://www.xiaohongshu.com"})`,
      },
      {
        question: 'How to extract text content from Xiaohongshu page',
        answer: `To extract text content from the current Xiaohongshu page, use the WebToolkit_extractText tool. This will convert the page content to Markdown format. WebToolkit_extractText({})`,
      },
      {
        question: 'How to take a screenshot of Xiaohongshu page',
        answer: `To take a screenshot of the current Xiaohongshu page, use the WebToolkit_screenshot tool. You can specify fullPage option for full page capture. WebToolkit_screenshot({"fullPage":true})`,
      },
      {
        question: 'How to refresh Xiaohongshu page',
        answer: `To refresh the current Xiaohongshu page, use the WebToolkit_refreshPage tool. You can specify waitForLoad and timeout options. WebToolkit_refreshPage({"waitForLoad":true,"timeout":5000})`,
      },
      {
        question: 'How to scroll to a specific element on Xiaohongshu',
        answer: `To scroll to a specific element on Xiaohongshu, use the WebToolkit_scrollTo tool with the element selector or index. WebToolkit_scrollTo({"selectorOrIndex":".note-detail-follow-btn button"})`,
      },
    ],
  },
];

// 配置
const MEMORY_SERVER_URL = 'http://localhost:3000/memory';
const BATCH_SIZE = 10; // 每批处理的数量
const DELAY_BETWEEN_BATCHES = 1000; // 批次间延迟（毫秒）

// 转换数据格式
function transformData(siteData) {
  const memories = [];

  siteData.forEach(site => {
    // 确保 URL 有正确的协议
    let siteUrl = site.site;
    if (!siteUrl.startsWith('http://') && !siteUrl.startsWith('https://')) {
      siteUrl = 'https://' + siteUrl;
    }

    const hostname = new URL(siteUrl).hostname;

    site.faqs.forEach(faq => {
      const memory = {
        data: `Q: ${faq.question}\n\nA: ${faq.answer}`,
        metadata: {
          site: siteUrl,
          hostname: hostname,
          memoryType: 'site',
        },
      };
      memories.push(memory);
    });
  });

  return memories;
}

// 发送单个 memory 请求
async function addMemory(memoryData) {
  try {
    console.log('📤 Sending raw memory:', JSON.stringify(memoryData, null, 2));

    const response = await axios.post(`${MEMORY_SERVER_URL}/add_raw`, memoryData);
    console.log('✅ Memory added successfully:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Failed to add memory:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// 批量处理
async function processBatch(memories, startIndex) {
  const batch = memories.slice(startIndex, startIndex + BATCH_SIZE);
  console.log(
    `\n📦 Processing batch ${Math.floor(startIndex / BATCH_SIZE) + 1} (${batch.length} items)`
  );

  const promises = batch.map(async (memory, index) => {
    const questionText = memory.data.split('\n')[0].replace('Q: ', '');
    console.log(
      `  ${startIndex + index + 1}. Adding memory for: ${memory.metadata.hostname} - ${questionText.substring(0, 50)}...`
    );
    const result = await addMemory(memory);
    return result;
  });

  const results = await Promise.all(promises);
  const successCount = results.filter(result => result).length;
  const failCount = results.length - successCount;

  console.log(`  ✅ Success: ${successCount}, ❌ Failed: ${failCount}`);
  return { successCount, failCount };
}

// 主函数
async function main() {
  console.log('🚀 Starting to update site memories...');
  console.log(`📊 Total sites: ${siteJson.length}`);

  const totalFaqs = siteJson.reduce((sum, site) => sum + site.faqs.length, 0);
  console.log(`📊 Total FAQs: ${totalFaqs}`);

  // 转换数据
  const memories = transformData(siteJson);
  console.log(`🔄 Transformed ${memories.length} memories`);

  // 获取所有唯一的 hostname
  const hostnames = [...new Set(memories.map(memory => memory.metadata.hostname))];
  console.log(`🌐 Hostnames to update: ${hostnames.join(', ')}`);

  console.log('\n📝 Note: This script will add new memories. If you need to delete old ones,');
  console.log('   you may need to manually delete them or implement a delete API endpoint.');

  let totalSuccess = 0;
  let totalFailed = 0;

  // 分批处理
  for (let i = 0; i < memories.length; i += BATCH_SIZE) {
    const { successCount, failCount } = await processBatch(memories, i);
    totalSuccess += successCount;
    totalFailed += failCount;

    // 如果不是最后一批，添加延迟
    if (i + BATCH_SIZE < memories.length) {
      console.log(`⏳ Waiting ${DELAY_BETWEEN_BATCHES}ms before next batch...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
    }
  }

  console.log('\n🎉 Update completed!');
  console.log(`📊 Summary:`);
  console.log(`  📤 Memories added: ${totalSuccess}/${memories.length}`);
  console.log(`  ❌ Failed to add: ${totalFailed}`);
  console.log(`  📈 Success rate: ${((totalSuccess / memories.length) * 100).toFixed(2)}%`);
}

// 运行脚本
main().catch(console.error);
