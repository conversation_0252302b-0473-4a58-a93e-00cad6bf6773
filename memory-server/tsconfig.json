{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "declaration": true, "sourceMap": true, "paths": {"@/*": ["./*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}