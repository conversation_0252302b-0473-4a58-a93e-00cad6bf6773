{"name": "memory-server", "version": "1.0.0", "description": "Memory server for AI agent", "main": "dist/index.js", "scripts": {"build": "rm -rf dist && npx tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/index.ts", "test": "jest", "test:api": "cd tests && node run-tests.js all", "test:search": "cd tests && node run-tests.js test-search-simple", "test:supabase": "cd tests && node run-tests.js test-supabase", "lint": "eslint src --ext .ts", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "dependencies": {"@anthropic-ai/sdk": "^0.53.0", "@google/genai": "^1.4.0", "@mistralai/mistralai": "^1.7.1", "@qdrant/js-client-rest": "^1.14.1", "@supabase/supabase-js": "^2.39.3", "@the-agent/shared": "workspace:*", "axios": "^1.6.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.18.2", "groq-sdk": "^0.23.0", "neo4j-driver": "^5.17.0", "ollama": "^0.5.16", "openai": "^5.1.1", "pg": "^8.16.0", "redis": "^5.5.6", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.11.19", "@types/pg": "^8.15.4", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "eslint": "^8.56.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}}