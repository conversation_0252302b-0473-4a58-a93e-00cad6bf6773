module.exports = {
  '*.{ts,tsx}': filenames => {
    const filteredFiles = filenames.filter(file => !file.includes('packages/telegram/'));

    if (filteredFiles.length === 0) {
      return [];
    }

    return [
      `eslint --fix ${filteredFiles.join(' ')}`,
      `prettier --write ${filteredFiles.join(' ')}`,
    ];
  },
  '*.{js,jsx,json,md}': filenames => {
    const filteredFiles = filenames.filter(file => !file.includes('packages/telegram/'));

    if (filteredFiles.length === 0) {
      return [];
    }

    return [`prettier --write ${filteredFiles.join(' ')}`];
  },
};
