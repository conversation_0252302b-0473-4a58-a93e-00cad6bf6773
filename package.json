{"name": "the-agent", "private": true, "scripts": {"build": "pnpm -r build", "dev": "pnpm -r dev", "prepare": "husky", "lint": "eslint --ext .ts,.tsx .", "lint:fix": "eslint --ext .ts,.tsx . --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "build:web": "pnpm --filter @the-agent/shared build && pnpm --filter web build", "start:web": "pnpm --filter web start", "build:landing": "pnpm --filter landing build", "start:landing": "pnpm --filter landing start", "build:memory-server": "pnpm --filter @the-agent/shared build && pnpm --filter memory-server build", "start:memory-server": "pnpm --filter memory-server start", "build:tg-bot": "pnpm --filter tg-bot build", "start:tg-bot": "pnpm --filter tg-bot start", "postinstall": "pnpm --filter @the-agent/shared build && pnpm --filter @the-agent/chat-store build"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx,json,md}": ["prettier --write"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "^3.5.3"}, "dependencies": {"openai": "^5.1.1", "zod": "^3.24.4"}}