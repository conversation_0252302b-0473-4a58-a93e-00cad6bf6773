{"name": "web", "version": "0.1.0", "private": true, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"@floating-ui/react": "^0.27.13", "@stripe/stripe-js": "^7.3.0", "@tailwindcss/typography": "^0.5.16", "@the-agent/shared": "workspace:*", "classnames": "^2.5.1", "date-fns": "^4.1.0", "dexie": "^4.0.8", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "lucide-react": "^0.507.0", "next": "15.3.0", "next-intl": "4.1.0", "react": "^18.2.0", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-markdown": "^10.1.0", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "tailwindcss": "^4.1.6"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20.17.30", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "tsc-files": "^1.1.4", "typescript": "^5"}}