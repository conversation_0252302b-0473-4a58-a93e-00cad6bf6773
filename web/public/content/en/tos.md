# Mysta Technologies, Inc. – Terms of Service

## Terms of Service Agreement

Effective Date: May 18, 2025

Last Updated: May 22, 2025

This Terms of Service Agreement (“Agreement”) constitutes a legally binding contract between you (“User,” “you,” or “your”) and Mysta Technologies, Inc., a Delaware corporation (“Mysta,” “we,” “us,” or “our”), governing your access to and use of the Mysta browser-native software extension, documentation, related materials, and services (collectively, the “Service”).

> BY INSTALLING, ACCESSING, OR USING THE SERVICE, YOU EXPRESSLY ACKNOWLEDGE THAT YOU HAVE READ, UNDERSTOOD, AND AGREED TO BE BOUND BY THIS AGREEMENT.

### 1. Definitions

- Authorized Use: Usage of the Service by individuals for internal personal, research, or commercial productivity purposes.
- Content: Any data, code, instructions, metadata, prompts, or output generated or processed through the Service.
- Local Execution: Processing that occurs exclusively within the User’s device/browser, without communication to Mysta-operated servers.

### 2. License Grant

Mysta grants you a limited, non-exclusive, non-transferable, revocable license to use the Service solely for Authorized Use and in compliance with this Agreement. All rights not expressly granted are reserved.

### 3. Restrictions

You shall not:

- Reverse engineer, decompile, disassemble, or attempt to derive the source code;
- Use the Service in violation of any applicable law, third-party rights, or contractual obligations;
- Deploy the Service in any high-risk system where failure could result in death, personal injury, or severe environmental harm;
- Circumvent or interfere with any technical or security mechanisms;
- Permit any unauthorized third party to access or use the Service.

### 4. Intellectual Property

All intellectual property rights in the Service — including software, documentation, and trademarks — remain the sole property of Mysta and its licensors.

### 5. Local-Only Data Architecture

Mysta is architected for client-side execution only:

- No collection, storage, or transmission of Personally Identifiable Information (PII);
- No access to browsing history, cookies, session tokens, login credentials, or form data;
- No tunneling, proxying, or packet capture;
- No remote telemetry, unless explicitly opted in.

### 6. Third-Party Systems and Models

If you choose to connect Mysta to external models, inference engines, or vector databases:

- Mysta bears no responsibility or liability for the data processed;
- You are solely responsible for compliance with data protection laws including the CCPA, CPRA, and VCDPA where applicable.

### 7. Disclaimers

THE SERVICE IS PROVIDED “AS IS” AND “AS AVAILABLE,” WITHOUT WARRANTIES OF ANY KIND. MYSTA DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, NON-INFRINGEMENT, ACCURACY, OR RESULTS FROM USE.

### 8. Limitation of Liability

To the maximum extent permitted by law:

- Mysta shall not be liable for any indirect, incidental, consequential, special, punitive, or exemplary damages, including loss of data, profits, business, or reputation.
- Aggregate liability under this Agreement shall not exceed $100 USD.

### 9. Indemnification

You agree to indemnify, defend, and hold harmless Mysta and its officers, directors, employees, contractors, and affiliates against any claims or liabilities arising out of:

- Your use of the Service;
- Your violation of this Agreement;
- Your use of third-party integrations or configurations.

### 10. Governing Law and Dispute Resolution

- This Agreement is governed by the laws of the State of Delaware, excluding conflict-of-law rules.
- Any dispute shall be resolved by binding arbitration under the rules of the American Arbitration Association (AAA) in New York County, NY.
- Either party may seek injunctive or equitable relief in court if needed to protect rights or confidentiality.

### 11. Termination

Mysta may terminate or suspend access immediately for any violation of this Agreement.  
Sections 4, 6, 7, 8, 9, and 10 survive termination.

---
