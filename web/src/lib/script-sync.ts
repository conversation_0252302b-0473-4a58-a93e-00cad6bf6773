export interface Script {
  code: string;
  version: number;
  updated_at: number;
  miniapp_id: number;
}

interface ExtensionMessage {
  type: string;
  action: string;
  data?: Record<string, unknown>;
  requestId?: string;
}

interface ExtensionResponse {
  success: boolean;
  data?: {
    code: string;
    version: number;
    updated_at: number;
  } | null;
  error?: string;
  requestId?: string;
}

interface ExtensionSyncEvent {
  type: 'script-updated';
  miniappId: number;
  data: {
    code: string;
    version: number;
    updated_at: number;
  };
}

class ScriptSyncManager {
  private syncListeners = new Set<(script: Script) => void>();

  constructor() {
    // Listen for messages from extension
    if (typeof window !== 'undefined') {
      window.addEventListener('message', this.handleExtensionMessage.bind(this));
    }
  }

  private handleExtensionMessage(event: MessageEvent) {
    // Only accept messages from same origin for security
    if (event.origin !== window.location.origin) {
      return;
    }

    const message = event.data;

    // Handle sync events from extension
    if (message?.type === 'FROM_EXTENSION_TO_WEB' && message.syncEvent) {
      this.handleSyncEvent(message.syncEvent);
    }
  }

  private handleSyncEvent(event: ExtensionSyncEvent) {
    // Convert extension sync event to script
    if (event.type === 'script-updated' && event.data) {
      const script: Script = {
        code: event.data.code,
        version: event.data.version,
        updated_at: event.data.updated_at,
        miniapp_id: event.miniappId,
      };

      this.notifyListeners(script);
    }
  }

  private async sendExtensionMessage(
    action: string,
    data?: Record<string, unknown>
  ): Promise<ExtensionResponse> {
    return new Promise((resolve, reject) => {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Set up timeout
      const timeout = setTimeout(() => {
        reject(new Error('Extension request timeout'));
      }, 5000); // 5 second timeout

      // Listen for response
      const handleResponse = (event: MessageEvent) => {
        if (
          event.origin === window.location.origin &&
          event.data?.type === 'FROM_EXTENSION_TO_WEB' &&
          event.data?.requestId === requestId
        ) {
          clearTimeout(timeout);
          window.removeEventListener('message', handleResponse);

          if (event.data.success !== false) {
            resolve(event.data);
          } else {
            reject(new Error(event.data.error || 'Extension request failed'));
          }
        }
      };

      window.addEventListener('message', handleResponse);

      // Send message to extension via window.postMessage
      const message: ExtensionMessage = {
        type: 'FROM_WEB_TO_EXTENSION',
        action,
        data,
        requestId,
      };

      window.postMessage(message, '*');
    });
  }

  // Check if extension is available by sending a ping
  private async isExtensionAvailable(): Promise<boolean> {
    try {
      await this.sendExtensionMessage('ping');
      return true;
    } catch {
      return false;
    }
  }

  private notifyListeners(script: Script) {
    this.syncListeners.forEach(listener => {
      try {
        listener(script);
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  // Save script and notify other contexts
  async saveScript(miniappId: number, code: string, version: number): Promise<void> {
    try {
      // Check if extension is available first
      const isExtensionAvailable = await this.isExtensionAvailable();

      if (!isExtensionAvailable) {
        throw new Error('Extension not available - script cannot be saved');
      }

      const response = await this.sendExtensionMessage('save-script', {
        miniappId,
        code,
        version,
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to save script');
      }
      // Extension will handle broadcasting sync events
    } catch (error) {
      console.error('Failed to save script via extension:', error);
      throw error;
    }
  }

  // Get script from extension storage
  async getScript(miniappId: number): Promise<Script | null> {
    try {
      // Check if extension is available first
      const isExtensionAvailable = await this.isExtensionAvailable();

      if (!isExtensionAvailable) {
        throw new Error('Extension not available');
      }

      const response = await this.sendExtensionMessage('get-script', {
        miniappId,
      });

      if (response.success && response.data) {
        return {
          code: response.data.code,
          version: response.data.version,
          updated_at: response.data.updated_at,
          miniapp_id: miniappId,
        };
      }

      return null;
    } catch (error) {
      console.error('Failed to get script via extension:', error);
      throw error;
    }
  }

  // Listen for sync events from other contexts
  addSyncListener(listener: (script: Script) => void): () => void {
    this.syncListeners.add(listener);

    return () => {
      this.syncListeners.delete(listener);
    };
  }

  // Cleanup
  destroy() {
    // Clear listeners
    this.syncListeners.clear();

    // Remove event listener
    if (typeof window !== 'undefined') {
      window.removeEventListener('message', this.handleExtensionMessage.bind(this));
    }
  }
}

// Singleton instance
export const scriptSyncManager = new ScriptSyncManager();
