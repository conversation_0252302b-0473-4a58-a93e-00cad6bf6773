import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';
import { hasLocale } from 'next-intl';

export default getRequestConfig(async ({ requestLocale }) => {
  // This typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested) ? requested : routing.defaultLocale;

  // // Ensure that the incoming locale is valid
  // if (!locale || !routing.locales.includes(locale as any)) {
  //   locale = routing.defaultLocale;
  // }

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default,
  };
});
