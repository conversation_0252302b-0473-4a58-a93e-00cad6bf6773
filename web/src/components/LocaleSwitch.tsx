'use client';

import { usePathname, useRouter } from '@/i18n/navigation';
import { routing } from '@/i18n/routing';
import { useClick, useDismiss, useFloating, useInteractions } from '@floating-ui/react';
import classNames from 'classnames';
import { Check, ChevronDown, Globe } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { useState } from 'react';

export const LocaleSwitch = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const selectedLocale = useLocale();
  const t = useTranslations('locale');
  const router = useRouter();
  const pathname = usePathname();

  // Menu Popover
  const { refs, floatingStyles, context } = useFloating({
    open: menuOpen,
    onOpenChange: setMenuOpen,
    placement: 'bottom-end',
  });
  const menuClick = useClick(context, {});
  const menuDismiss = useDismiss(context);
  const { getReferenceProps, getFloatingProps } = useInteractions([menuClick, menuDismiss]);

  const locales = routing.locales;

  const handleLocaleChange = (newLocale: string) => {
    // Set cookie on client side
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=********; SameSite=Lax`;

    router.replace(pathname, { locale: newLocale });
  };

  return (
    <div>
      <div
        ref={refs.setReference}
        {...getReferenceProps()}
        className="flex items-center gap-1 cursor-pointer"
      >
        <Globe className="w-4 h-4" />

        <div className="text-sm">{t(selectedLocale)}</div>

        <ChevronDown className={classNames('w-4 h-4', menuOpen && 'rotate-180')} />
      </div>

      {menuOpen && (
        <div
          ref={refs.setFloating}
          style={floatingStyles}
          {...getFloatingProps()}
          className="bg-white rounded-md shadow-md px-0 py-1 w-40 flex flex-col items-stretch mt-2"
        >
          {locales.map(locale => (
            <div
              key={locale}
              className={classNames(
                'flex items-center justify-between cursor-pointer px-3 py-2',
                selectedLocale === locale ? 'bg-[var(--blue)]/20' : 'hover:bg-gray-100'
              )}
              onClick={() => handleLocaleChange(locale)}
            >
              <div>{t(locale)}</div>

              {selectedLocale === locale && <Check className="w-4 h-4" />}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
