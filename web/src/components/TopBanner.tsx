'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useTranslations } from 'next-intl';

const BANNER_STORAGE_KEY = 'mysta-banner-closed';

export default function TopBanner() {
  const t = useTranslations();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const bannerClosed = localStorage.getItem(BANNER_STORAGE_KEY);
    setIsVisible(!bannerClosed);
  }, []);

  useEffect(() => {
    if (isVisible) {
      document.body.style.paddingTop = '52px';
    } else {
      document.body.style.paddingTop = '0';
    }

    return () => {
      document.body.style.paddingTop = '0';
    };
  }, [isVisible]);

  const handleClose = () => {
    setIsVisible(false);
    localStorage.setItem(BANNER_STORAGE_KEY, 'true');
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="left-0 w-full z-10 bg-blue-600 text-white">
      <div className="max-w-7xl mx-auto px-4 md:px-6 py-1 md:py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 flex-1">
            <span className="text-md">{t('banner.betaAnnouncement')}</span>
            <a
              href="https://t.me/mysta_ai"
              target="_blank"
              rel="noopener noreferrer"
              className="text-white underline hover:text-blue-200 transition-colors text-md whitespace-nowrap"
            >
              {t('banner.joinTelegram')}
            </a>
          </div>

          <button
            onClick={handleClose}
            className="ml-4 p-1 rounded-full hover:bg-white/20 transition-colors cursor-pointer"
            aria-label={t('banner.closeBanner')}
          >
            <X size={20} />
          </button>
        </div>
      </div>
    </div>
  );
}
