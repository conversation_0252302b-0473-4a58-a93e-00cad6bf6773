'use client';

import { useAuth } from '@/contexts/AuthContext';
import { usePathname } from '@/i18n/navigation';
import Image from 'next/image';
import { LocaleSwitch } from './LocaleSwitch';
import { useTranslations } from 'next-intl';
import TopBanner from './TopBanner';

export const Header = () => {
  const { signOut } = useAuth();
  const pathname = usePathname();
  const t = useTranslations('nav');

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <TopBanner />

      <header className="h-16 bg-white dark:bg-gray-800 shadow ">
        <div className="max-w-7xl mx-auto px-4 md:px-6 flex justify-between items-center h-full">
          <div className="flex items-center">
            <Image src="/mysta-logo-brand-beta.png" alt="Mysta Logo" width={100} height={30} />
          </div>

          <div className="flex items-center gap-4">
            <LocaleSwitch />

            {pathname.includes('/profile') && (
              <button
                onClick={signOut}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-100 rounded-md cursor-pointer"
              >
                {t('signOut')}
              </button>
            )}
          </div>
        </div>
      </header>
    </div>
  );
};
