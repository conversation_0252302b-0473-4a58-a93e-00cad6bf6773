'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Image from 'next/image';
import { RefreshCw } from 'lucide-react';
import { PaymentModal } from './PaymentModal';
import { CouponCodeModal } from './CouponCodeModal';
import { CreditsCharts } from './CreditsCharts';
import { formatCurrency } from '@/lib/utils';
import { useRouter } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

export default function ProfilePage() {
  const t = useTranslations();
  const { user, loading, rotateApiKey, refreshUserData } = useAuth();
  const [isCopied, setIsCopied] = useState(false);
  const [isRotating, setIsRotating] = useState(false);
  const router = useRouter();
  const [buyCreditsOpen, setBuyCreditsOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [redeemCouponOpen, setRedeemCouponOpen] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
    }
  }, [user, loading, router]);

  const handleCopyApiKey = async () => {
    if (user?.apiKey) {
      await navigator.clipboard.writeText(user.apiKey);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    }
  };

  const handleRotateApiKey = async () => {
    if (user) {
      setIsRotating(true);
      try {
        await rotateApiKey();
      } catch (error) {
        console.error('Error rotating API key:', error);
      } finally {
        setIsRotating(false);
      }
    }
  };

  const formatApiKeyForDisplay = (apiKey: string) => {
    if (!apiKey || apiKey.length < 8) return apiKey;
    const start = apiKey.slice(0, 4);
    const end = apiKey.slice(-4);
    return `${start}••••••••••••••••••••••••${end}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
      </div>
    );
  }

  // Don't render the page if there's no user and we're not loading
  if (!user) {
    return null; // This will show nothing while the redirect happens in the useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pt-16">
      <main className="max-w-7xl mx-auto px-4 md:px-6 py-4 md:py-6">
        {/* Top Section - Two-column layout */}
        <div className="flex flex-col lg:flex-row gap-4 md:gap-6 mb-4 md:mb-6">
          {/* Left Column - User Profile and Credits Header in a single card */}
          <div className="lg:w-1/3 bg-white dark:bg-gray-800 shadow-lg rounded-lg">
            <div className="flex flex-col h-full">
              {/* User Profile Section - 1/3 height */}
              <div className="p-6 relative" style={{ height: '30%' }}>
                <div className="flex flex-col items-center">
                  {user?.photoURL ? (
                    <div className="w-20 h-20 overflow-hidden rounded-full mb-3">
                      <Image
                        src={user.photoURL}
                        alt="User Profile"
                        width={80}
                        height={80}
                        className="rounded-full"
                      />
                    </div>
                  ) : (
                    <div className="w-20 h-20 flex items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700 mb-3">
                      <span className="text-3xl text-gray-500 dark:text-gray-400">
                        {user?.displayName?.charAt(0) || t('profile.defaultUserName').charAt(0)}
                      </span>
                    </div>
                  )}

                  <h3 className="text-xl font-medium text-gray-900 dark:text-white text-center">
                    {user?.displayName || t('profile.defaultUserName')}
                  </h3>

                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 text-center">
                    {user?.email}
                  </p>
                </div>
              </div>

              {/* Credits Header Card - 2/3 height with background image */}
              <div className="p-4 flex-grow" style={{ height: '70%' }}>
                <div className="relative rounded-lg overflow-hidden h-full">
                  {/* Background image for Credits section only */}
                  <div
                    className="absolute inset-0 bg-cover bg-center"
                    style={{ backgroundImage: 'url(/dashboard-bg.png)' }}
                  ></div>

                  <div className="relative z-10 h-full flex flex-col p-6">
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="text-lg font-medium text-white">{t('profile.credits')}</h3>
                      <button
                        onClick={async () => {
                          setIsRefreshing(true);
                          try {
                            await refreshUserData();
                          } catch (error) {
                            console.error('Error refreshing data:', error);
                          } finally {
                            setIsRefreshing(false);
                          }
                        }}
                        className="text-white hover:text-gray-200 transition-colors cursor-pointer"
                        disabled={isRefreshing}
                      >
                        <RefreshCw size={18} className={`${isRefreshing ? 'animate-spin' : ''}`} />
                      </button>
                    </div>

                    <div className="flex-grow flex flex-col justify-center items-center">
                      <p className="text-4xl font-bold text-white mb-6">
                        {formatCurrency(user.balance, { maximumFractionDigits: 2 })}
                      </p>
                      <div>
                        <div className="flex space-x-3 w-full">
                          {/* <button
                          onClick={() => setBuyCreditsOpen(true)}
                          className="flex-1 px-4 py-2 text-sm font-medium text-gray-800 bg-white rounded-md hover:bg-gray-200 transition-colors cursor-pointer"
                        >
                          Recharge
                        </button> */}
                          <button
                            onClick={() => setRedeemCouponOpen(true)}
                            className="flex-1 px-4 py-2 text-sm font-medium text-white bg-gray-800 bg-opacity-50 border border-gray-600 rounded-md hover:opacity-70 transition-colors cursor-pointer"
                          >
                            {t('profile.redeem')}
                          </button>
                        </div>

                        <div className="mt-3 flex-1 text-white">
                          <span className="text-sm">
                            {t.rich('banner.announcementShort', {
                              join: chunks => (
                                <a
                                  href="https://t.me/mysta_ai"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="underline transition-colors text-md whitespace-nowrap"
                                >
                                  {chunks}
                                </a>
                              ),
                            })}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Right Column - API Key and Data Sources with adjusted heights */}
          <div className="lg:w-2/3 flex flex-col h-full gap-4">
            {/* API Key Section - 1/3 height */}
            <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden h-full">
              <div className="border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                    {t('profile.apiKey.title')}
                  </h3>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                  {t('profile.apiKey.description')}
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="relative flex-grow">
                    <input
                      type="text"
                      readOnly
                      value={
                        user?.apiKeyEnabled
                          ? formatApiKeyForDisplay(user?.apiKey || '')
                          : '••••••••••••••••••••••••'
                      }
                      className={`w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md ${
                        user?.apiKeyEnabled
                          ? 'bg-gray-50 dark:bg-gray-700'
                          : 'bg-gray-100 dark:bg-gray-800'
                      } text-gray-900 dark:text-gray-100`}
                      disabled={!user?.apiKeyEnabled}
                    />
                    {user?.apiKeyEnabled && (
                      <button
                        onClick={handleCopyApiKey}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 cursor-pointer"
                        aria-label="Copy API key"
                      >
                        {isCopied ? (
                          <span className="text-green-500">{t('profile.apiKey.copied')}</span>
                        ) : (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                            />
                          </svg>
                        )}
                      </button>
                    )}
                  </div>
                  <button
                    onClick={handleRotateApiKey}
                    disabled={isRotating || !user?.apiKeyEnabled}
                    className={`px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:opacity-70 transition-opacity cursor-pointer ${
                      isRotating || !user?.apiKeyEnabled ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {isRotating ? t('profile.apiKey.rotating') : t('profile.apiKey.rotateKey')}
                  </button>
                </div>
              </div>
            </div>
            {/* Data Sources Section - 2/3 height */}
            <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden h-full">
              <div className="p-4 md:p-6">
                <CreditsCharts />
              </div>
            </div>
          </div>
        </div>
      </main>

      <footer className="max-w-7xl mx-auto px-4 md:px-6 pb-4 md:pb-6 text-center text-sm text-gray-500 dark:text-gray-400">
        &copy; {new Date().getFullYear()} Mysta. {t('profile.allRightsReserved')}
      </footer>

      {/* Modals */}
      <PaymentModal isOpen={buyCreditsOpen} onClose={() => setBuyCreditsOpen(false)} />
      <CouponCodeModal isOpen={redeemCouponOpen} onClose={() => setRedeemCouponOpen(false)} />
    </div>
  );
}
