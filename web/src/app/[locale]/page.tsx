'use client';

import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Link, useRouter } from '@/i18n/navigation';

export default function Home() {
  const { user, loading, signInWithGoogle } = useAuth();
  const router = useRouter();
  const t = useTranslations();

  useEffect(() => {
    if (user && !loading) {
      router.push('/profile');
    }
  }, [user, loading, router]);

  if (user || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col items-center justify-center min-h-screen"
      style={{
        backgroundImage: 'url(/dashboard-bg.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
    >
      <div className="w-full max-w-[560px] bg-white rounded-[20px] p-12 shadow-lg">
        <div className="flex flex-col items-center mb-8">
          <div className="flex items-center gap-2.5">
            <Image src="/mysta-logo-brand-beta.png" alt="Mysta Logo" width={150} height={50} />
          </div>
          <p className="mt-6 text-lg leading-8 font-bold">{t('auth.signInDescription')}</p>
        </div>

        <div className="space-y-4">
          <button
            onClick={signInWithGoogle}
            className="flex items-center justify-center w-full gap-3 py-3 px-6 text-white bg-black border border-gray-300 rounded-md hover:opacity-70 transition-colors cursor-pointer"
          >
            <Image
              src="/google-logo.svg"
              alt="Google logo"
              width={20}
              height={20}
              className="w-5 h-5"
            />
            <span>{t('auth.signInWithGoogle')}</span>
          </button>
        </div>

        <div className="mt-6 text-center text-sm text-gray-500 whitespace-nowrap">
          {t.rich('auth.agreementText', {
            termsLink: chunks => (
              <Link href="/tos" target="_blank" className="text-blue-500 hover:text-blue-600">
                {chunks}
              </Link>
            ),
            privacyLink: chunks => (
              <Link href="/privacy" target="_blank" className="text-blue-500 hover:text-blue-600">
                {chunks}
              </Link>
            ),
          })}
        </div>
      </div>
    </div>
  );
}
