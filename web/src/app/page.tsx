import { redirect } from 'next/navigation';
import { routing } from '@/i18n/routing';
import { cookies } from 'next/headers';

// This page only renders when the app is built statically (output: 'export')
export default async function RootPage() {
  const cookieStore = await cookies();
  const locale = cookieStore.get('NEXT_LOCALE')?.value;

  if (locale) {
    redirect(`/${locale}`);
  }

  // Redirect to the default locale
  redirect(`/${routing.defaultLocale}`);
}
