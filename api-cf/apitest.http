### Variables
@baseUrl = http://localhost:8787
@apiKey = d6e46688-0c76-4b3b-9a36-00f83bac6f07
@token = "eyJhbGciOiJSUzI1NiIsImtpZCI6IjQ3YWU0OWM0YzlkM2ViODVhNTI1NDA3MmMzMGQyZThlNzY2MWVmZTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.u9KVjbmWNJUZfM-n8Lks0tJ9YTUKycIlH9e7AcH0g0RlJkjUkz_8oYxsxsZGIRaksyqeryidY2hBbMpHJqZcKpQ-Oa1VvKEmMYCtVYb6cHkjaVmhsKiMCa7iaAVUxi98JbD6UlKYDi1EGj4LudiVpiAw6A1edX30zhB56KYlob09hdyMlK-fXVOtTNZ3P7-2ICO0VgIOSy_WgNYP7JqlPpWNuMm04O0vm81s0kzhXlf1ODjnmGUsRjIQqpWWSlZrrv5uEilVbDT-6JVKUYzjbVW51M0269ItWJrvh0ChSonKsyrsaeyY3xzsDplBGU8cIpRVzdCpPgSFv67QLzJHyQ"
@botApiKey = bot-mystaai-20250714-123456789abc

### Health Check
GET {{baseUrl}}/health
Content-Type: application/json

### Test Multiple Dashes in Campaign Name
POST {{baseUrl}}/v1/gen_coupon_code
Content-Type: application/json
x-api-key: {{botApiKey}}

{
  "campaign": "DEV",
  "credits": 1000000
}

### Redeem Black Friday Code (Should Succeed)
POST {{baseUrl}}/v1/user/redeem_coupon_code
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "code": "DEV-246A3B"
}

### Generate Another Black Friday Code
POST {{baseUrl}}/v1/gen_coupon_code
Content-Type: application/json
x-api-key: {{botApiKey}}

{
  "campaign": "DEV",
  "credits": 2000000
}

### Try to Redeem Another Black Friday Code (Should Fail)
POST {{baseUrl}}/v1/user/redeem_coupon_code
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "code": "DEV-246A3B"
}

### Get User Balance
GET {{baseUrl}}/v1/user/balance
Content-Type: application/json
x-api-key: {{apiKey}}

### Get Credit Daily
GET {{baseUrl}}/v1/user/credit_daily
Content-Type: application/json
x-api-key: {{apiKey}}

### Get User Info
GET {{baseUrl}}/v1/user
Content-Type: application/json
x-api-key: {{apiKey}}

### Rotate API Key
POST {{baseUrl}}/v1/user/rotate_api_key
Content-Type: application/json
x-api-key: {{apiKey}}

### Toggle API Key Enabled
POST {{baseUrl}}/v1/user/toggle_api_key_enabled
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "enabled": true
}

### Clear User
POST {{baseUrl}}/v1/user/clear
Content-Type: application/json
Authorization: Bearer {{token}}

### Create Conversation
POST {{baseUrl}}/v1/conversation/create
Content-Type: application/json
x-api-key: {{apiKey}}

### List Conversations
GET {{baseUrl}}/v1/conversation/list
Content-Type: application/json
x-api-key: {{apiKey}}

### Delete Conversation
# Replace {conversationId} with an actual conversation ID
@conversationId = 1747056387705
POST {{baseUrl}}/v1/conversation/delete?conversationId={{conversationId}}
Content-Type: application/json
x-api-key: {{apiKey}}

### Save Message with Memory Processing
POST {{baseUrl}}/v1/message/save
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "message": {
    "id": 1747563300,
    "conversation_id": 1747384379706,
    "role": "user",
    "content": "Hello, this is a test message for memory processing",
    "tool_calls": [],
    "tool_call_id": "",
    "name": ""
  },
  "top_k_related": 3,
  "threshold": 0.7
}

### Save Message with Tool Calls
POST {{baseUrl}}/v1/message/save
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "message": {
    "id": 1747563301,
    "conversation_id": 1747384379706,
    "role": "assistant",
    "content": "I'll help you with that",
    "tool_calls": [
      {
        "id": "call_123",
        "type": "function",
        "function": {
          "name": "search",
          "arguments": "{\"query\":\"test query\"}"
        }
      }
    ],
    "tool_call_id": "call_123",
    "name": ""
  },
  "top_k_related": 3,
  "threshold": 0.7
}

### Chat Completion with Memory
POST {{baseUrl}}/v1/chat/completions
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful AI assistant."
    },
    {
      "role": "user",
      "content": "What can you do?"
    }
  ],
  "model": "deepseek-chat",
  "use_memory": true
}

### Chat Completion with Streaming
POST {{baseUrl}}/v1/chat/completions
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful AI assistant."
    },
    {
      "role": "user",
      "content": "What can you do?"
    }
  ],
  "model": "deepseek-chat",
  "temperature": 0.7,
  "max_tokens": 500,
  "stream": true
}

### Stripe Checkout
POST {{baseUrl}}/v1/stripe/checkout
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "amount": 10
}

### Stripe Webhook (模拟Stripe事件)
POST {{baseUrl}}/v1/stripe/webhook
Content-Type: application/json
stripe-signature: test_signature

{
  "id": "evt_test123456",
  "object": "event",
  "api_version": "2023-10-16",
  "created": 1677868469,
  "type": "checkout.session.completed",
  "data": {
    "object": {
      "id": "cs_test_123456",
      "object": "checkout.session",
      "amount_subtotal": 1000,
      "customer_email": "<EMAIL>",
      "metadata": {
        "orderId": "order_123456",
        "userId": "user_123456",
        "userEmail": "<EMAIL>"
      }
    }
  }
}

### Telegram Stats
GET {{baseUrl}}/v1/tg/stats
Content-Type: application/json
x-api-key: {{apiKey}}

### Save Site Message
POST {{baseUrl}}/v1/message/save_site
Content-Type: application/json
x-api-key: {{apiKey}}

{
    "site": "https://x.com",
    "faqs": [
        {
            "question": "How to find comment list on https://x.com/{user_id}/status/{post_id}",
            "answer": "To find comment list, use the WebToolkit_buildElementMap tool. You'll need the fullXPath of the comment field. WebToolkit_buildElementMap({\"fullXPath\":\"/html/body/div[1]/div/div/div[2]/main/div/div/div/div[1]/div/section/div\"})"
        }
    ]
}

### Search Site Message
POST {{baseUrl}}/v1/message/search_site
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "site": "https://www.example.com",
  "user_message": "How can I sign up for an account?"
}

### Get My Chat
GET {{baseUrl}}/v1/tg/get_my_chat
Content-Type: application/json
x-api-key: {{apiKey}}

### Get Telegram Dialogs
GET {{baseUrl}}/v1/tg/get_dialogs?limit=10&offset=0&chat_title=test&is_public=true&is_free=true&status=active&sort_by=updated_at&sort_order=desc
Content-Type: application/json
x-api-key: {{apiKey}}

### Get Telegram Messages
GET {{baseUrl}}/v1/tg/get_messages?chat_id=123456&limit=50&offset=0&message_text=test&sender_id=user123&sender_username=testuser&start_timestamp=**********&end_timestamp=**********&sort_by=message_timestamp&sort_order=desc
Content-Type: application/json
x-api-key: {{apiKey}}

### Search Telegram Messages
GET {{baseUrl}}/v1/tg/search_messages?query=hello world&chat_id=123456&top_k=10&message_range=2&threshold=0.7&is_public=true&is_free=true
Content-Type: application/json
x-api-key: {{apiKey}}

### Sync Telegram Chat
POST {{baseUrl}}/v1/tg/sync_chat
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "chats": [
  {
    "chat_id": "-202505162001",
    "chat_title": "Test Chat",
    "chat_type": "group",
    "is_public": true,
    "is_free": true,
    "subscription_fee": 0
  },
  {
    "chat_id": "-202505162002",
    "chat_title": "Test Chat 2",
    "chat_type": "group"
  }
  ]
}

### Sync Telegram Messages
POST {{baseUrl}}/v1/tg/sync_messages
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "messages": [
    {
      "chat_id": "-202505150125",
      "message_id": "msg1",
      "message_text": "Hello, this is a test message",
      "message_timestamp": **********,
      "sender_id": "user123",
      "sender_username": "testuser",
      "sender_firstname": "Test",
      "sender_lastname": "User"
    },
    {
      "chat_id": "-202505150126",
      "message_id": "msg2",
      "message_text": "This is a reply to the test message",
      "message_timestamp": 1704067300,
      "sender_id": "user456"
    }
  ]
}

### Test list conversations
GET http://localhost:8787/api/conversations

### Test Save Miniapp
POST {{baseUrl}}/v1/miniapp/save
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "miniapp": {
    "id": 1747563300,
    "name": "Test Miniapp",
    "conversation_id": 1747384379706,
    "history": []
  }
}

### Test Update Miniapp (name and status)
POST {{baseUrl}}/v1/miniapp/update
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "id": 1747563300,
  "name": "Test Miniapp (updated)",
  "status": "deployed"
}

### Test Update Miniapp (installation and history)
POST {{baseUrl}}/v1/miniapp/update
Content-Type: application/json
x-api-key: {{apiKey}}

{
  "id": 1747563300,
  "installation": {
    "code": "console.log('v1.0.0');",
    "changelogs": "Initial deployment",
    "deployed_at": **********
  },
  "status": "deployed"
}