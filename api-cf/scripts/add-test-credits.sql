-- Insert a test user if not exists
INSERT OR IGNORE INTO users (id, user_email, api_key, api_key_enabled, balance)
VALUES (
    'local-xiangnuan',
    '<EMAIL>',
    '0ba127f3-3e57-4069-8dea-5b87e0eb207d',
    1,
    10000000  -- 10 million credits
);

-- Add credit history record
INSERT INTO credit_history (user_id, tx_credits, tx_type, tx_reason, model)
VALUES (
    'local-xiangnuan',
    10000000,
    'credit',
    'Test credits',
    'test'
); 