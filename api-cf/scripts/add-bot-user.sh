#!/bin/bash

# Execute SQL script locally
echo "Adding bot user to local database..."
npx wrangler d1 execute mysta-staging --file=./scripts/add-bot-user.sql --local

# Execute SQL script on remote D1
echo "Adding bot user to remote database..."
npx wrangler d1 execute mysta-staging --file=./scripts/add-bot-user.sql --remote

# Verify the insertions
echo "Verifying local bot user insertion..."
npx wrangler d1 execute mysta-staging --command="SELECT id, user_email, api_key, api_key_enabled, balance FROM users WHERE id = 'bot-mystaai';" --local

echo "Verifying remote bot user insertion..."
npx wrangler d1 execute mysta-staging --command="SELECT id, user_email, api_key, api_key_enabled, balance FROM users WHERE id = 'bot-mystaai';" --remote 