-- Create users table
CREATE TABLE IF NOT EXISTS users(
    id TEXT PRIMARY KEY,
    user_email TEXT,
    api_key TEXT NOT NULL,
    api_key_enabled INTEGER NOT NULL DEFAULT 1 CHECK(api_key_enabled IN (0, 1)),
    balance INTEGER NOT NULL DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders(
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    amount INTEGER NOT NULL,
    currency TEXT NOT NULL DEFAULT 'usd',
    stripe_session_id TEXT,
    status TEXT NOT NULL CHECK(status IN ('pending', 'completed', 'cancelled', 'failed', 'finalized')) DEFAULT('pending'),
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON>EY(user_id) REFERENCES users(id)
);

-- Create credit history table
CREATE TABLE IF NOT EXISTS credit_history(
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    tx_credits INTEGER NOT NULL,
    tx_type TEXT NOT NULL CHECK(tx_type IN ('credit', 'debit')),
    tx_reason TEXT NOT NULL,
    model TEXT,
    order_id TEXT,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id),
    FOREIGN KEY(order_id) REFERENCES orders(id)
);

-- Create coupon codes table
CREATE TABLE IF NOT EXISTS coupon_codes(
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT,
    code TEXT NOT NULL UNIQUE,
    credits INTEGER NOT NULL,
    max_uses INTEGER NOT NULL DEFAULT 1,
    used_count INTEGER NOT NULL DEFAULT 0,
    is_active INTEGER NOT NULL DEFAULT 1 CHECK(is_active IN (0, 1)),
    user_whitelist TEXT DEFAULT NULL,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expired_at TEXT,
    FOREIGN KEY(user_id) REFERENCES users(id)
);

-- Create coupon redemptions table
CREATE TABLE IF NOT EXISTS coupon_redemptions(
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    coupon_code TEXT NOT NULL,
    redeemed_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id),
    FOREIGN KEY(coupon_code) REFERENCES coupon_codes(code),
    UNIQUE(user_id, coupon_code)
);

-- Create agent conversations table
CREATE TABLE IF NOT EXISTS agent_conversations(
    id INTEGER PRIMARY KEY,
    status TEXT DEFAULT 'active',
    last_selected_at INTEGER,
    title TEXT
);

-- Create agent messages table
CREATE TABLE IF NOT EXISTS agent_messages(
    id INTEGER PRIMARY KEY,
    conversation_id INTEGER NOT NULL,
    role TEXT NOT NULL,
    content TEXT NOT NULL,
    tool_calls TEXT,
    tool_call_id TEXT,
    name TEXT,
    FOREIGN KEY (conversation_id) REFERENCES agent_conversations(id)
);

-- Create telegram dialogs table
CREATE TABLE IF NOT EXISTS telegram_dialogs (
    id TEXT PRIMARY KEY,
    chat_id TEXT NOT NULL,
    chat_type TEXT NOT NULL,
    chat_title TEXT NOT NULL,
    is_public BOOLEAN NOT NULL DEFAULT false,
    is_free BOOLEAN NOT NULL DEFAULT true,
    subscription_fee NUMERIC NOT NULL DEFAULT 0,
    last_synced_at TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(chat_id)
);

-- Create telegram messages table
CREATE TABLE IF NOT EXISTS telegram_messages (
    id TEXT PRIMARY KEY,
    chat_id TEXT NOT NULL,
    message_id TEXT NOT NULL,
    message_text TEXT NOT NULL,
    message_timestamp INTEGER NOT NULL,
    sender_id TEXT NOT NULL,
    sender_username TEXT,
    sender_firstname TEXT,
    sender_lastname TEXT,
    embedding_id TEXT,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(chat_id, message_id),
    FOREIGN KEY(chat_id) REFERENCES telegram_dialogs(chat_id)
);

-- Create knowledge_base table for site-specific Q&A storage
CREATE TABLE IF NOT EXISTS knowledge_base(
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    faq_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    site TEXT NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
); 