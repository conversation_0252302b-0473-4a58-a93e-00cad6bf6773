interface Env {
  // variables
  OPENROUTER_API_KEY: string;
  DEEPINFRA_API_KEY: string;
  STRIPE_PUBLIC_KEY: string;
  STRIPE_PRIVATE_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  MYSTA_PUBLIC_DOMAIN: string;
  MEMORY_SERVICE_URL: string;
  MEMORY_SERVICE_API_KEY: string;
  SAVE_SITE_EMAIL_WHITELIST?: string;
  BOT_API_KEY?: string;
  SUPABASE_URL: string;
  SUPABASE_KEY: string;
  // bindings
  DB: D1Database;
}
