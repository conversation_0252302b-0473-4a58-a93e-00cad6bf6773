import {
  createMessageStore,
  createConversationStore,
  createTemplateStore,
  createMiniAppStore,
} from '@the-agent/chat-store/workers';

// create store instance factory function - only support Supabase
export function createStores(env: any) {
  const config = {
    type: 'supabase' as const,
    supabaseUrl: env.SUPABASE_URL,
    supabaseKey: env.SUPABASE_KEY,
  };

  return {
    messageStore: createMessageStore(config),
    conversationStore: createConversationStore(config),
    templateStore: createTemplateStore(config),
    miniappStore: createMiniAppStore(config),
  };
}
