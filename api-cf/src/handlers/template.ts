import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { GatewayServiceError } from '../types/service';
import {
  GetTemplatesRequestSchema,
  GetTemplatesResponseSchema,
  TemplateRegisterRequestSchema,
  TemplateRegisterResponseSchema,
} from '@the-agent/shared';
import { createStores } from '../services/chat-store';

export class RegisterTemplate extends OpenAPIRoute {
  schema = {
    request: {
      query: TemplateRegisterRequestSchema,
    },
    responses: {
      '200': {
        description: 'Search memory successfully',
        content: {
          'application/json': {
            schema: TemplateRegisterResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    if (!userId) {
      throw new GatewayServiceError(401, 'Unauthorized');
    }

    const body = await c.req.json();
    const { templateStore } = createStores(c.env);
    const result = await templateStore.registerTemplate(userId, body);
    return c.json(result, 200);
  }
}

export class GetTemplates extends OpenAPIRoute {
  schema = {
    request: {
      query: GetTemplatesRequestSchema,
    },
    responses: {
      '200': {
        description: 'Search memory successfully',
        content: {
          'application/json': {
            schema: GetTemplatesResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    if (!userId) {
      throw new GatewayServiceError(401, 'Unauthorized');
    }

    const { templateStore } = createStores(c.env);
    const { domain } = c.req.query();
    const result = await templateStore.getTemplates(userId, { domain });
    return c.json(result, 200);
  }
}
