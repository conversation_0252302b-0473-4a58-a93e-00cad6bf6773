import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { getUserBalance, deductUserCredits } from '../d1/user';
import {
  ChatCompletionCreateParam,
  ChatCompletionCreateParamSchema,
  ChatCompletionResponseSchema,
  ChatMessage,
  TokenUsage,
} from '@the-agent/shared';
import { calculateCredits } from '../utils/creditCalculator';
import { OPENROUTER_API_URL } from '../utils/common';
import { createOpenAIClient, OpenAIClient } from '../utils/openai';

const DEFAULT_MODEL = 'google/gemini-2.5-pro-preview';

export class ChatCompletions extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: ChatCompletionCreateParamSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Chat completion response',
        content: {
          'application/json': {
            schema: ChatCompletionResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const env = c.env;
    const userId = c.get('userId');
    const credits = await getUserBalance(env, userId);
    if (credits <= 0) {
      return c.json(
        {
          error: {
            message: 'Insufficient credits. Please add more credits to your account.',
            type: 'insufficient_credits',
            param: null,
            code: 'insufficient_credits',
          },
        },
        402
      );
    }

    // Create OpenAI client
    const params = await c.req.json();
    params.model = DEFAULT_MODEL;

    const client = createOpenAIClient(env.OPENROUTER_API_KEY, OPENROUTER_API_URL);

    // Handle streaming response
    if (params.stream) {
      const upstreamAbort = new AbortController();
      params.stream_options = { include_usage: true, signal: upstreamAbort.signal };
      const upstream = await client.streamChatCompletion(params);

      // If upstream isn’t SSE, return it as JSON/text, not as SSE
      const ct = upstream.headers.get('content-type') || '';
      if (!upstream.ok || !ct.startsWith('text/event-stream')) {
        const body = await upstream.text();
        return new Response(body, {
          status: upstream.status,
          headers: {
            'Content-Type': ct.includes('json') ? 'application/json' : 'text/plain',
            'Access-Control-Allow-Origin': '*',
            'Cache-Control': 'no-store',
          },
        });
      }

      const { readable, writable } = new TransformStream();
      const writer = writable.getWriter();
      const reader = upstream.body!.getReader();

      let completedContent = '';
      let tokenUsage: TokenUsage | null = null;
      let buffer = '';
      let abortedByClient = false;

      // Detect downstream abort (browser/client closed connection)
      const onClientAbort = () => {
        abortedByClient = true;
        try {
          upstreamAbort.abort();
        } catch {}
        try {
          reader.cancel();
        } catch {}
        // No point writing an SSE error now; client is gone.
      };

      // Hono/CF Workers request signal
      const signal = c.req.raw.signal;
      signal?.addEventListener('abort', onClientAbort, { once: true });

      // Helper to parse frames to capture usage/content (don’t emit, just parse)
      const processBuffer = (buf: string): string => {
        if (!buf) return '';
        const lines = buf.split('\n');
        const remainder = lines.pop() || '';
        for (const line of lines) {
          if (!line.startsWith('data: ')) continue;
          const payload = line.slice(6).trim();
          if (!payload || payload === '[DONE]') continue;
          try {
            const data = JSON.parse(payload);
            if (data.choices?.[0]?.delta?.content) {
              completedContent += data.choices[0].delta.content;
            }
            if (data.usage) tokenUsage = data.usage;
          } catch {}
        }
        return remainder;
      };

      // Pump bytes downstream; also parse a shadow copy to catch usage
      const pump = (async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            // Try to forward. If client is gone, this throws.
            await writer.write(value);
            buffer += new TextDecoder().decode(value);
            buffer = processBuffer(buffer);
          }
          // flush leftovers
          processBuffer(buffer);
        } catch {
          // If client aborted, writer.write likely threw; nothing else to do.
          if (!abortedByClient) {
            // If it's a real upstream error and client is still connected, send an SSE error frame.
            try {
              const msg = `data: ${JSON.stringify({ error: { message: 'Stream error', type: 'server_error' } })}\n\n`;
              await writer.write(new TextEncoder().encode(msg));
            } catch {}
          }
        } finally {
          try {
            await writer.close();
          } catch {}
          try {
            reader.releaseLock();
          } catch {}
        }
      })();

      // Do accounting in the background
      c.executionCtx.waitUntil(
        pump.then(async () => {
          try {
            if (!tokenUsage) {
              // If user aborted before final usage frame, choose a policy:
              // (A) bill prompt only:
              const promptText = params.messages
                .map((m: ChatMessage) => (typeof m.content === 'string' ? m.content : ''))
                .join(' ');
              tokenUsage = {
                prompt_tokens: estimateTokens(promptText),
                completion_tokens: abortedByClient ? 0 : estimateTokens(completedContent),
                total_tokens: 0, // will set below
              };
              tokenUsage.total_tokens = tokenUsage.prompt_tokens + tokenUsage.completion_tokens;
            }
            const { cost } = calculateCredits(params.model, tokenUsage);
            await deductUserCredits(c.env, userId, cost.totalCostWithMultiplier, params.model);
          } catch {
            // Credit deduction failed silently
          }
        })
      );

      return new Response(readable, {
        status: 200,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-transform, no-cache',
          Connection: 'keep-alive',
          'Access-Control-Allow-Origin': '*',
        },
      });
    } else {
      const result = await processChatCompletion(env, userId, client, params);
      return c.json(result as Record<string, unknown>, 200);
    }
  }
}

async function processChatCompletion(
  env: Env,
  userId: string,
  client: OpenAIClient,
  params: ChatCompletionCreateParam
) {
  const response = await client.createChatCompletion(params);

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
  }

  const result = (await response.json()) as {
    usage: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  } & Record<string, unknown>;

  if (!result.usage) {
    return result;
  }

  const { cost } = calculateCredits(params.model, result.usage);
  await deductUserCredits(env, userId, cost.totalCostWithMultiplier, params.model);
  return result;
}

function estimateTokens(text: string) {
  const cjkRegex =
    /[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/;
  const hasCJK = cjkRegex.test(text);
  const charsPerToken = hasCJK ? 2 : 4;
  return Math.max(1, Math.ceil(text.length / charsPerToken));
}
