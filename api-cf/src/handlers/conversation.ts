import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { createStores } from '../services/chat-store';

import {
  CreateConversationRequestSchema,
  CreateConversationResponseSchema,
  DeleteConversationRequestSchema,
  DeleteConversationResponseSchema,
  ListConversationsRequestSchema,
  ListConversationsResponseSchema,
  UpdateConversationRequestSchema,
} from '@the-agent/shared';

export class CreateConversation extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: CreateConversationRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Conversation created successfully',
        content: {
          'application/json': {
            schema: CreateConversationResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const { id, type } = await c.req.json();
    const { conversationStore } = createStores(c.env);

    try {
      await conversationStore.createConversation(userId, { id, type: type ?? 'default' });
      return c.json({ id }, 200);
    } catch (error) {
      return c.json({ error: (error as Error).message }, 500);
    }
  }
}

// ===== DELETE CONVERSATION =====

export class DeleteConversation extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: DeleteConversationRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Conversation deleted successfully',
        content: {
          'application/json': {
            schema: DeleteConversationResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const { id } = await c.req.json();

    const { conversationStore } = createStores(c.env);

    try {
      await conversationStore.deleteConversation(id, userId);
      return c.json({ deleted: true }, 200);
    } catch (error) {
      return c.json({ error: (error as Error).message }, 500);
    }
  }
}

// ===== LIST CONVERSATIONS =====

export class ListConversations extends OpenAPIRoute {
  schema = {
    request: {
      query: ListConversationsRequestSchema,
    },
    responses: {
      '200': {
        description: 'List of user conversations',
        content: {
          'application/json': {
            schema: ListConversationsResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const startFrom = parseInt(c.req.query('startFrom') || '0');

    const { conversationStore } = createStores(c.env);

    try {
      const conversations = await conversationStore.listConversations(userId, startFrom);
      return c.json({ conversations }, 200);
    } catch (error) {
      return c.json({ error: (error as Error).message }, 500);
    }
  }
}

export class UpdateConversation extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: UpdateConversationRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Conversation selected successfully',
        content: {
          'application/json': {
            schema: CreateConversationResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const updateData = await c.req.json();
    const { conversationStore } = createStores(c.env);

    try {
      await conversationStore.updateConversation(userId, updateData);
      return c.json({ success: true }, 200);
    } catch (error) {
      return c.json({ error: (error as Error).message }, 500);
    }
  }
}
