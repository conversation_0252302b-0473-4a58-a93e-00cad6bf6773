import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import {
  createUser,
  getCreditDaily,
  getUserBalance,
  getUserInfo,
  rotateApi<PERSON>ey,
  redeemCouponCode,
  generateCouponCode,
} from '../d1/user';
import {
  GetUserResponseSchema,
  RotateApiKeyResponseSchema,
  RedeemCouponResponseSchema,
  RedeemCouponRequestSchema,
  GetUserBalanceResponseSchema,
  GetCreditDailyResponseSchema,
  GetCreditDailyRequestSchema,
  GenerateCouponCodeRequestSchema,
  GenerateCouponCodeResponseSchema,
  SyncUserDataResponseSchema,
  SyncUserDataRequestSchema,
} from '@the-agent/shared';
import { DEFAULT_CAMPAIGN, DEFAULT_CREDITS, DEFAULT_MAX_USES } from '../utils/common';
import { syncUserData } from '@the-agent/chat-store/utils';
import { createStores } from '../services/chat-store';

export class GetUser extends OpenAPIRoute {
  schema = {
    responses: {
      '200': {
        description: 'User info',
        content: {
          'application/json': {
            schema: GetUserResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const email = c.get('userEmail');

    const userInfo = await getUserInfo(c.env, userId);
    if (!userInfo) {
      // initiate user
      if (!email) {
        throw new Error('User email not found');
      }
      const user = await createUser(c.env, userId, email);
      return c.json(
        {
          id: user.id,
          email: user.email,
          api_key: user.api_key,
          api_key_enabled: user.api_key_enabled,
          balance: user.balance,
          permission: user.permission,
        },
        200
      );
    } else {
      return c.json(
        {
          id: userInfo.id,
          email: userInfo.email,
          api_key: userInfo.api_key,
          api_key_enabled: userInfo.api_key_enabled,
          balance: userInfo.balance,
          permission: userInfo.permission,
        },
        200
      );
    }
  }
}

export class GetUserBalance extends OpenAPIRoute {
  schema = {
    responses: {
      '200': {
        description: 'User info',
        content: {
          'application/json': {
            schema: GetUserBalanceResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const balance = await getUserBalance(c.env, userId);
    return c.json(
      {
        balance,
      },
      200
    );
  }
}

export class GetCreditDaily extends OpenAPIRoute {
  schema = {
    request: {
      query: GetCreditDailyRequestSchema,
    },
    responses: {
      '200': {
        description: 'Daily credit usage',
        content: {
          'application/json': {
            schema: GetCreditDailyResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const query = c.req.query();

    const options = {
      startDate: query.startDate,
      endDate: query.endDate,
    };

    const data = await getCreditDaily(c.env, userId, options);
    return c.json({ data }, 200);
  }
}

export class RotateApiKey extends OpenAPIRoute {
  schema = {
    responses: {
      '200': {
        description: 'Rotate API key',
        content: {
          'application/json': {
            schema: RotateApiKeyResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const newApiKey = await rotateApiKey(c.env, userId);
    return c.json({ newApiKey }, 200);
  }
}

export class RedeemCouponCode extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: RedeemCouponRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Redeem coupon code',
        content: {
          'application/json': {
            schema: RedeemCouponResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const userEmail = c.get('userEmail');
    const { code } = await c.req.json();

    const result = await redeemCouponCode(c.env, userId, userEmail, code);

    return c.json(
      {
        added_credits: result.added_credits,
        total_credits: result.total_credits,
      },
      200
    );
  }
}

export class GenerateCouponCode extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: GenerateCouponCodeRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Coupon code generated successfully',
        content: {
          'application/json': {
            schema: GenerateCouponCodeResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const bot_api_key = c.req.header('x-api-key');
    const { campaign, credits, max_uses } = await c.req.json();

    // Authenticate bot token (now using API_KEY for better security)
    const validApiKey = c.env.BOT_API_KEY;
    if (!validApiKey || !bot_api_key || bot_api_key !== validApiKey) {
      return c.json({ error: 'Invalid API key' }, 401);
    }

    // Use provided values or default values
    const finalCampaign = campaign || DEFAULT_CAMPAIGN;
    const finalCredits = credits || DEFAULT_CREDITS;
    const finalMaxUses = max_uses || DEFAULT_MAX_USES;

    const result = await generateCouponCode(c.env, finalCampaign, finalCredits, finalMaxUses);

    return c.json(result, 200);
  }
}

export class SyncUserData extends OpenAPIRoute {
  schema = {
    request: {
      query: SyncUserDataRequestSchema,
    },
    responses: {
      '200': {
        description: 'Sync user data (conversations, messages, miniapps)',
        content: {
          'application/json': {
            schema: SyncUserDataResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const startFrom = parseInt(c.req.query('startFrom') || '0');

    const { conversationStore, miniappStore } = createStores(c.env);

    try {
      const { conversations, miniapps } = await syncUserData({
        userId,
        startFrom,
        conversationStore,
        miniappStore,
      });
      return c.json({ conversations, miniapps }, 200);
    } catch (error) {
      return c.json({ error: (error as Error).message }, 500);
    }
  }
}
