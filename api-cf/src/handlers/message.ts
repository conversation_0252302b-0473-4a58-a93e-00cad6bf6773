import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import {
  Message,
  MessageSchema,
  SaveMessageRequestSchemaV2,
  SaveMessageResponseSchemaV2,
} from '@the-agent/shared';
import { GatewayServiceError } from '../types/service';
import { createStores } from '../services/chat-store';

export class SaveMessageV2 extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: SaveMessageRequestSchemaV2,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Message saved successfully with memory server integration',
        content: {
          'application/json': {
            schema: SaveMessageResponseSchemaV2,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const body = await c.req.json();

    // Validate the message
    let message: Message;
    try {
      message = MessageSchema.parse(body.message);
    } catch (error) {
      console.error('Invalid message format:', error);
      throw new GatewayServiceError(400, 'Invalid message format');
    }

    const { messageStore } = createStores(c.env);

    try {
      await messageStore.saveMessage(message, userId);
      return c.json({ success: true }, 200);
    } catch (error) {
      return c.json({ error: (error as Error).message }, 500);
    }
  }
}
