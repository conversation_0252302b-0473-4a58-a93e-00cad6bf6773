import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import {
  MiniApp,
  MiniAppSchema,
  SaveMiniAppRequestSchema,
  SaveMiniAppResponseSchema,
  UpdateMiniAppRequestSchema,
  UpdateMiniAppResponseSchema,
} from '@the-agent/shared';
import { createStores } from '../services/chat-store';
import { GatewayServiceError } from '../types/service';

export class SaveMiniApp extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: SaveMiniAppRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'MiniApp saved successfully',
        content: {
          'application/json': {
            schema: SaveMiniAppResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const body = await c.req.json();

    let miniapp: MiniApp;
    try {
      miniapp = MiniAppSchema.parse(body.miniapp);
    } catch (error) {
      console.error('Invalid miniapp format:', error);
      throw new GatewayServiceError(400, 'Invalid miniapp format');
    }

    const { miniappStore } = createStores(c.env);

    try {
      await miniappStore.saveMiniApp(miniapp, userId);
      return c.json({ success: true }, 200);
    } catch (error) {
      console.error('Failed to save miniapp:', error);
      return c.json({ error: (error as Error).message }, 500);
    }
  }
}

export class UpdateMiniApp extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: UpdateMiniAppRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'MiniApp updated successfully',
        content: {
          'application/json': {
            schema: UpdateMiniAppResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const body = await c.req.json();

    const parsed = UpdateMiniAppRequestSchema.safeParse(body);
    if (!parsed.success) {
      console.error('Invalid update miniapp request:', parsed.error);
      throw new GatewayServiceError(400, 'Invalid update miniapp request');
    }

    const { miniappStore } = createStores(c.env);

    try {
      await miniappStore.updateMiniApp(userId, parsed.data);
      return c.json({ success: true }, 200);
    } catch (error) {
      console.error('Failed to update miniapp:', error);
      return c.json({ error: (error as Error).message }, 500);
    }
  }
}
