import {
  MessageRole,
  Message,
  SearchMemoryOptions,
  AddMemoryOptions,
  SearchResult,
} from '@the-agent/shared';

export interface MessageInput {
  role: MessageRole;
  content: string;
}

export async function searchMemory(
  env: Env,
  text: string,
  config: SearchMemoryOptions
): Promise<SearchResult> {
  if (!config.limit || config.limit <= 0) {
    return {
      results: [],
      relations: [],
    };
  }

  const response = await fetch(`${env.MEMORY_SERVICE_URL}/memory/search`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${env.MEMORY_SERVICE_API_KEY}`,
    },
    body: JSON.stringify({
      text,
      config,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Memory server search failed:', {
      status: response.status,
      error: errorText,
      config,
      text,
    });
    throw new Error(`Memory server search failed: ${response.status} - ${errorText}`);
  }
  return response.json();
}

export async function addMessagesToMemory(
  env: Env,
  messages: Message[],
  config: AddMemoryOptions
): Promise<SearchResult> {
  const response = await fetch(`${env.MEMORY_SERVICE_URL}/memory/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${env.MEMORY_SERVICE_API_KEY}`,
    },
    body: JSON.stringify({
      messages,
      config,
    }),
  });
  if (!response.ok) {
    const errorText = await response.text();
    console.error('Memory server add failed:', {
      status: response.status,
      error: errorText,
      config,
      messages,
    });
    return {
      results: [],
      relations: [],
    };
  }
  return await response.json();
}

export async function addSiteMessageToMemory(
  env: Env,
  messages: Message[],
  config: AddMemoryOptions
): Promise<SearchResult> {
  const response = await fetch(`${env.MEMORY_SERVICE_URL}/memory/add_site`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${env.MEMORY_SERVICE_API_KEY}`,
    },
    body: JSON.stringify({
      messages,
      config,
    }),
  });
  if (!response.ok) {
    const errorText = await response.text();
    console.error('Memory server add failed:', {
      status: response.status,
      error: errorText,
      config,
      messages,
    });
    return {
      results: [],
      relations: [],
    };
  }
  return await response.json();
}

// Utility function to extract siteId from a URL or return as-is if invalid
export function extractSiteIdFromUrl(site: string): string {
  try {
    const url = new URL(site);
    return url.hostname;
  } catch (error) {
    console.warn('Invalid site URL, using as-is:', site, error);
    return site;
  }
}
