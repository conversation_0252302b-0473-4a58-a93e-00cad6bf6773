{"name": "@the-agent/telegram", "version": "0.1.0", "description": "NodeJS/Browser MTProto API Telegram client library,", "main": "browser/index.js", "types": "browser/index.d.ts", "scripts": {"test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/gram-js/gramjs.git"}, "browser": {"fs": false}, "license": "MIT", "bugs": {"url": "https://github.com/gram-js/gramjs/issues"}, "keywords": ["telegram", "mtproto", "userbot", "api"], "homepage": "https://github.com/gram-js/gramjs#readme", "optionalDependencies": {"bufferutil": "^4.0.3", "utf-8-validate": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.12.13", "@babel/preset-env": "^7.12.16", "@types/jest": "^29.5.1", "@types/mime": "^2.0.3", "@types/node": "^15.12.0", "@types/node-localstorage": "^1.3.0", "@types/pako": "^1.0.1", "@types/websocket": "^1.0.4", "babel-loader": "^8.2.2", "jest": "^29.5.0", "os-browserify": "^0.3.0", "prettier": "2.3.1", "process": "^0.11.10", "ts-jest": "^29.1.0", "ts-loader": "^8.0.16", "ts-node": "^9.1.1", "typedoc": "^0.22.11", "typedoc-plugin-missing-exports": "^0.22.6", "typescript": "^4.2.4", "util": "^0.12.4", "webpack": "^5.69.0", "webpack-cli": "^4.9.2"}, "dependencies": {"@cryptography/aes": "^0.1.1", "async-mutex": "^0.3.0", "big-integer": "^1.6.48", "buffer": "^6.0.3", "htmlparser2": "^6.1.0", "mime": "^3.0.0", "pako": "^2.0.3", "path-browserify": "^1.0.1", "real-cancellable-promise": "^1.1.1", "store2": "^2.13.0", "ts-custom-error": "^3.2.0", "websocket": "^1.0.34"}}