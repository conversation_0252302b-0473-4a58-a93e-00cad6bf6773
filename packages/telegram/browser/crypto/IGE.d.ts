import { <PERSON><PERSON><PERSON> } from "buffer/";
declare class IGENEW {
    private ige;
    constructor(key: <PERSON><PERSON><PERSON>, iv: <PERSON><PERSON><PERSON>);
    /**
     * Decrypts the given text in 16-bytes blocks by using the given key and 32-bytes initialization vector
     * @param cipherText {<PERSON>uffer}
     * @returns {<PERSON>uffer}
     */
    decryptIge(cipherText: <PERSON>uffer): Buffer;
    /**
     * Encrypts the given text in 16-bytes blocks by using the given key and 32-bytes initialization vector
     * @param plainText {Buffer}
     * @returns {Buffer}
     */
    encryptIge(plainText: Buffer): Buffer;
}
export { IGENEW as IGE };
