import { TLMessage } from "./TLMessage";
import { R<PERSON><PERSON><PERSON><PERSON> } from "./RPCResult";
import { MessageContainer } from "./MessageContainer";
import { GZIPPacked } from "./GZIPPacked";

export const coreObjects = new Map<number, Function>([
    [RPCResult.CONSTRUCTOR_ID, RPC<PERSON><PERSON>ult],
    [GZIPPacked.CONSTRUCTOR_ID, GZIPPacked],
    [MessageContainer.CONSTRUCTOR_ID, MessageContainer],
]);
export { RPCResult, TLMessage, MessageContainer, GZIPPacked };
