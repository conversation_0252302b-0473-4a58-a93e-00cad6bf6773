{"compilerOptions": {"module": "commonjs", "target": "es2017", "lib": ["dom", "es7"], "sourceMap": false, "downlevelIteration": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "declaration": true, "skipLibCheck": true, "outDir": "./dist"}, "exclude": ["gramjs/tl/types-generator", "node_modules"], "include": ["gramjs"]}