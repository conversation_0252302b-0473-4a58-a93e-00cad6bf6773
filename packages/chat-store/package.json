{"name": "@the-agent/chat-store", "version": "0.1.0", "description": "Chat data storage abstraction layer", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./workers": {"types": "./dist/workers.d.ts", "default": "./dist/workers.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "dependencies": {"@the-agent/shared": "workspace:*", "@supabase/supabase-js": "^2.53.0"}, "devDependencies": {"@types/pg": "^8.15.5", "typescript": "^5.5.2"}, "peerDependencies": {"pg": "^8.16.3"}}