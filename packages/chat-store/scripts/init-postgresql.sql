-- 创建对话表
CREATE TABLE IF NOT EXISTS conversations (
    local_id BIGINT PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK(status IN ('active', 'archived', 'deleted')),
    last_selected_at BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建消息表
CREATE TABLE IF NOT EXISTS messages (
    local_id BIGINT PRIMARY KEY,
    conversation_local_id BIGINT NOT NULL REFERENCES conversations(local_id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK(role IN ('system', 'user', 'assistant', 'tool')),
    content TEXT,
    reasoning TEXT,
    tool_calls JSONB,
    tool_call_id VARCHAR(255),
    name <PERSON><PERSON>HA<PERSON>(255),
    status VARCHAR(20) DEFAULT 'completed' CHECK(status IN ('pending', 'completed', 'error')),
    error TEXT,
    actor VARCHAR(20) DEFAULT 'user' CHECK(actor IN ('user', 'system')),
    task_id VARCHAR(255),
    run_id VARCHAR(255),
    agent_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_local_id ON messages(conversation_local_id);
CREATE INDEX IF NOT EXISTS idx_messages_user_id ON messages(user_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_role ON messages(role); 