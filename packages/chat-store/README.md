# @the-agent/chat-store

A chat data storage abstraction layer supporting Supabase and PostgreSQL.

## Features

- Message storage (create, query, update, delete)
- Conversation storage (create, query, update, delete)
- Supports Supabase and PostgreSQL
- Interface abstraction, easy to extend
- Type-safe API

## Installation

```bash
pnpm add @the-agent/chat-store
```

## Usage

### 1. Storage Configuration

```typescript
import { createMessageStore, createConversationStore } from '@the-agent/chat-store';

// Supabase config
const supabaseConfig = {
  type: 'supabase' as const,
  supabaseUrl: 'your_supabase_url',
  supabaseKey: 'your_supabase_key',
};

// PostgreSQL config
const postgresConfig = {
  type: 'postgresql' as const,
  host: 'localhost',
  port: 5432,
  database: 'your_database',
  username: 'your_username',
  password: 'your_password',
  ssl: false,
};

// Create store instances
const messageStore = createMessageStore(supabaseConfig);
const conversationStore = createConversationStore(postgresConfig);
```

### 2. Message Operations

```typescript
import { Message } from '@the-agent/shared';

// Save a message
const message: Message = {
  id: 123,
  conversation_id: 456,
  role: 'user',
  content: 'Hello, how are you?',
  // ... other fields
};

await messageStore.saveMessage(message, 'user-id');

// Get all messages in a conversation
const messages = await messageStore.getMessages(456);

// Update a message
await messageStore.updateMessage(123, {
  content: 'Updated content',
  reasoning: 'Updated reasoning',
});

// Delete a message
await messageStore.deleteMessage(123);
```

### 3. Conversation Operations

```typescript
// Create a conversation
await conversationStore.createConversation(456, 'user-id');

// List conversations
const conversations = await conversationStore.listConversations('user-id', 0);

// Delete a conversation
await conversationStore.deleteConversation(456, 'user-id');

// Update conversation status (database-level)
await conversationStore.updateConversationStatus(456, 'user-id', 'archived');
```

## Database Initialization

### PostgreSQL

Run `scripts/init-postgresql.sql` to create the required tables and indexes:

```bash
psql -d your_database -f packages/chat-store/scripts/init-postgresql.sql
```

### Supabase

Supabase will automatically create the table structure, no manual initialization required.

## Type Definitions

All types are imported from the `@the-agent/shared` package:

```typescript
import { Message, Conversation, MessageRole, MessageStatus } from '@the-agent/shared';
```

## Development

```bash
# Build
pnpm build

# Development mode
pnpm dev
```

## Architecture

```
chat-store/
├── interfaces/          # Interface definitions
│   ├── message.store.ts
│   └── conversation.store.ts
├── supabase/           # Supabase implementations
│   ├── message.store.ts
│   └── conversation.store.ts
├── postgresql/         # PostgreSQL implementations
│   ├── message.store.ts
│   └── conversation.store.ts
├── factory.ts          # Factory functions
└── index.ts            # Entry file
```

## Extension

To add a new storage backend:

1. Implement the `MessageStore` and `ConversationStore` interfaces
2. Add the corresponding factory function in `factory.ts`
3. Export the new implementation
