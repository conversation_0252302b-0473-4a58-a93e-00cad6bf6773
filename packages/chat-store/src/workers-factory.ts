import { MessageStore, ConversationStore } from './interfaces';
import { TemplateStore } from './interfaces/template.store';
import { MiniAppStore } from './interfaces/miniapp.store';
import { SupabaseMessageStore, SupabaseConversationStore } from './supabase';
import { SupabaseTemplateStore } from './supabase/template.store';
import { SupabaseMiniAppStore } from './supabase/miniapp.store';

export interface SupabaseConfig {
  type: 'supabase';
  supabaseUrl: string;
  supabaseKey: string;
}

// Workers environment only support Supabase
export type StorageConfig = SupabaseConfig;

export function createMessageStore(config: StorageConfig): MessageStore {
  if (config.type === 'supabase') {
    return new SupabaseMessageStore(config.supabaseUrl, config.supabaseKey);
  } else {
    throw new Error('Only Supabase is supported in Workers environment');
  }
}

export function createConversationStore(config: StorageConfig): ConversationStore {
  if (config.type === 'supabase') {
    return new SupabaseConversationStore(config.supabaseUrl, config.supabaseKey);
  } else {
    throw new Error('Only Supabase is supported in Workers environment');
  }
}

export function createTemplateStore(config: StorageConfig): TemplateStore {
  if (config.type === 'supabase') {
    return new SupabaseTemplateStore(config.supabaseUrl, config.supabaseKey);
  } else {
    throw new Error('Only Supabase is supported in Workers environment');
  }
}

export function createMiniAppStore(config: StorageConfig): MiniAppStore {
  if (config.type === 'supabase') {
    return new SupabaseMiniAppStore(config.supabaseUrl, config.supabaseKey);
  } else {
    throw new Error('Only Supabase is supported in Workers environment');
  }
}
