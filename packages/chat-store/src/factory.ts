import { Pool } from 'pg';
import { MessageStore, ConversationStore, MiniAppStore } from './interfaces';
import { SupabaseMessageStore, SupabaseConversationStore } from './supabase';
import { PostgresMessageStore, PostgresConversationStore } from './postgresql';
import { SupabaseMiniAppStore } from './supabase/miniapp.store';
import { PostgresMiniAppStore } from './postgresql/miniapp.store';
import { SupabaseTemplateStore } from './supabase/template.store';
import { TemplateStore } from './interfaces/template.store';
import { PostgresTemplateStore } from './postgresql/template.store';

export interface SupabaseConfig {
  type: 'supabase';
  supabaseUrl: string;
  supabaseKey: string;
}

export interface PostgresConfig {
  type: 'postgresql';
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}

export type StorageConfig = SupabaseConfig | PostgresConfig;

export function createMessageStore(config: StorageConfig): MessageStore {
  if (config.type === 'supabase') {
    return new SupabaseMessageStore(config.supabaseUrl, config.supabaseKey);
  } else {
    const pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : false,
    });
    return new PostgresMessageStore(pool);
  }
}

export function createConversationStore(config: StorageConfig): ConversationStore {
  if (config.type === 'supabase') {
    return new SupabaseConversationStore(config.supabaseUrl, config.supabaseKey);
  } else {
    const pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : false,
    });
    return new PostgresConversationStore(pool);
  }
}

export function createTemplateStore(config: StorageConfig): TemplateStore {
  if (config.type === 'supabase') {
    return new SupabaseTemplateStore(config.supabaseUrl, config.supabaseKey);
  } else {
    const pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : false,
    });
    return new PostgresTemplateStore(pool);
  }
}

export function createMiniAppStore(config: StorageConfig): MiniAppStore {
  if (config.type === 'supabase') {
    return new SupabaseMiniAppStore(config.supabaseUrl, config.supabaseKey);
  } else {
    const pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : false,
    });
    return new PostgresMiniAppStore(pool);
  }
}
