import { ConversationType } from '@the-agent/shared';

export interface MessageRow {
  id: string;
  local_id: number;
  conversation_id: string;
  role: string;
  content: string | null;
  reasoning: string | null;
  tool_calls: string | null;
  tool_call_id: string | null;
  name: string | null;
  status: string | null;
  error: string | null;
  actor: 'system' | 'user' | null;
  task_id: string | null;
  run_id: string | null;
  agent_id: string | null;
  metadata: string | null;
}

export interface ConversationRow {
  id: string;
  local_id: number;
  type: ConversationType;
  title: string | null;
  user_id: string;
  status: 'deleted' | 'active';
  last_selected_at?: number;
}
