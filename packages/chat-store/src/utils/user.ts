import { ConversationStore } from '../interfaces/conversation.store';
import { MiniAppStore } from '../interfaces/miniapp.store';
import { SyncUserDataResponse } from '@the-agent/shared';

export async function syncUserData({
  userId,
  startFrom = 0,
  conversationStore,
  miniappStore,
}: {
  userId: string;
  startFrom?: number;
  conversationStore: ConversationStore;
  miniappStore: MiniAppStore;
}): Promise<SyncUserDataResponse> {
  const [conversations, miniapps] = await Promise.all([
    conversationStore.listConversations(userId, startFrom, ['default', 'remote']),
    miniappStore.listMiniApps(userId, startFrom),
  ]);

  return {
    conversations,
    miniapps,
  };
}
