// generalizePath('/status/abc123', '/status/xyz456');
// → "/status/{var}"

// generalizePath('/user/elon/posts/123', '/user/grimes/posts/456');
// → "/user/{var}/posts/{var}"

// generalizePath('/a/b/c', '/a/b');
// → "/a/b/{var}"

// generalizePath('/abc', '/xyz');
// → "/{var}"
export function generalizePath(p1: string, p2: string): string {
  const t1 = p1.split('/').filter(Boolean);
  const t2 = p2.split('/').filter(Boolean);

  const maxLen = Math.max(t1.length, t2.length);
  const result: string[] = [];

  for (let i = 0; i < maxLen; i++) {
    if (t1[i] === t2[i] && t1[i] !== undefined) {
      result.push(t1[i]);
    } else {
      result.push('{var}');
    }
  }
  return '/' + result.join('/');
}

export function isGeneralizable(p1: string, p2: string): boolean {
  const t1 = p1.split('/').filter(Boolean);
  const t2 = p2.split('/').filter(Boolean);

  if (t1.length !== t2.length) return false;

  // Require at least 1 shared segment at the start
  return t1[0] === t2[0];
}
