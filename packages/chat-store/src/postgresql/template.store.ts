import { Pool } from 'pg';
import {
  GetTemplatesRequest,
  GetTemplatesResponse,
  Template,
  TemplateRegisterRequest,
  TemplateRegisterResponse,
} from '@the-agent/shared';
import { TemplateStore } from '../interfaces/template.store';
import { generalizePath, isGeneralizable } from '../utils/template';

const SEARCH_QUERY = `
SELECT *, (tag_vector <=> $1) AS similarity
   FROM templates
   WHERE user_id = $2
     AND domain = $3
     AND simhash BETWEEN $4 AND $4
     AND (tag_vector <=> $1) < 0.05
   ORDER BY similarity ASC
   LIMIT 5
`;

const INSERT_QUERY = `
INSERT INTO templates (user_id, domain, path_pattern, example_path, tag_vector, simhash)
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING *
`;

const GET_TEMPLATES_QUERY = `
SELECT id, domain, path_pattern
   FROM templates
   WHERE user_id = $1
`;

export class PostgresTemplateStore implements TemplateStore {
  private pool: Pool;

  constructor(pool: Pool) {
    this.pool = pool;
  }

  async registerTemplate(
    userId: string,
    request: TemplateRegisterRequest
  ): Promise<TemplateRegisterResponse> {
    const client = await this.pool.connect();
    try {
      const { rows } = await client.query(SEARCH_QUERY, [
        request.tag_vector,
        userId,
        request.domain,
        request.simhash - 3,
        request.simhash + 3,
      ]);

      for (const row of rows) {
        const generalizable = isGeneralizable(row.example_path, request.path);

        if (generalizable) {
          const generalized = generalizePath(row.example_path, request.path);

          if (generalized !== row.path_pattern) {
            await client.query(
              `UPDATE template_registry SET path_pattern = $1, updated_at = NOW() WHERE id = $2`,
              [generalized, row.id]
            );
            row.path_pattern = generalized;
          }

          return {
            id: row.id,
            domain: row.domain,
            path_pattern: row.path_pattern,
            matched: true,
          } as TemplateRegisterResponse;
        }
      }
      // no match, insert new template
      const result = await client.query(INSERT_QUERY, [
        userId,
        request.domain,
        request.path,
        request.path,
        request.tag_vector,
        BigInt(request.simhash),
      ]);
      return {
        id: result.rows[0].id,
        domain: request.domain,
        path_pattern: request.path,
        matched: false,
      } as TemplateRegisterResponse;
    } finally {
      client.release();
    }
  }

  async getTemplates(userId: string, request: GetTemplatesRequest): Promise<GetTemplatesResponse> {
    const client = await this.pool.connect();
    try {
      let query = GET_TEMPLATES_QUERY;
      const params = [userId];
      if (request.domain) {
        query += ' AND domain = $2';
        params.push(request.domain);
      }
      const { rows } = await client.query(query, params);
      return {
        templates: rows.map(row => ({
          id: row.id,
          domain: row.domain,
          path_pattern: row.path_pattern,
        })),
      };
    } finally {
      client.release();
    }
  }
}
