import { Pool } from 'pg';
import {
  Conversation,
  ConversationType,
  MessageRole,
  MessageStatus,
  ToolCall,
  UpdateConversationRequest,
} from '@the-agent/shared';
import { ConversationStore } from '../interfaces/conversation.store';
import { MessageRow } from '../workers';
import { MiniApp } from '@the-agent/shared';

export class PostgresConversationStore implements ConversationStore {
  private pool: Pool;

  constructor(pool: Pool) {
    this.pool = pool;
  }

  async createConversation(userId: string, conversation: Conversation): Promise<void> {
    const client = await this.pool.connect();
    try {
      await client.query(
        `INSERT INTO conversations (id, local_id, user_id, status, type, last_selected_at)
         VALUES (md5(concat_ws(':', $2::text, $1::bigint::text)), $1, $2, $3, $4, $5)
         ON CONFLICT (user_id, local_id) DO NOTHING`,
        [conversation.id, userId, 'active', conversation.type, Date.now()]
      );
    } finally {
      client.release();
    }
  }

  async listConversations(
    userId: string,
    startFrom: number,
    types?: ConversationType[]
  ): Promise<Conversation[]> {
    const client = await this.pool.connect();
    try {
      const typesFilter = types
        ? `AND c.type IN (${types.map(type => `'${type}'`).join(',')})`
        : '';
      const query = `
        SELECT 
          c.local_id as conversation_id,
          c.type as type,
          c.last_selected_at as last_selected_at,
          COALESCE(
            json_agg(
              CASE WHEN m.local_id IS NOT NULL THEN
                json_build_object(
                  'local_id', m.local_id,
                  'conversation_id', c.local_id,
                  'role', m.role,
                  'content', m.content,
                  'reasoning', m.reasoning,
                  'tool_calls', m.tool_calls,
                  'tool_call_id', m.tool_call_id,
                  'name', m.name,
                  'status', m.status,
                  'error', m.error,
                  'actor', m.actor,
                  'task_id', m.task_id,
                  'run_id', m.run_id,
                  'agent_id', m.agent_id,
                  'metadata', m.metadata
                )
              END
            ) FILTER (WHERE m.local_id IS NOT NULL),
            '[]'::json
          ) as messages
        FROM conversations c
        LEFT JOIN messages m ON c.id = m.conversation_id
        WHERE c.user_id = $1 AND c.status = 'active' AND c.local_id >= $2 ${typesFilter}
        GROUP BY c.local_id, c.type, c.last_selected_at
        ORDER BY c.last_selected_at DESC NULLS LAST, c.local_id DESC
      `;

      const result = await client.query(query, [userId, startFrom]);

      return result.rows.map(
        (row: {
          conversation_id: number;
          type: ConversationType;
          last_selected_at?: number;
          messages: MessageRow[];
        }) => ({
          id: row.conversation_id,
          type: row.type,
          last_selected_at: row.last_selected_at || row.conversation_id, // Use last_selected_at or fallback to conversation_id
          messages: row.messages.map((message: MessageRow) => ({
            id: message.local_id,
            conversation_id: row.conversation_id,
            role: message.role as MessageRole,
            content: message.content,
            reasoning: message.reasoning,
            tool_calls: this.parseToolCalls(message.tool_calls),
            tool_call_id: message.tool_call_id,
            name: message.name,
            status: message.status as MessageStatus | null,
            error: message.error,
            actor: message.actor,
            task_id: message.task_id,
            run_id: message.run_id,
            agent_id: message.agent_id,
            metadata: message.metadata ? JSON.parse(message.metadata) : {},
          })),
        })
      );
    } finally {
      client.release();
    }
  }

  async deleteConversation(id: number, userId: string): Promise<void> {
    const client = await this.pool.connect();
    try {
      await client.query(
        'UPDATE conversations SET status = $1 WHERE local_id = $2 AND user_id = $3',
        ['deleted', id, userId]
      );
    } finally {
      client.release();
    }
  }

  async updateConversation(userId: string, request: UpdateConversationRequest): Promise<void> {
    const client = await this.pool.connect();
    try {
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (request.last_selected_at !== undefined) {
        updateFields.push(`last_selected_at = $${paramIndex++}`);
        updateValues.push(request.last_selected_at);
      }
      if (request.title !== undefined) {
        updateFields.push(`title = $${paramIndex++}`);
        updateValues.push(request.title);
      }
      if (request.type !== undefined) {
        updateFields.push(`type = $${paramIndex++}`);
        updateValues.push(request.type);
      }
      if (request.status !== undefined) {
        updateFields.push(`status = $${paramIndex++}`);
        updateValues.push(request.status);
      }

      if (updateFields.length === 0) {
        return; // No fields to update
      }

      updateValues.push(request.id, userId);
      const query = `UPDATE conversations SET ${updateFields.join(', ')} WHERE local_id = $${paramIndex++} AND user_id = $${paramIndex}`;

      await client.query(query, updateValues);
    } finally {
      client.release();
    }
  }

  private parseToolCalls(toolCalls: unknown): ToolCall[] | null {
    if (typeof toolCalls === 'string') {
      return JSON.parse(toolCalls);
    }
    if (toolCalls === null) {
      return null;
    }
    return toolCalls as ToolCall[];
  }
}
