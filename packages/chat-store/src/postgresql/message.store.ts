import { Pool } from 'pg';
import { Message } from '@the-agent/shared';
import { MessageStore } from '../interfaces/message.store';
import { sha256Hex } from '../utils/crypto';

export class PostgresMessageStore implements MessageStore {
  private pool: Pool;

  constructor(pool: Pool) {
    this.pool = pool;
  }

  async saveMessage(message: Message, userId: string): Promise<void> {
    const client = await this.pool.connect();
    const convId = await sha256Hex(`${userId}:${message.conversation_id}`);
    const messageQuery = `
      INSERT INTO messages (
        id, local_id, conversation_id, role, content, reasoning, 
        tool_calls, tool_call_id, name, status, error, actor, 
        task_id, run_id, agent_id
      ) VALUES (md5(concat_ws(':', $1::bigint::text, $2::text)), $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
    `;

    const messageValues = [
      message.id, // local_id
      convId, // conversation_id
      message.role,
      message.content,
      message.reasoning,
      message.tool_calls ? JSON.stringify(message.tool_calls) : null,
      message.tool_call_id,
      message.name,
      message.status || 'completed',
      message.error,
      message.actor || 'user',
      message.task_id || null,
      message.run_id || null,
      message.agent_id || null,
      message.metadata ? JSON.stringify(message.metadata) : null,
    ];

    try {
      await client.query('BEGIN');
      await client.query(messageQuery, messageValues);
      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw new Error(`Failed to save message: ${error}`);
    } finally {
      client.release();
    }
  }
}
