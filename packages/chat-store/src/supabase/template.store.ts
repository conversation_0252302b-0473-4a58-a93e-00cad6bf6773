import {
  GetTemplatesRequest,
  GetTemplatesResponse,
  TemplateRegisterRequest,
  TemplateRegisterResponse,
} from '@the-agent/shared';
import { TemplateStore } from '../interfaces/template.store';
import { generalizePath, isGeneralizable } from '../utils/template';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

export class SupabaseTemplateStore implements TemplateStore {
  private supabase: SupabaseClient;

  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  async registerTemplate(
    userId: string,
    request: TemplateRegisterRequest
  ): Promise<TemplateRegisterResponse> {
    const { data: candidates, error } = await this.supabase
      .from('templates')
      .select('*') // ✅ no expressions here
      .eq('user_id', userId)
      .eq('domain', request.domain)
      .gte('simhash', Number(request.simhash) - 3)
      .lte('simhash', Number(request.simhash) + 3)
      .lt('tag_vector <=>', 0.05) // ✅ filter only
      .limit(5);

    if (error) {
      throw new Error(`Failed to register template: ${error.message}`);
    }

    for (const row of candidates) {
      const generalizable = isGeneralizable(row.example_path, request.path);

      if (generalizable) {
        const generalized = generalizePath(row.example_path, request.path);

        if (generalized !== row.path_pattern) {
          await this.supabase
            .from('templates')
            .update({
              path_pattern: generalized,
              updated_at: new Date().toISOString(),
            })
            .eq('id', row.id);
          row.path_pattern = generalized;
        }
        return {
          id: row.id,
          domain: row.domain,
          path_pattern: row.path_pattern,
          matched: true,
        };
      }
    }

    // no match, insert new template
    const { data: result, error: insertError } = await this.supabase
      .from('templates')
      .insert([
        {
          user_id: userId,
          domain: request.domain,
          path_pattern: request.path,
          example_path: request.path,
          tag_vector: request.tag_vector,
          simhash: BigInt(request.simhash),
        },
      ])
      .select()
      .single();

    if (insertError) {
      throw new Error(`Failed to insert template: ${insertError.message}`);
    }

    return {
      id: result.id,
      domain: request.domain,
      path_pattern: request.path,
      matched: false,
    } as TemplateRegisterResponse;
  }

  async getTemplates(userId: string, request: GetTemplatesRequest): Promise<GetTemplatesResponse> {
    const query = this.supabase
      .from('templates')
      .select('*')
      .order('created_at', { ascending: false });

    if (request.domain) {
      query.eq('domain', request.domain);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to get templates: ${error.message}`);
    }

    if (!data) {
      return { templates: [] } as GetTemplatesResponse;
    }

    return {
      templates: data.map(row => ({
        id: row.id,
        domain: row.domain,
        path_pattern: row.path_pattern,
      })),
    } as GetTemplatesResponse;
  }
}
