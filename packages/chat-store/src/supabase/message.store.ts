import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Message } from '@the-agent/shared';
import { MessageStore } from '../interfaces/message.store';
import { sha256Hex } from '../utils/crypto';

export class SupabaseMessageStore implements MessageStore {
  private supabase: SupabaseClient;

  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  async saveMessage(message: Message, userId: string): Promise<void> {
    const convId = await sha256Hex(`${userId}:${message.conversation_id}`);
    const { error } = await this.supabase.from('messages').insert({
      local_id: message.id,
      conversation_id: convId,
      role: message.role,
      content: message.content,
      reasoning: message.reasoning,
      tool_calls: message.tool_calls,
      tool_call_id: message.tool_call_id,
      name: message.name,
      status: message.status || 'completed',
      error: message.error,
      actor: message.actor || 'user',
      task_id: message.task_id,
      run_id: message.run_id,
      agent_id: message.agent_id,
      metadata: message.metadata ?? null,
    });
    if (error) {
      throw new Error(`Failed to save message: ${error.message}`);
    }
  }
}
