import { createClient, SupabaseClient } from '@supabase/supabase-js';
import {
  Conversation,
  MessageRole,
  MessageStatus,
  ConversationType,
  UpdateConversationRequest,
} from '@the-agent/shared';
import { ConversationStore } from '../interfaces/conversation.store';
import { ConversationRow, MessageRow } from '../types/database';

export class SupabaseConversationStore implements ConversationStore {
  private supabase: SupabaseClient;

  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  async createConversation(userId: string, conversation: Conversation): Promise<void> {
    const { error } = await this.supabase.from('conversations').upsert(
      {
        local_id: conversation.id,
        user_id: userId,
        status: 'active',
        type: conversation.type,
        last_selected_at: Date.now(), // Use current timestamp for new conversations
      },
      { onConflict: 'user_id,local_id', ignoreDuplicates: true }
    );

    if (error) {
      throw new Error(`Failed to create conversation: ${error.message}`);
    }
  }

  async listConversations(
    userId: string,
    startFrom: number,
    types?: ConversationType[]
  ): Promise<Conversation[]> {
    let query = this.supabase
      .from('conversations')
      .select('*, messages(*)')
      .eq('user_id', userId)
      .eq('status', 'active');

    if (types && types.length > 0) {
      query = query.in('type', types);
    }

    const { data, error } = await query.gte('local_id', startFrom);

    if (error) {
      throw new Error(`Failed to list conversations: ${error.message}`);
    }

    return data.map((conversation: ConversationRow & { messages: MessageRow[] }) => ({
      id: conversation.local_id,
      type: conversation.type as ConversationType,
      last_selected_at: conversation.last_selected_at || conversation.local_id, // Use last_selected_at or fallback to local_id
      messages: (conversation.messages || []).map((message: MessageRow) => ({
        id: message.local_id,
        conversation_id: conversation.local_id,
        role: message.role as MessageRole,
        content: message.content,
        reasoning: message.reasoning,
        tool_calls: this.parseJson(message.tool_calls),
        tool_call_id: message.tool_call_id,
        name: message.name,
        status: message.status as MessageStatus | null,
        error: message.error,
        actor: message.actor,
        task_id: message.task_id,
        run_id: message.run_id,
        agent_id: message.agent_id,
        metadata: this.parseJson(message.metadata),
      })),
    }));
  }

  async deleteConversation(id: number, userId: string): Promise<void> {
    const { error } = await this.supabase
      .from('conversations')
      .update({ status: 'deleted' })
      .eq('local_id', id)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to delete conversation: ${error.message}`);
    }
  }

  async updateConversation(userId: string, request: UpdateConversationRequest): Promise<void> {
    const updateData: Partial<ConversationRow> = {};

    if (request.last_selected_at !== undefined) {
      updateData.last_selected_at = request.last_selected_at;
    }
    if (request.title !== undefined) {
      updateData.title = request.title;
    }
    if (request.type !== undefined) {
      updateData.type = request.type;
    }
    if (request.status !== undefined) {
      updateData.status = request.status as 'deleted' | 'active';
    }

    const { error } = await this.supabase
      .from('conversations')
      .update(updateData)
      .eq('local_id', request.id)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to update conversation: ${error.message}`);
    }
  }

  private parseJson(json: unknown): any | null {
    if (typeof json === 'string') {
      return JSON.parse(json);
    }
    return json;
  }
}
