import { Conversation, ConversationType, UpdateConversationRequest } from '@the-agent/shared';

export interface ConversationStore {
  createConversation(userId: string, conversation: Conversation): Promise<void>;
  listConversations(
    userId: string,
    startFrom: number,
    types?: ConversationType[]
  ): Promise<Conversation[]>;
  deleteConversation(id: number, userId: string): Promise<void>;
  updateConversation(userId: string, request: UpdateConversationRequest): Promise<void>;
}
