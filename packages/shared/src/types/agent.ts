import OpenAI from 'openai';
import { Message, ToolCall, ToolCallResult } from './api';
import { SearchFilters } from './memory';
import { TaskNode } from './task';

export type ChatStatus = 'idle' | 'running' | 'streaming' | 'calling_tool';

export interface MemoryOptions {
  recent: number; // number of recent messages to include
  related: number; //top k related messages to include
  site: number; // number of site messages to include
  graph: number; // number of graph messages to include
  tab: boolean; // if to include current tab info
}

export interface ChatOptions {
  conversationId: number;
  onMessageUpdate?: (message: Message) => Promise<void>;
  onMessageComplete?: (message: Message) => Promise<void>;
  onStatusChange?: (status: ChatStatus) => Promise<void>;
  onChatComplete?: (messages: Message[]) => Promise<void>;
}

export interface ToolOptions {
  filters?: SearchFilters;
  metadata?: Record<string, unknown>;
  onToolMessageUpdate?: (results: any) => Promise<void>;
}

export interface TaskOptions {
  task: TaskNode;
}

export interface AgentRuntimeConfig {
  chatOptions?: ChatOptions;
  toolOptions?: ToolOptions;
  taskOptions?: TaskOptions;
}

export interface ToolExecutor {
  execute: (toolCall: ToolCall, options?: AgentRuntimeConfig) => Promise<ToolCallResult>;

  getTools: () => OpenAI.ChatCompletionTool[];

  getPostToolcallMessage: (toolCall: ToolCall) => string;

  isFinalToolCall?: (toolCall: ToolCall) => boolean;
}

export interface ContextBuilderConfig {
  memoryOptions?: Partial<MemoryOptions>;
  taskOptions?: TaskOptions;
}

export interface ContextBuilder {
  build: (message: Message, config?: ContextBuilderConfig) => Promise<Message[]>;
}

export interface AgentConfig {
  id: string;
  llmClient: OpenAI;
  model: string;
  systemPrompt: string;
  contextBuilder: ContextBuilder;
  toolExecutor: ToolExecutor;
  maxToolCalls: number;
  maxDepth?: number; // max depth of the task tree
}

export interface Agent {
  // infer the next message until the end of the conversation
  run: (userMessage: Message, runtimeConfig?: AgentRuntimeConfig) => Promise<Message[]>;

  getConfig: () => AgentConfig;

  abort(): Promise<void>;
}
