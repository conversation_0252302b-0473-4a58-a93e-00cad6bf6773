import z from 'zod';
import { Message } from './api';

export type TaskStatus = 'waiting' | 'pending' | 'planned' | 'completed' | 'error';

export const TaskBaseSchema = z.object({
  id: z.string(),
  goal: z.string(),
  output: z.string().nullable().optional(),
  inputFromTasks: z.array(z.string()).optional(),
  repeat: z.number().nullable().optional(),
});

export const TaskSchema = TaskBaseSchema.extend({
  foreach: TaskBaseSchema.nullable().optional(),
});
export type Task = z.infer<typeof TaskSchema>;

export interface TaskResult {
  id: string;
  status: 'completed' | 'error';
  output?: string;
}

export interface RuntimeInput extends TaskResult {
  runtimeInput?: string;
}

export interface TaskRunOptions {
  plan: boolean;
  input: string;
}

export interface TaskAgentRunConfig {
  user: string;
  continue: string;
  error: string;
}

export interface TaskNode {
  id: string;
  task: Task;
  nested_tasks: TaskNode[];
  atomic: boolean;
  history: Message[];
  parent: TaskNode | null;
  depth: number;

  started_at?: string;
  completed_at?: string;
  runtimeInput?: string;
  result: TaskResult | null;

  run(onTaskComplete?: (result: TaskResult[]) => void): Promise<TaskResult>;
  finish(result: TaskResult): void;
  getTaskInputs(): RuntimeInput[];
}
