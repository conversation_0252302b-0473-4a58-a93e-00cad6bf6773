import { Message, MessageContent, MessageContentPart } from '../types/api';

export function extractMessageText(message: Message): string {
  if (message.role === 'user') {
    return extractTextFromContent(message.content);
  }
  return message.content || '';
}

// add a unified content parser for message.content field
export function parseMessageContent(
  content: string | null | undefined
): string | MessageContentPart[] {
  if (!content) {
    return '';
  }
  try {
    const parsed = JSON.parse(content);
    if (Array.isArray(parsed)) {
      return parsed as MessageContentPart[];
    }
    return content;
  } catch {
    return content;
  }
}

// extract text from multi-modal content
export function extractTextFromContent(content: MessageContent | null | undefined): string {
  if (!content) {
    return '';
  }

  if (Array.isArray(content)) {
    return extractTextFromContentPart(content);
  }

  try {
    const parsed = JSON.parse(content);
    if (Array.isArray(parsed)) {
      return extractTextFromContentPart(parsed);
    }
  } catch {}
  return content;
}

function extractTextFromContentPart(parts: MessageContentPart[]): string {
  return parts
    .map(part => {
      if (part.type === 'text' && part.text) {
        return part.text;
      }
      return '';
    })
    .join('\n')
    .trim();
}
