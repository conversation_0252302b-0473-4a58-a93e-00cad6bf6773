import { z } from 'zod';
import {
  type CreateConversationRequest,
  type CreateConversationResponse,
  type DeleteConversationRequest,
  type DeleteConversationResponse,
  type ListConversationsResponse,
  type SyncUserDataRequest,
  type SyncUserDataResponse,
  CreateConversationResponseSchema,
  DeleteConversationResponseSchema,
  ListConversationsResponseSchema,
  SyncUserDataResponseSchema,
  type GetUserResponse,
  type RotateApiKeyResponse,
  type GetCreditDailyResponse,
  type RedeemCouponResponse,
  type StripeCheckoutResponse,
  GetUserResponseSchema,
  RotateApiKeyResponseSchema,
  GetCreditDailyResponseSchema,
  RedeemCouponResponseSchema,
  StripeCheckoutResponseSchema,
  ListConversationsRequest,
  GetCreditDailyRequest,
  SaveMessageResponseSchemaV2,
  SaveMessageResponseV2,
  SaveMessageRequestV2,
  TemplateRegisterRequest,
  TemplateRegisterResponseSchema,
  TemplateRegisterResponse,
  GetTemplatesRequest,
  GetTemplatesResponse,
  GetTemplatesResponseSchema,
  UpdateConversationRequest,
  UpdateConversationResponse,
  UpdateConversationResponseSchema,
  SaveMiniAppResponseSchema,
  SaveMiniAppRequest,
  SaveMiniAppResponse,
  type UpdateMiniAppRequest,
  type UpdateMiniAppResponse,
  UpdateMiniAppResponseSchema,
} from '../types/api';
import {
  AddMemoryRequest,
  AddMemoryResponse,
  AddMemoryResponseSchema,
  SearchMemoryRequest,
  SearchMemoryRequestV2,
  SearchMemoryResponse,
  SearchMemoryResponseSchema,
} from '../types/memory';

export interface APIClientConfig {
  baseUrl: string;
  apiKey?: string;
  authToken?: string;
}

export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public data?: unknown
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export class APIClient {
  private baseUrl: string;
  private headers: Record<string, string>;

  constructor(config: APIClientConfig) {
    this.baseUrl = config.baseUrl.endsWith('/') ? config.baseUrl.slice(0, -1) : config.baseUrl;
    this.headers = {
      'Content-Type': 'application/json',
    };

    if (config.apiKey) {
      this.headers['x-api-key'] = config.apiKey;
    }
    if (config.authToken) {
      this.headers['Authorization'] = `Bearer ${config.authToken}`;
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    schema?: z.ZodType<T>
  ): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        ...this.headers,
        ...options.headers,
      },
    });

    if (!response.ok) {
      let errorData;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        try {
          errorData = await response.json();
        } catch {
          errorData = 'Invalid JSON response';
        }
      } else {
        try {
          errorData = await response.text();
        } catch {
          errorData = 'Could not read error response';
        }
      }

      throw new APIError(
        errorData || errorData?.error?.message || 'API request failed',
        response.status,
        errorData
      );
    }

    const data = await response.json();
    if (schema) {
      const result = schema.safeParse(data);
      if (!result.success) {
        const metadata = {
          data,
          schema,
          result,
        };
        throw new APIError(`Invalid response data ${JSON.stringify(metadata)}`, 500, result.error);
      }
      return result.data;
    }
    return data as T;
  }

  // Conversation endpoints
  async createConversation(data: CreateConversationRequest): Promise<CreateConversationResponse> {
    return this.request(
      '/v1/conversation/create',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      CreateConversationResponseSchema
    );
  }

  async deleteConversation(data: DeleteConversationRequest): Promise<DeleteConversationResponse> {
    return this.request(
      '/v1/conversation/delete',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      DeleteConversationResponseSchema
    );
  }

  async listConversations(params?: ListConversationsRequest): Promise<ListConversationsResponse> {
    const queryParams = new URLSearchParams();
    if (params?.startFrom) queryParams.append('startFrom', params.startFrom.toString());

    const queryString = queryParams.toString();
    const endpoint = queryString ? `/v1/conversation/list?${queryString}` : '/v1/conversation/list';

    return this.request(
      endpoint,
      {
        method: 'GET',
      },
      ListConversationsResponseSchema
    );
  }

  async syncUserData(params?: SyncUserDataRequest): Promise<SyncUserDataResponse> {
    const queryParams = new URLSearchParams();
    if (params?.startFrom) queryParams.append('startFrom', params.startFrom.toString());

    const queryString = queryParams.toString();
    const endpoint = queryString ? `/v1/user/sync_data?${queryString}` : '/v1/user/sync_data';

    return this.request(
      endpoint,
      {
        method: 'GET',
      },
      SyncUserDataResponseSchema
    );
  }

  async updateConversation(data: UpdateConversationRequest): Promise<UpdateConversationResponse> {
    return this.request(
      '/v1/conversation/update',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      UpdateConversationResponseSchema
    );
  }

  // User endpoints
  async getUser(): Promise<GetUserResponse> {
    return this.request('/v1/user', { method: 'GET' }, GetUserResponseSchema);
  }

  async rotateApiKey(): Promise<RotateApiKeyResponse> {
    return this.request('/v1/user/rotate_api_key', { method: 'POST' }, RotateApiKeyResponseSchema);
  }

  async getCreditDaily(params?: GetCreditDailyRequest): Promise<GetCreditDailyResponse> {
    const queryParams = new URLSearchParams();
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);

    const queryString = queryParams.toString();
    const endpoint = queryString ? `/v1/user/credit_daily?${queryString}` : '/v1/user/credit_daily';

    return this.request(endpoint, { method: 'GET' }, GetCreditDailyResponseSchema);
  }

  async redeemCoupon(code: string): Promise<RedeemCouponResponse> {
    return this.request(
      '/v1/user/redeem_coupon_code',
      {
        method: 'POST',
        body: JSON.stringify({ code }),
      },
      RedeemCouponResponseSchema
    );
  }

  // Stripe endpoints
  async createCheckout(amount: number): Promise<StripeCheckoutResponse> {
    return this.request(
      '/v1/stripe/checkout',
      {
        method: 'POST',
        body: JSON.stringify({ amount }),
      },
      StripeCheckoutResponseSchema
    );
  }

  // Message endpoints
  async saveMessageV2(data: SaveMessageRequestV2): Promise<SaveMessageResponseV2> {
    return this.request(
      '/v2/message/save',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      SaveMessageResponseSchemaV2
    );
  }

  async searchMemory(data: SearchMemoryRequest): Promise<SearchMemoryResponse> {
    const queryParams = new URLSearchParams();
    queryParams.append('text', data.text);
    queryParams.append('limit', data.limit.toString());
    queryParams.append('conversationId', data.conversationId.toString());

    const queryString = queryParams.toString();
    return this.request(
      `/v1/memory/search?${queryString}`,
      {
        method: 'GET',
      },
      SearchMemoryResponseSchema
    );
  }

  async searchMemoryV2(data: SearchMemoryRequestV2): Promise<SearchMemoryResponse> {
    return this.request(
      '/v2/memory/search',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      SearchMemoryResponseSchema
    );
  }

  async addMemory(data: AddMemoryRequest): Promise<AddMemoryResponse> {
    return this.request(
      '/v1/memory/add',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      AddMemoryResponseSchema
    );
  }

  async registerTemplate(data: TemplateRegisterRequest): Promise<TemplateRegisterResponse> {
    return this.request(
      '/v1/template/register',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      TemplateRegisterResponseSchema
    );
  }

  async getTemplates(data: GetTemplatesRequest): Promise<GetTemplatesResponse> {
    return this.request(
      '/v1/templates',
      {
        method: 'GET',
      },
      GetTemplatesResponseSchema
    );
  }

  // Miniapp endpoints
  async saveMiniApp(data: SaveMiniAppRequest): Promise<SaveMiniAppResponse> {
    return this.request(
      '/v1/miniapp/save',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      SaveMiniAppResponseSchema
    );
  }

  async updateMiniApp(data: UpdateMiniAppRequest): Promise<UpdateMiniAppResponse> {
    return this.request(
      '/v1/miniapp/update',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      UpdateMiniAppResponseSchema
    );
  }
}
