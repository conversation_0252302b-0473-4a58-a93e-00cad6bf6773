# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_TYPE=postgresql
DATABASE_URL=postgresql://postgres:<EMAIL>:24343/test

# Supabase Configuration (optional, if using Supabase)
# DATABASE_TYPE=supabase
# SUPABASE_URL=your_supabase_url
# SUPABASE_KEY=your_supabase_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# Security
CORS_ORIGIN=*
API_KEY=your_api_key_optional 