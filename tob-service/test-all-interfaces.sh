#!/bin/bash

# ToB Service - Comprehensive Interface Testing Script
# Tests all available endpoints with proper authentication and error handling

BASE_URL="http://localhost:3000"
API_KEY="test-api-key-1"
TEST_USER_ID="test-user-123"

echo "=========================================="
echo "ToB Service - Comprehensive Interface Test"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo "API Key: $API_KEY"
echo "Test User ID: $TEST_USER_ID"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_result() {
    local status=$1
    local message=$2
    if [ "$status" = "SUCCESS" ]; then
        echo -e "${GREEN}✓ $message${NC}"
    elif [ "$status" = "ERROR" ]; then
        echo -e "${RED}✗ $message${NC}"
    elif [ "$status" = "WARNING" ]; then
        echo -e "${YELLOW}⚠ $message${NC}"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}ℹ $message${NC}"
    fi
}

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "\n${BLUE}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    # Create temporary file for response
    temp_file=$(mktemp)
    
    if [ "$method" = "GET" ]; then
        curl -s -w "\nHTTPSTATUS:%{http_code}" -X GET "$BASE_URL$endpoint" \
            -H "X-API-Key: $API_KEY" \
            -H "Content-Type: application/json" > "$temp_file"
    else
        curl -s -w "\nHTTPSTATUS:%{http_code}" -X $method "$BASE_URL$endpoint" \
            -H "X-API-Key: $API_KEY" \
            -H "Content-Type: application/json" \
            -d "$data" > "$temp_file"
    fi
    
    # Extract status code and response body
    status_code=$(grep "HTTPSTATUS:" "$temp_file" | cut -d: -f2)
    body=$(grep -v "HTTPSTATUS:" "$temp_file")
    
    echo "Status Code: $status_code"
    echo "Response: $body"
    
    if [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        print_result "SUCCESS" "$description completed successfully"
    else
        print_result "ERROR" "$description failed with status $status_code"
    fi
    
    # Clean up temp file
    rm "$temp_file"
    
    echo "----------------------------------------"
}

# Test 1: Health Check
echo -e "\n${YELLOW}1. Testing Health Check${NC}"
test_endpoint "GET" "/health" "" "Health Check"

# Test 2: Create Conversation
echo -e "\n${YELLOW}2. Testing Conversation Management${NC}"
test_endpoint "POST" "/v1/conversation/create" '{"id": 123456789}' "Create Conversation"

# Test 3: List Conversations
test_endpoint "GET" "/v1/conversation/list" "" "List Conversations"

# Test 4: Save Message
echo -e "\n${YELLOW}3. Testing Message Management${NC}"
test_endpoint "POST" "/v2/message/save" '{
  "message": {
    "id": 987654321,
    "conversation_id": 123456789,
    "role": "user",
    "content": "Hello, how are you?",
    "status": "completed"
  }
}' "Save Message"

# Test 5: Chat Completions
echo -e "\n${YELLOW}4. Testing Chat Completions${NC}"
test_endpoint "POST" "/v1/chat/completions" '{
  "model": "gpt-4",
  "messages": [
    {"role": "user", "content": "Hello, this is a test message"}
  ],
  "temperature": 0.7,
  "max_tokens": 100
}' "Chat Completions"

# Test 6: Memory Search (v1)
echo -e "\n${YELLOW}5. Testing Memory Management${NC}"
test_endpoint "GET" "/v1/memory/search?text=test&limit=3" "" "Memory Search (v1)"

# Test 7: Add Memory
test_endpoint "POST" "/v1/memory/add" '{
  "messages": [
    {
      "role": "user",
      "content": "This is a test memory message"
    },
    {
      "role": "assistant", 
      "content": "This is a test response"
    }
  ],
  "config": {
    "metadata": {
      "memoryType": "conversation",
      "conversationId": "123456789"
    }
  }
}' "Add Memory"

# Test 8: Memory Search (v2)
test_endpoint "POST" "/v2/memory/search" '{
  "text": "test search",
  "limit": 3,
  "filters": {
    "conversationId": "123456789"
  }
}' "Memory Search (v2)"

# Test 10: Delete Conversation
echo -e "\n${YELLOW}7. Testing Conversation Deletion${NC}"
test_endpoint "POST" "/v1/conversation/delete" '{"id": 123456789}' "Delete Conversation"

# Test 11: Error Cases
echo -e "\n${YELLOW}8. Testing Error Cases${NC}"

# Test without API key
echo -e "\n${BLUE}Testing: Unauthorized Access (no API key)${NC}"
temp_file=$(mktemp)
curl -s -w "\nHTTPSTATUS:%{http_code}" -X GET "$BASE_URL/health" \
    -H "Content-Type: application/json" > "$temp_file"
status_code=$(grep "HTTPSTATUS:" "$temp_file" | cut -d: -f2)
body=$(grep -v "HTTPSTATUS:" "$temp_file")
echo "Status Code: $status_code"
echo "Response: $body"
if [ "$status_code" -eq 401 ]; then
    print_result "SUCCESS" "Unauthorized access properly handled"
else
    print_result "WARNING" "Unauthorized access not properly handled"
fi
rm "$temp_file"

# Test invalid endpoint
echo -e "\n${BLUE}Testing: Invalid Endpoint${NC}"
temp_file=$(mktemp)
curl -s -w "\nHTTPSTATUS:%{http_code}" -X GET "$BASE_URL/invalid-endpoint" \
    -H "X-API-Key: $API_KEY" \
    -H "Content-Type: application/json" > "$temp_file"
status_code=$(grep "HTTPSTATUS:" "$temp_file" | cut -d: -f2)
body=$(grep -v "HTTPSTATUS:" "$temp_file")
echo "Status Code: $status_code"
echo "Response: $body"
if [ "$status_code" -eq 404 ]; then
    print_result "SUCCESS" "Invalid endpoint properly handled"
else
    print_result "WARNING" "Invalid endpoint not properly handled"
fi
rm "$temp_file"

# Test malformed JSON
echo -e "\n${BLUE}Testing: Malformed JSON${NC}"
temp_file=$(mktemp)
curl -s -w "\nHTTPSTATUS:%{http_code}" -X POST "$BASE_URL/v1/conversation/create" \
    -H "X-API-Key: $API_KEY" \
    -H "Content-Type: application/json" \
    -d '{invalid json}' > "$temp_file"
status_code=$(grep "HTTPSTATUS:" "$temp_file" | cut -d: -f2)
body=$(grep -v "HTTPSTATUS:" "$temp_file")
echo "Status Code: $status_code"
echo "Response: $body"
if [ "$status_code" -eq 400 ]; then
    print_result "SUCCESS" "Malformed JSON properly handled"
else
    print_result "WARNING" "Malformed JSON not properly handled"
fi
rm "$temp_file"

echo -e "\n=========================================="
echo "Comprehensive Interface Testing Completed"
echo "=========================================="
echo ""
print_result "INFO" "All endpoints have been tested"
print_result "INFO" "Check the output above for detailed results"
echo ""
echo "Note: Some tests may fail if the database is not properly configured"
echo "or if the service is not running. This is expected behavior." 