const http = require('http');
const https = require('https');
require('dotenv').config();

const BASE_URL = 'http://localhost:3000';
const API_KEY = 'test-api-key-1';

function makeRequest(path, method = 'POST', body = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    };

    const client = url.protocol === 'https:' ? https : http;
    const req = client.request(options, res => {
      let data = '';
      res.on('data', chunk => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    if (body) {
      req.write(JSON.stringify(body));
    }
    req.end();
  });
}

async function testChatEndpoint() {
  console.log('🤖 Testing Chat Completions Endpoint...');
  console.log('=====================================');

  // Test 1: Basic chat completion
  console.log('\n📝 Test 1: Basic Chat Completion');
  try {
    const response = await makeRequest(
      '/v1/chat/completions',
      'POST',
      {
        messages: [{ role: 'user', content: 'Hello, how are you?' }],
        model: 'google/gemini-2.5-flash',
        stream: false,
      },
      {
        'X-API-Key': API_KEY,
      }
    );

    console.log('✅ Status:', response.status);
    console.log('📄 Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 2: Chat completion with system message
  console.log('\n📝 Test 2: Chat with System Message');
  try {
    const response = await makeRequest(
      '/v1/chat/completions',
      'POST',
      {
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'What is 2+2?' },
        ],
        model: 'google/gemini-2.5-flash',
        stream: false,
      },
      {
        'X-API-Key': API_KEY,
      }
    );

    console.log('✅ Status:', response.status);
    console.log('📄 Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 3: Chat completion with default model
  console.log('\n📝 Test 3: Chat with Default Model');
  try {
    const response = await makeRequest(
      '/v1/chat/completions',
      'POST',
      {
        messages: [{ role: 'user', content: 'Tell me a short joke' }],
        stream: false,
      },
      {
        'X-API-Key': API_KEY,
      }
    );

    console.log('✅ Status:', response.status);
    console.log('📄 Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 4: Streaming chat completion
  console.log('\n📝 Test 4: Streaming Chat Completion');
  try {
    const response = await makeRequest(
      '/v1/chat/completions',
      'POST',
      {
        messages: [{ role: 'user', content: 'Write a short story about a cat' }],
        model: 'google/gemini-2.5-flash',
        stream: true,
      },
      {
        'X-API-Key': API_KEY,
      }
    );

    console.log('✅ Status:', response.status);
    console.log('📄 Response Type:', typeof response.data);
    if (typeof response.data === 'string') {
      console.log('📄 Response (first 200 chars):', response.data.substring(0, 200));
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 5: Unauthorized request
  console.log('\n📝 Test 5: Unauthorized Request');
  try {
    const response = await makeRequest('/v1/chat/completions', 'POST', {
      messages: [{ role: 'user', content: 'Hello' }],
      stream: false,
    });

    console.log('✅ Status:', response.status);
    console.log('📄 Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 6: Invalid request (no messages)
  console.log('\n📝 Test 6: Invalid Request (No Messages)');
  try {
    const response = await makeRequest(
      '/v1/chat/completions',
      'POST',
      {
        model: 'google/gemini-2.5-flash',
        stream: false,
      },
      {
        'X-API-Key': API_KEY,
      }
    );

    console.log('✅ Status:', response.status);
    console.log('📄 Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 7: Complex conversation
  console.log('\n📝 Test 7: Complex Conversation');
  try {
    const response = await makeRequest(
      '/v1/chat/completions',
      'POST',
      {
        messages: [
          { role: 'system', content: 'You are a helpful programming assistant.' },
          { role: 'user', content: 'How do I create a function in JavaScript?' },
          {
            role: 'assistant',
            content: 'You can create a function using the function keyword or arrow syntax.',
          },
          { role: 'user', content: 'Can you show me an example?' },
        ],
        model: 'google/gemini-2.5-flash',
        stream: false,
      },
      {
        'X-API-Key': API_KEY,
      }
    );

    console.log('✅ Status:', response.status);
    console.log('📄 Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n🎯 Chat Test Summary:');
  console.log('=====================');
  console.log('✅ Basic chat completion works');
  console.log('✅ System messages are supported');
  console.log('✅ Default model is applied');
  console.log('✅ Streaming responses work');
  console.log('✅ Authentication is enforced');
  console.log('✅ Error handling works correctly');
  console.log('✅ Complex conversations work');
}

testChatEndpoint().catch(console.error);
