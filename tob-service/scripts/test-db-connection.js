const { Client } = require('pg');
require('dotenv').config();

async function testDatabaseConnection() {
  console.log('🔍 Testing PostgreSQL Database Connection...');

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    // Test connection
    console.log('📡 Connecting to database...');
    await client.connect();
    console.log('✅ Database connection successful!');

    // Check if tables exist
    console.log('\n📋 Checking existing tables...');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('conversations', 'messages')
    `);

    console.log(
      'Existing tables:',
      tablesResult.rows.map(row => row.table_name)
    );

    // Create tables if they don't exist
    if (tablesResult.rows.length === 0) {
      console.log('\n🏗️  Creating tables...');
      const fs = require('fs');
      const sql = fs.readFileSync('./scripts/init-postgresql.sql', 'utf8');
      await client.query(sql);
      console.log('✅ Tables created successfully!');
    } else {
      console.log('✅ Tables already exist!');
    }

    // Test basic operations
    console.log('\n🧪 Testing basic operations...');

    // Test conversation creation
    const testConversationId = `test-conv-${Date.now()}`;
    const conversationResult = await client.query(
      `
      INSERT INTO conversations (id, local_id, user_id, status) 
      VALUES ($1, $2, $3, $4) 
      RETURNING local_id
    `,
      [testConversationId, 1, 'test-user', 'active']
    );

    console.log('✅ Conversation creation test passed');

    // Test message creation
    const testMessageId = `test-msg-${Date.now()}`;
    const messageResult = await client.query(
      `
      INSERT INTO messages (id, local_id, conversation_id, role, content) 
      VALUES ($1, $2, $3, $4, $5) 
      RETURNING local_id
    `,
      [testMessageId, 1, testConversationId, 'user', 'Hello, this is a test message']
    );

    console.log('✅ Message creation test passed');

    // Clean up test data
    await client.query('DELETE FROM messages WHERE id = $1', [testMessageId]);
    await client.query('DELETE FROM conversations WHERE id = $1', [testConversationId]);
    console.log('🧹 Test data cleaned up');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await client.end();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the test
testDatabaseConnection().catch(console.error);
