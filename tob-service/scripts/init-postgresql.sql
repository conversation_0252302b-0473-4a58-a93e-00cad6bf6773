-- Create conversations table for PostgreSQL
CREATE TABLE IF NOT EXISTS conversations (
    id TEXT PRIMARY KEY,
    local_id BIGINT NOT NULL,
    user_id TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    last_selected_at BIGINT,
    title TEXT
);

-- Create messages table for PostgreSQL
CREATE TABLE IF NOT EXISTS messages (
    id TEXT PRIMARY KEY,
    local_id BIGINT NOT NULL,
    conversation_id TEXT NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role TEXT NOT NULL,
    content TEXT NOT NULL,
    tool_calls TEXT,
    tool_call_id TEXT,
    name TEXT,
    status VARCHAR(20) DEFAULT 'completed',
    error TEXT,
    reasoning TEXT,
    actor TEXT DEFAULT 'user',
    agent_id TEXT,
    run_id TEXT,
    task_id TEXT
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_role ON messages(role);
