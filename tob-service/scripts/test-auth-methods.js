const http = require('http');
const https = require('https');
require('dotenv').config();

const BASE_URL = 'http://localhost:3000';
const API_KEY = 'test-api-key-1';
const BEARER_TOKEN = 'test-bearer-token-1';

function makeRequest(path, method = 'GET', body = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    };

    const client = url.protocol === 'https:' ? https : http;
    const req = client.request(options, res => {
      let data = '';
      res.on('data', chunk => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    if (body) {
      req.write(JSON.stringify(body));
    }
    req.end();
  });
}

async function testAuthMethods() {
  console.log('🔐 Testing Authentication Methods...');
  console.log('=====================================');

  // Test 1: API Key Authentication
  console.log('\n🔑 Testing API Key Authentication...');
  try {
    const apiKeyResponse = await makeRequest('/health', 'GET', null, {
      'X-API-Key': API_KEY,
    });
    console.log('✅ API Key Auth - Status:', apiKeyResponse.status);
    console.log('   Response:', JSON.stringify(apiKeyResponse.data, null, 2));
  } catch (error) {
    console.log('❌ API Key Auth failed:', error.message);
  }

  // Test 2: Bearer Token Authentication
  console.log('\n🎫 Testing Bearer Token Authentication...');
  try {
    const bearerResponse = await makeRequest('/health', 'GET', null, {
      Authorization: `Bearer ${BEARER_TOKEN}`,
    });
    console.log('✅ Bearer Token Auth - Status:', bearerResponse.status);
    console.log('   Response:', JSON.stringify(bearerResponse.data, null, 2));
  } catch (error) {
    console.log('❌ Bearer Token Auth failed:', error.message);
  }

  // Test 3: No Authentication (should fail)
  console.log('\n🚫 Testing No Authentication...');
  try {
    const noAuthResponse = await makeRequest('/health', 'GET');
    console.log('⚠️  No Auth - Status:', noAuthResponse.status);
    console.log('   Response:', JSON.stringify(noAuthResponse.data, null, 2));
  } catch (error) {
    console.log('❌ No Auth failed:', error.message);
  }

  // Test 4: API Key with Conversation Creation
  console.log('\n💬 Testing API Key with Conversation Creation...');
  try {
    const conversationResponse = await makeRequest(
      '/v1/conversation/create',
      'POST',
      {
        id: Date.now(),
      },
      {
        'X-API-Key': API_KEY,
      }
    );
    console.log('✅ API Key Conversation - Status:', conversationResponse.status);
    console.log('   Response:', JSON.stringify(conversationResponse.data, null, 2));
  } catch (error) {
    console.log('❌ API Key Conversation failed:', error.message);
  }

  // Test 5: Bearer Token with Conversation Creation
  console.log('\n💬 Testing Bearer Token with Conversation Creation...');
  try {
    const conversationResponse = await makeRequest(
      '/v1/conversation/create',
      'POST',
      {
        id: Date.now(),
      },
      {
        Authorization: `Bearer ${BEARER_TOKEN}`,
      }
    );
    console.log('✅ Bearer Token Conversation - Status:', conversationResponse.status);
    console.log('   Response:', JSON.stringify(conversationResponse.data, null, 2));
  } catch (error) {
    console.log('❌ Bearer Token Conversation failed:', error.message);
  }

  // Test 6: API Key with Message Save
  console.log('\n💾 Testing API Key with Message Save...');
  try {
    const messageResponse = await makeRequest(
      '/v2/message/save',
      'POST',
      {
        message: {
          id: Date.now(),
          conversation_id: Date.now(),
          role: 'user',
          content: 'Test message via API Key',
        },
      },
      {
        'X-API-Key': API_KEY,
      }
    );
    console.log('✅ API Key Message - Status:', messageResponse.status);
    console.log('   Response:', JSON.stringify(messageResponse.data, null, 2));
  } catch (error) {
    console.log('❌ API Key Message failed:', error.message);
  }

  // Test 7: Bearer Token with Message Save
  console.log('\n💾 Testing Bearer Token with Message Save...');
  try {
    const messageResponse = await makeRequest(
      '/v2/message/save',
      'POST',
      {
        message: {
          id: Date.now(),
          conversation_id: Date.now(),
          role: 'user',
          content: 'Test message via Bearer Token',
        },
      },
      {
        Authorization: `Bearer ${BEARER_TOKEN}`,
      }
    );
    console.log('✅ Bearer Token Message - Status:', messageResponse.status);
    console.log('   Response:', JSON.stringify(messageResponse.data, null, 2));
  } catch (error) {
    console.log('❌ Bearer Token Message failed:', error.message);
  }

  console.log('\n🎯 Authentication Test Summary:');
  console.log('================================');
  console.log('✅ Both API Key and Bearer Token authentication methods are working');
  console.log('✅ Unauthenticated requests are properly rejected');
  console.log('✅ Authentication works for all endpoints');
}

testAuthMethods().catch(console.error);
