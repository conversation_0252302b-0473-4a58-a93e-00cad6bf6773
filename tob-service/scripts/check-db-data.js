const { Client } = require('pg');
require('dotenv').config();

async function checkDatabaseData() {
  console.log('🔍 Checking Database Data...');

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    console.log('✅ Database connection successful!');

    // Check conversations table
    console.log('\n📋 Conversations Table:');
    console.log('======================');
    const conversationsResult = await client.query('SELECT COUNT(*) as count FROM conversations');
    console.log(`Total conversations: ${conversationsResult.rows[0].count}`);

    if (parseInt(conversationsResult.rows[0].count) > 0) {
      const conversations = await client.query(
        'SELECT id, local_id, user_id, status, created_at FROM conversations ORDER BY created_at DESC LIMIT 5'
      );
      console.log('Recent conversations:');
      conversations.rows.forEach((row, index) => {
        console.log(
          `${index + 1}. ID: ${row.id}, Local ID: ${row.local_id}, User: ${row.user_id}, Status: ${row.status}, Created: ${row.created_at}`
        );
      });
    }

    // Check messages table
    console.log('\n💬 Messages Table:');
    console.log('==================');
    const messagesResult = await client.query('SELECT COUNT(*) as count FROM messages');
    console.log(`Total messages: ${messagesResult.rows[0].count}`);

    if (parseInt(messagesResult.rows[0].count) > 0) {
      const messages = await client.query(
        'SELECT id, local_id, conversation_id, role, content, status, created_at FROM messages ORDER BY created_at DESC LIMIT 5'
      );
      console.log('Recent messages:');
      messages.rows.forEach((row, index) => {
        console.log(
          `${index + 1}. ID: ${row.id}, Local ID: ${row.local_id}, Conversation: ${row.conversation_id}, Role: ${row.role}, Status: ${row.status}, Created: ${row.created_at}`
        );
        console.log(
          `   Content: ${row.content?.substring(0, 50)}${row.content?.length > 50 ? '...' : ''}`
        );
      });
    }

    // Check table structure
    console.log('\n🏗️ Table Structure:');
    console.log('==================');

    const conversationsSchema = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'conversations' 
      ORDER BY ordinal_position
    `);
    console.log('Conversations table columns:');
    conversationsSchema.rows.forEach(row => {
      console.log(
        `  ${row.column_name}: ${row.data_type} ${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`
      );
    });

    const messagesSchema = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'messages' 
      ORDER BY ordinal_position
    `);
    console.log('\nMessages table columns:');
    messagesSchema.rows.forEach(row => {
      console.log(
        `  ${row.column_name}: ${row.data_type} ${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`
      );
    });
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await client.end();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the check
checkDatabaseData().catch(console.error);
