#!/bin/bash

echo "🚀 Starting ToB Service Tests..."
echo "================================"

# Check if service is running
echo "🔍 Checking if service is running..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ Service is running"
else
    echo "❌ Service is not running. Please start the service first:"
    echo "   npm run dev"
    exit 1
fi

echo ""
echo "🧪 Running API tests..."
node scripts/test-api-complete.js

echo ""
echo "🧪 Running database tests..."
node scripts/test-db-connection.js

echo ""
echo "✅ All tests completed!" 