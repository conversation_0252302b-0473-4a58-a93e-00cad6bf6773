const http = require('http');
const https = require('https');
const url = require('url');
require('dotenv').config();

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const TEST_USER_ID = 'test-user-' + Date.now();
const testApiKey = 'test-api-key-1'; // Use the default API key from config
const OPENAI_MODEL = process.env.OPENAI_MODEL || 'gpt-4'; // Use environment model config

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(options.url);
    const isHttps = parsedUrl.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: parsedUrl.path,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    const req = client.request(requestOptions, res => {
      let responseData = '';

      res.on('data', chunk => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData,
            headers: res.headers,
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers,
          });
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testHealthEndpoint() {
  console.log('\n🏥 Testing Health Endpoint...');
  try {
    const response = await makeRequest({
      url: `${BASE_URL}/health`,
      method: 'GET',
    });
    console.log('✅ Health check passed');
    console.log('Status:', response.data.status);
    console.log('Services:', response.data.services);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function testConversationEndpoints() {
  console.log('\n💬 Testing Conversation Endpoints...');

  const testConversationId = Date.now();

  try {
    // Test create conversation
    console.log('📝 Creating conversation...');
    const createResponse = await makeRequest(
      {
        url: `${BASE_URL}/v1/conversation/create`,
        method: 'POST',
        headers: {
          'X-API-Key': testApiKey,
        },
      },
      {
        id: testConversationId,
      }
    );

    if (createResponse.status === 200) {
      console.log('✅ Conversation created successfully');
    } else {
      console.log('⚠️ Conversation creation response:', createResponse.data);
    }

    // Test list conversations
    console.log('📋 Listing conversations...');
    const listResponse = await makeRequest({
      url: `${BASE_URL}/v1/conversation/list?startFrom=0`,
      method: 'GET',
      headers: {
        'X-API-Key': testApiKey,
      },
    });

    if (listResponse.status === 200) {
      console.log('✅ Conversations listed successfully');
      console.log('Found conversations:', listResponse.data.conversations?.length || 0);
    } else {
      console.log('⚠️ List conversations response:', listResponse.data);
    }

    // Test delete conversation
    console.log('🗑️ Deleting conversation...');
    const deleteResponse = await makeRequest(
      {
        url: `${BASE_URL}/v1/conversation/delete`,
        method: 'POST',
        headers: {
          'X-API-Key': testApiKey,
        },
      },
      {
        id: testConversationId,
      }
    );

    if (deleteResponse.status === 200) {
      console.log('✅ Conversation deleted successfully');
    } else {
      console.log('⚠️ Delete conversation response:', deleteResponse.data);
    }

    return true;
  } catch (error) {
    console.error('❌ Conversation test failed:', error.message);
    return false;
  }
}

async function testMessageEndpoints() {
  console.log('\n💭 Testing Message Endpoints...');

  const testConversationId = Date.now();
  const testMessageId = Date.now();

  try {
    // First create a conversation
    console.log('📝 Creating conversation for message test...');
    await makeRequest(
      {
        url: `${BASE_URL}/v1/conversation/create`,
        method: 'POST',
        headers: {
          'X-API-Key': testApiKey,
        },
      },
      {
        id: testConversationId,
      }
    );

    // Test save message
    console.log('💾 Saving message...');
    const saveResponse = await makeRequest(
      {
        url: `${BASE_URL}/v2/message/save`,
        method: 'POST',
        headers: {
          'X-API-Key': testApiKey,
        },
      },
      {
        message: {
          id: testMessageId,
          conversation_id: testConversationId,
          role: 'user',
          content: 'Hello, this is a test message',
          status: 'completed',
        },
      }
    );

    if (saveResponse.status === 200) {
      console.log('✅ Message saved successfully');
    } else {
      console.log('⚠️ Save message response:', saveResponse.data);
    }

    // Clean up
    await makeRequest(
      {
        url: `${BASE_URL}/v1/conversation/delete`,
        method: 'POST',
        headers: {
          'X-API-Key': testApiKey,
        },
      },
      {
        id: testConversationId,
      }
    );

    return true;
  } catch (error) {
    console.error('❌ Message test failed:', error.message);
    return false;
  }
}

async function testMemoryEndpoints() {
  console.log('\n🧠 Testing Memory Endpoints...');

  try {
    // Test memory search
    console.log('🔍 Testing memory search...');
    const searchResponse = await makeRequest({
      url: `${BASE_URL}/v1/memory/search?text=test`,
      method: 'GET',
      headers: {
        'X-API-Key': testApiKey,
      },
    });

    if (searchResponse.status === 200) {
      console.log('✅ Memory search successful');
    } else {
      console.log('⚠️ Memory search response:', searchResponse.data);
    }

    // Test memory add
    console.log('➕ Testing memory add...');
    const addResponse = await makeRequest(
      {
        url: `${BASE_URL}/v1/memory/add`,
        method: 'POST',
        headers: {
          'X-API-Key': testApiKey,
        },
      },
      {
        messages: [
          {
            role: 'user',
            content: 'This is a test memory entry',
          },
        ],
        config: {
          metadata: {
            memoryType: 'semantic',
            timestamp: Date.now(),
          },
        },
      }
    );

    if (addResponse.status === 200) {
      console.log('✅ Memory add successful');
    } else {
      console.log('⚠️ Memory add response:', addResponse.data);
    }

    // Test v2 memory search
    console.log('🔍 Testing v2 memory search...');
    const searchV2Response = await makeRequest(
      {
        url: `${BASE_URL}/v2/memory/search`,
        method: 'POST',
        headers: {
          'X-API-Key': testApiKey,
        },
      },
      {
        text: 'test',
        config: {
          limit: 10,
        },
      }
    );

    if (searchV2Response.status === 200) {
      console.log('✅ V2 memory search successful');
    } else {
      console.log('⚠️ V2 memory search response:', searchV2Response.data);
    }

    return true;
  } catch (error) {
    console.error('❌ Memory test failed:', error.message);
    return false;
  }
}

async function testErrorHandling() {
  console.log('\n🚨 Testing Error Handling...');

  try {
    // Test invalid endpoint
    console.log('🔍 Testing 404 error...');
    const invalidResponse = await makeRequest({
      url: `${BASE_URL}/invalid-endpoint`,
      method: 'GET',
    });

    if (invalidResponse.status === 404) {
      console.log('✅ 404 error handling works correctly');
    } else {
      console.log('⚠️ Unexpected response for invalid endpoint:', invalidResponse.status);
    }
  } catch (error) {
    console.log('⚠️ Error for invalid endpoint:', error.message);
  }

  try {
    // Test unauthorized access
    console.log('🔍 Testing 401 error...');
    const unauthorizedResponse = await makeRequest({
      url: `${BASE_URL}/v1/conversation/list`,
      method: 'GET',
    });

    if (unauthorizedResponse.status === 401) {
      console.log('✅ 401 unauthorized handling works correctly');
    } else {
      console.log('⚠️ Unexpected response for unauthorized access:', unauthorizedResponse.status);
    }
  } catch (error) {
    console.log('⚠️ Error for unauthorized access:', error.message);
  }

  return true;
}

async function runAllTests() {
  console.log('🚀 Starting Complete API Tests...');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`👤 Test User ID: ${TEST_USER_ID}`);
  console.log(`🤖 OpenAI Model: ${OPENAI_MODEL}`);

  const results = {
    health: await testHealthEndpoint(),
    conversation: await testConversationEndpoints(),
    message: await testMessageEndpoints(),
    memory: await testMemoryEndpoints(),
    errorHandling: await testErrorHandling(),
  };

  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the logs above for details.');
  }
}

// Run tests
runAllTests().catch(console.error);
