const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:3000';
const API_KEY = 'test-api-key-1';

function makeRequest(url, options) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    const req = client.request(url, options, res => {
      let data = '';
      res.on('data', chunk => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

async function testMemorySearch() {
  console.log('🔍 Testing Memory Search...');

  const url = `${BASE_URL}/v1/memory/search?text=test&limit=3`;
  const options = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
    },
  };

  try {
    const response = await makeRequest(url, options);
    console.log('✅ Memory Search Response:', response.status, response.data);
    return response;
  } catch (error) {
    console.error('❌ Memory Search Error:', error.message);
    return null;
  }
}

async function testMemoryAdd() {
  console.log('\n📝 Testing Memory Add...');

  const url = `${BASE_URL}/v1/memory/add`;
  const body = {
    messages: [
      {
        role: 'user',
        content: 'This is a test message for memory storage',
        conversation_id: 1,
      },
    ],
    config: {
      metadata: {
        memoryType: 'semantic',
        userId: 'test-user-1',
      },
      filters: {
        userId: 'test-user-1',
      },
    },
  };

  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
    },
    body: JSON.stringify(body),
  };

  try {
    const response = await makeRequest(url, options);
    console.log('✅ Memory Add Response:', response.status, response.data);
    return response;
  } catch (error) {
    console.error('❌ Memory Add Error:', error.message);
    return null;
  }
}

async function testMemorySearchV2() {
  console.log('\n🔍 Testing Memory Search V2...');

  const url = `${BASE_URL}/v2/memory/search`;
  const body = {
    text: 'test message',
    config: {
      limit: 3,
      filters: {
        memoryType: 'semantic',
        userId: 'test-user-1',
      },
    },
  };

  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
    },
    body: JSON.stringify(body),
  };

  try {
    const response = await makeRequest(url, options);
    console.log('✅ Memory Search V2 Response:', response.status, response.data);
    return response;
  } catch (error) {
    console.error('❌ Memory Search V2 Error:', error.message);
    return null;
  }
}

async function main() {
  console.log('🚀 Starting Memory API Tests...\n');

  // Test memory search
  await testMemorySearch();

  // Test memory add
  await testMemoryAdd();

  // Test memory search v2
  await testMemorySearchV2();

  console.log('\n✅ Memory API tests completed!');
}

main().catch(console.error);
