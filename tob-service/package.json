{"name": "@the-agent/tob-service", "version": "0.1.0", "description": "ToB local deployment service for chat functionality", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "docker:dev": "docker-compose -f docker/development.yml up --build", "docker:supabase": "docker-compose -f docker/supabase.yml up --build", "docker:prod": "docker-compose -f docker/production.yml up --build", "lint": "eslint src --ext .ts", "test": "jest"}, "dependencies": {"@the-agent/chat-store": "workspace:*", "@the-agent/shared": "workspace:*", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "@supabase/supabase-js": "^2.53.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/pg": "^8.10.9", "@types/node": "^20.10.0", "typescript": "^5.3.2", "tsx": "^4.6.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["tob", "chat", "local", "deployment"], "author": "The Agent Team", "license": "MIT"}