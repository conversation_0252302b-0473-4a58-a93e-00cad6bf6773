# ToB Service

ToB local deployment service for chat functionality with support for PostgreSQL and Supabase databases.

## Quick Start

### PostgreSQL Setup

```bash
# Start with Docker Compose (Recommended)
pnpm docker:dev

# Or manual setup
brew services start postgresql
createdb tob_service
psql -d tob_service -f scripts/init-postgresql.sql
pnpm dev
```

### Supabase Setup

```bash
# Start with Docker Compose (Recommended)
pnpm docker:supabase

# Or manual setup
pnpm dev
```

### Production Deployment

```bash
# Enterprise deployment with monitoring
pnpm docker:prod
```

## Testing

### PostgreSQL Testing

```bash
# Run PostgreSQL tests
./scripts/test-postgresql.sh
```

### Supabase Testing

```bash
# Run Supabase tests
./scripts/test-supabase.sh
```

## API Endpoints

### Core Chat APIs

- `POST /v1/chat/completions` - Chat completions with streaming support

### Conversation Management

- `POST /v1/conversation/create` - Create conversation
- `GET /v1/conversation/list` - List conversations
- `POST /v1/conversation/delete` - Delete conversation

### Message Management

- `POST /v2/message/save` - Save message

### Memory & Site Search

- `POST /v1/memory/search` - Search memory
- `POST /v1/memory/add` - Add to memory
- `POST /v2/memory/search` - Advanced memory search
- `POST /v1/message/search_site` - Search site-specific messages

### Health Check

- `GET /health` - Health check

## Configuration

### Environment Variables

#### Required Configuration

- `PORT` - Service port (default: 3000)
- `NODE_ENV` - Environment (development/production)
- `DATABASE_TYPE` - Database type (postgresql/supabase)
- `DATABASE_URL` - PostgreSQL connection string
- `SUPABASE_URL` - Supabase URL (for Supabase mode)
- `SUPABASE_KEY` - Supabase key (for Supabase mode)
- `OPENAI_API_KEY` - OpenAI API key
- `OPENAI_MODEL` - OpenAI model (default: gpt-4)

#### Authentication Configuration

- `AUTH_TYPE` - Authentication type (api_key/jwt/both)
- `API_KEYS` - Comma-separated API keys (for API key auth)
- `JWT_SECRET` - JWT secret (for JWT auth)

#### Optional Configuration

- `CORS_ORIGIN` - CORS origin (default: \*)

### Docker Setup

- `docker/development.yml` - Development environment with PostgreSQL
- `docker/supabase.yml` - Supabase Docker setup
- `docker/production.yml` - Production environment

For detailed Docker configuration and authentication options, see [docker/README.md](docker/README.md).

## Ports

- Service: `http://localhost:3000`
- PostgreSQL: `localhost:5432` (Docker)
- Supabase: `localhost:5432` (Docker)
