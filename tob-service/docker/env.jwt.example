# ToB Service - JWT Authentication Configuration
# Suitable for enterprise deployment with user management

# ===== Basic Configuration =====
PORT=3000
NODE_ENV=production

# ===== Database Configuration =====
DATABASE_URL=************************************************/tob_service
DATABASE_TYPE=postgresql

# ===== OpenAI Configuration =====
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# ===== Authentication Configuration =====
# Authentication type: api_key, jwt, both
AUTH_TYPE=jwt

# JWT Authentication
JWT_SECRET=your-enterprise-jwt-secret
JWT_ISSUER=https://your-company.com
JWT_AUDIENCE=tob-service

# API Key Authentication (used when AUTH_TYPE=api_key or both)
API_KEYS=

# ===== Memory Server Configuration =====
MEMORY_SERVICE_URL=https://memory-server-staging.up.railway.app
MEMORY_SERVICE_API_KEY=e4b1c2d3a4f5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1

# ===== Security Configuration =====
CORS_ORIGIN=https://your-company-domain.com 