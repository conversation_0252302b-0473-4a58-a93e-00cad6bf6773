#!/bin/bash

echo "🚀 ToB Service Quick Start"
echo "=========================="
echo ""
echo "Please select deployment option:"
echo "1) Development Environment (PostgreSQL)"
echo "2) Supabase Version"
echo "3) Production - API Key Authentication"
echo "4) Production - JWT Authentication"
echo "5) Production - Both Authentication Methods"
echo "6) Exit"
echo ""

read -p "Enter your choice (1-6): " choice

case $choice in
    1)
        echo "Starting development environment..."
        echo "Creating .env file for development..."
        cat > .env << EOF
# Development Environment Configuration
PORT=3000
NODE_ENV=development
DATABASE_TYPE=postgresql
DATABASE_URL=************************************************/tob_service
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
AUTH_TYPE=api_key
API_KEYS=test-api-key-1,test-api-key-2
JWT_SECRET=test-jwt-secret
CORS_ORIGIN=*
EOF
        echo "Development environment variables created"
        echo "Please edit .env file to configure OPENAI_API_KEY"
        echo "Then run: docker-compose -f docker/development.yml up --build"
        ;;
    2)
        echo "Starting Supabase version..."
        echo "Creating .env file for Supabase..."
        cat > .env << EOF
# Supabase Environment Configuration
PORT=3000
NODE_ENV=development
DATABASE_TYPE=supabase
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-key
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
AUTH_TYPE=api_key
API_KEYS=test-api-key-1,test-api-key-2
JWT_SECRET=test-jwt-secret
CORS_ORIGIN=*
EOF
        echo "Supabase environment variables created"
        echo "Please edit .env file to configure SUPABASE_URL, SUPABASE_KEY and OPENAI_API_KEY"
        echo "Then run: docker-compose -f docker/supabase.yml up --build"
        ;;
    3)
        echo "Starting production environment (API Key auth)..."
        cp docker/env.api-key.example .env
        echo "API Key authentication config copied"
        echo "Please edit .env file to configure API_KEYS and OPENAI_API_KEY"
        echo "Then run: docker-compose -f docker/production.yml up --build"
        ;;
    4)
        echo "Starting production environment (JWT auth)..."
        cp docker/env.jwt.example .env
        echo "JWT authentication config copied"
        echo "Please edit .env file to configure JWT_SECRET and OPENAI_API_KEY"
        echo "Then run: docker-compose -f docker/production.yml up --build"
        ;;
    5)
        echo "Starting production environment (Both auth methods)..."
        cp docker/env.api-key.example .env
        echo "Both authentication methods config copied"
        echo "Please edit .env file to configure API_KEYS, JWT_SECRET and OPENAI_API_KEY"
        echo "Then run: docker-compose -f docker/production.yml up --build"
        ;;
    6)
        echo "Exiting"
        exit 0
        ;;
    *)
        echo "Invalid choice"
        exit 1
        ;;
esac 