# Docker Configuration

This directory contains Docker deployment configurations for ToB Service.

## Quick Start

### Interactive Setup (Recommended)

```bash
./docker/quick-start.sh
```

### Manual Setup

#### Development Environment

```bash
# Using PostgreSQL
docker-compose -f docker/development.yml up

# Using Supabase
docker-compose -f docker/supabase.yml up
```

#### Production Environment

```bash
# Choose authentication method first, then run:
docker-compose -f docker/production.yml up
```

## Configuration Files

| File              | Purpose                 | Use Case                              |
| ----------------- | ----------------------- | ------------------------------------- |
| `development.yml` | Development environment | Local development, testing            |
| `supabase.yml`    | Supabase version        | Using Supabase as database            |
| `production.yml`  | Production environment  | Enterprise deployment with monitoring |

### Authentication Method Selection

| Authentication | Use Case                          | Config File            |
| -------------- | --------------------------------- | ---------------------- |
| API Key        | Simple deployment, internal tools | `env.api-key.example`  |
| JWT            | User management required          | `env.jwt.example`      |
| Both           | Flexible deployment               | Use either config file |

## Environment Variables

The project provides complete environment variable configurations:

### Production Environment (By Authentication Method)

- `env.api-key.example` - API Key authentication config
- `env.jwt.example` - JWT authentication config

Use the quick start script to automatically copy the appropriate environment variable file:

```bash
./docker/quick-start.sh
```

## Database Selection

- **PostgreSQL**: Suitable for self-hosted, full data control
- **Supabase**: Suitable for quick deployment, no database management needed
