# Authentication Method Selection Guide

## Overview

ToB Service supports two authentication methods: **API Key** and **JWT**. You can configure the service to use either one or both methods simultaneously.

## Authentication Method Comparison

| Authentication | Complexity | Use Case                                     | Advantages                      | Disadvantages           |
| -------------- | ---------- | -------------------------------------------- | ------------------------------- | ----------------------- |
| **API Key**    | ⭐         | Internal tool integration, simple deployment | Simple config, good performance | Limited user management |
| **JWT**        | ⭐⭐       | User management required                     | Flexible, standardized          | Requires JWT management |
| **Both**       | ⭐⭐       | Flexible deployment                          | Supports both methods           | Slightly more complex   |

## Detailed Explanation

### 1. API Key Authentication ⭐

**Use Cases:**

- Internal tool integration
- Simple API calls
- No user management needed
- Quick deployment

**Configuration Example:**

```bash
# Copy configuration
cp docker/env.api-key.example .env

# Edit .env file
AUTH_TYPE=api_key
API_KEYS=your-api-key-1,your-api-key-2
OPENAI_API_KEY=your-openai-key
```

**Usage:**

```bash
curl -H "X-API-Key: your-api-key" http://localhost:3000/v1/chat/completions
```

### 2. JWT Authentication ⭐⭐

**Use Cases:**

- User management required
- Multi-user systems
- Role-based access control needed

**Configuration Example:**

```bash
# Copy configuration
cp docker/env.jwt.example .env

# Edit .env file
AUTH_TYPE=jwt
JWT_SECRET=your-jwt-secret
OPENAI_API_KEY=your-openai-key
```

**Usage:**

```bash
curl -H "Authorization: Bearer your-jwt-token" http://localhost:3000/v1/chat/completions
```

### 3. Both Authentication Methods ⭐⭐

**Use Cases:**

- Flexible deployment scenarios
- Migration between authentication methods
- Support for different client types

**Configuration Example:**

```bash
# Edit .env file
AUTH_TYPE=both
API_KEYS=your-api-key-1,your-api-key-2
JWT_SECRET=your-jwt-secret
OPENAI_API_KEY=your-openai-key
```

## Configuration Options

### AUTH_TYPE Environment Variable

Set the `AUTH_TYPE` environment variable to control authentication:

- `api_key`: Only API Key authentication
- `jwt`: Only JWT authentication
- `both`: Both authentication methods (default)

### Required Environment Variables

#### For API Key Authentication

```bash
AUTH_TYPE=api_key
API_KEYS=key1,key2,key3
OPENAI_API_KEY=your-openai-key
DATABASE_URL=your-database-url
```

#### For JWT Authentication

```bash
AUTH_TYPE=jwt
JWT_SECRET=your-secret-key
OPENAI_API_KEY=your-openai-key
DATABASE_URL=your-database-url
```

#### For Both Methods

```bash
AUTH_TYPE=both
API_KEYS=key1,key2
JWT_SECRET=your-secret-key
OPENAI_API_KEY=your-openai-key
DATABASE_URL=your-database-url
```

## Recommendations

### Quick Start

Choose **API Key Authentication** for simple configuration, suitable for quick deployment and testing.

### Production Deployment

- **Simple Integration**: API Key
- **User Management**: JWT
- **Flexible Deployment**: Both

### Security Considerations

- **Internal Use**: API Key is sufficient
- **Multi-user**: JWT
- **Mixed Environment**: Both

## Migration Guide

If you need to migrate from one authentication method to another:

1. **Backup current configuration**
2. **Set AUTH_TYPE to 'both' for transition period**
3. **Configure both authentication methods**
4. **Test both authentication methods**
5. **Update client code gradually**
6. **Switch to preferred method**

## Important Notes

⚠️ **Important Reminders:**

- Set `AUTH_TYPE` to control which authentication methods are enabled
- Ensure only necessary authentication parameters are configured
- Regularly update authentication keys and secrets
- Monitor authentication failure logs
