version: '3.8'

services:
  # ToB Service
  tob-service:
    build:
      context: .
      dockerfile: docker/Dockerfile.production
    container_name: tob-service
    restart: unless-stopped
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=************************************************/tob_service
      - DATABASE_TYPE=postgresql
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=gpt-4
      - CORS_ORIGIN=${CORS_ORIGIN:-*}
      - API_KEYS=${API_KEYS}
      - JWT_SECRET=${JWT_SECRET}
      - MEMORY_SERVICE_URL=${MEMORY_SERVICE_URL:-https://memory-server-staging.up.railway.app}
      - MEMORY_SERVICE_API_KEY=${MEMORY_SERVICE_API_KEY}
      - LDAP_URL=${LDAP_URL}
      - LDAP_BASE_DN=${LDAP_BASE_DN}
      - LDAP_BIND_DN=${LDAP_BIND_DN}
      - LDAP_BIND_PASSWORD=${LDAP_BIND_PASSWORD}
      - SSO_URL=${SSO_URL}
      - SSO_CLIENT_ID=${SSO_CLIENT_ID}
      - SSO_CLIENT_SECRET=${SSO_CLIENT_SECRET}
      - LOG_LEVEL=info
      - METRICS_ENABLED=true
      - METRICS_PORT=9090
    volumes:
      - ./logs:/app/logs
      - ./backups:/app/backups
    depends_on:
      - postgres
      - redis
    networks:
      - tob-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: tob-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=tob_service
      - POSTGRES_USER=tob_user
      - POSTGRES_PASSWORD=tob_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-postgresql.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - '5432:5432'
    networks:
      - tob-network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U tob_user -d tob_service']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: tob-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - '6379:6379'
    networks:
      - tob-network
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: tob-nginx
    restart: unless-stopped
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - tob-service
    networks:
      - tob-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: tob-prometheus
    restart: unless-stopped
    ports:
      - '9090:9090'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - tob-network

  # Grafana Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: tob-grafana
    restart: unless-stopped
    ports:
      - '3001:3000'
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - tob-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  tob-network:
    driver: bridge
