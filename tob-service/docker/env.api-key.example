# ToB Service - API Key Authentication Configuration
# Suitable for simple deployment, recommended for internal tool integration

# ===== Basic Configuration =====
PORT=3000
NODE_ENV=production

# ===== Database Configuration =====
DATABASE_URL=************************************************/tob_service
DATABASE_TYPE=postgresql

# ===== OpenAI Configuration =====
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# ===== Authentication Configuration =====
# Authentication type: api_key, jwt, both
AUTH_TYPE=api_key

# API Key Authentication
API_KEYS=your-api-key-1,your-api-key-2,your-api-key-3

# JWT Authentication (used when AUTH_TYPE=jwt or both)
JWT_SECRET=

# ===== Memory Server Configuration =====
MEMORY_SERVICE_URL=https://memory-server-staging.up.railway.app
MEMORY_SERVICE_API_KEY=e4b1c2d3a4f5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1

# ===== Security Configuration =====
CORS_ORIGIN=* 