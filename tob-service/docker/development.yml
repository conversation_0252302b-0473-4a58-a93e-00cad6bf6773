version: '3.8'

services:
  tob-service-postgresql:
    build:
      context: .
      dockerfile: docker/Dockerfile
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    depends_on:
      - postgres
    networks:
      - tob-network

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: tob_service
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-postgresql.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - tob-network

volumes:
  postgres_data:

networks:
  tob-network:
    driver: bridge
