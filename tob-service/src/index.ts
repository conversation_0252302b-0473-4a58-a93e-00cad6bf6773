import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import env from './config';
import { databaseService } from './services/database';
import { conversationHandler } from './handlers/conversation';
import { messageHandler } from './handlers/message';
import { memoryHandler } from './handlers/memory';
import { authMiddleware, optionalAuthMiddleware } from './middleware/auth';
import { HTTP_STATUS, ERROR_MESSAGES } from './utils/constants';

const app: express.Application = express();

app.use(helmet());
app.use(
  cors({
    origin: env.CORS_ORIGIN,
    credentials: true,
  })
);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

app.get('/health', optionalAuthMiddleware, async (req, res) => {
  try {
    const dbHealth = await databaseService.healthCheck();

    const status = dbHealth ? 'healthy' : 'unhealthy';
    const statusCode = dbHealth ? HTTP_STATUS.OK : HTTP_STATUS.SERVICE_UNAVAILABLE;

    res.status(statusCode).json({
      status,
      timestamp: new Date().toISOString(),
      services: {
        database: dbHealth ? 'healthy' : 'unhealthy',
      },
    });
  } catch (error) {
    res.status(HTTP_STATUS.SERVICE_UNAVAILABLE).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: (error as Error).message,
    });
  }
});

app.post(
  '/v1/conversation/create',
  authMiddleware,
  conversationHandler.createConversation.bind(conversationHandler)
);
app.post(
  '/v1/conversation/delete',
  authMiddleware,
  conversationHandler.deleteConversation.bind(conversationHandler)
);
app.post(
  '/v1/conversation/update',
  authMiddleware,
  conversationHandler.updateConversation.bind(conversationHandler)
);
app.get(
  '/v1/conversation/list',
  authMiddleware,
  conversationHandler.listConversations.bind(conversationHandler)
);

app.get(
  '/v1/user/sync_data',
  authMiddleware,
  conversationHandler.syncUserData.bind(conversationHandler)
);

app.post('/v2/message/save', authMiddleware, messageHandler.saveMessage.bind(messageHandler));

// Memory endpoints
app.get('/v1/memory/search', authMiddleware, memoryHandler.searchMemory.bind(memoryHandler));
app.post('/v1/memory/add', authMiddleware, memoryHandler.addMemory.bind(memoryHandler));
app.post('/v2/memory/search', authMiddleware, memoryHandler.searchMemoryV2.bind(memoryHandler));

app.use((err: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
    error: ERROR_MESSAGES.DATABASE_ERROR,
    message: env.NODE_ENV === 'development' ? err.message : ERROR_MESSAGES.SERVICE_UNAVAILABLE,
  });
});

app.use('*', (req, res) => {
  res.status(HTTP_STATUS.NOT_FOUND).json({ error: ERROR_MESSAGES.INVALID_REQUEST });
});

process.on('SIGTERM', async () => {
  process.exit(0);
});

process.on('SIGINT', async () => {
  process.exit(0);
});

const PORT = parseInt(env.PORT, 10);

app.listen(PORT, () => {
  console.log(`ToB Service started on port ${PORT}`);
  console.log(`Environment: ${env.NODE_ENV}`);
});
