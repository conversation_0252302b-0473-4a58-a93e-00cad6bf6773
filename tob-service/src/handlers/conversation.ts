import { Response } from 'express';
import { AuthenticatedRequest } from '../types/auth';
import { databaseService } from '../services/database';
import { HTTP_STATUS } from '../utils/constants';
import { UpdateConversationRequest } from '@the-agent/shared';

export class ConversationHandler {
  async createConversation(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }
      const { id } = req.body;

      await databaseService.conversationStore.createConversation(userId, { id, type: 'default' });

      return res.status(HTTP_STATUS.OK).json({ id });
    } catch (error) {
      return res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json({ error: (error as Error).message });
    }
  }

  async deleteConversation(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }
      const { id } = req.body;

      await databaseService.conversationStore.deleteConversation(id, userId);

      return res.status(HTTP_STATUS.OK).json({ deleted: true });
    } catch (error) {
      return res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json({ error: (error as Error).message });
    }
  }

  async listConversations(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }
      const startFrom = parseInt((req.query.startFrom as string) || '0');

      const conversations = await databaseService.conversationStore.listConversations(
        userId,
        startFrom
      );

      return res.status(HTTP_STATUS.OK).json({ conversations });
    } catch (error) {
      return res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json({ error: (error as Error).message });
    }
  }

  async updateConversation(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }
      const updateData = req.body as UpdateConversationRequest;

      await databaseService.conversationStore.updateConversation(userId, updateData);

      return res.status(HTTP_STATUS.OK).json({ success: true });
    } catch (error) {
      return res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json({ error: (error as Error).message });
    }
  }
}

export const conversationHandler = new ConversationHandler();
