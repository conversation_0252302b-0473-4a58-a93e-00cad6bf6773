import { Response } from 'express';
import { AuthenticatedRequest } from '../types/auth';
import { databaseService } from '../services/database';
import { Message, MessageSchema } from '@the-agent/shared';
import { HTTP_STATUS, ERROR_MESSAGES } from '../utils/constants';

export class MessageHandler {
  async saveMessage(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }
      const body = req.body;

      // Validate the message
      let message: Message;
      try {
        message = MessageSchema.parse(body.message);
      } catch (error) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({ error: ERROR_MESSAGES.INVALID_REQUEST });
      }

      await databaseService.messageStore.saveMessage(message, userId);

      return res.status(HTTP_STATUS.OK).json({ success: true });
    } catch (error) {
      return res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json({ error: (error as Error).message });
    }
  }
}

export const messageHandler = new MessageHandler();
