import { Response } from 'express';
import { AuthenticatedRequest } from '../types/auth';
import { HTTP_STATUS } from '../utils/constants';
import { syncUserData } from '@the-agent/chat-store';
import { databaseService } from '../services/database';

export class UserHandler {
  async syncUserData(req: AuthenticatedRequest, res: Response) {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }
    const startFrom = parseInt((req.query.startFrom as string) || '0');
    const { conversations, miniapps } = await syncUserData({
      userId,
      startFrom,
      conversationStore: databaseService.conversationStore,
      miniappStore: databaseService.miniappStore,
    });
    return res.status(HTTP_STATUS.OK).json({ conversations, miniapps });
  }
}
