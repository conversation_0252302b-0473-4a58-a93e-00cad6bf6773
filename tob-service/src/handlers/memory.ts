import { Response } from 'express';
import { AuthenticatedRequest } from '../types/auth';
import { HTTP_STATUS, ERROR_MESSAGES } from '../utils/constants';
import {
  Message,
  MemoryType,
  Entity,
  AddMemoryOptions,
  extractTextFromContent,
} from '@the-agent/shared';
import { searchMemory, addMessagesToMemory } from '../utils/memory';

export class MemoryHandler {
  async searchMemory(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: {
            message: 'Unauthorized',
            type: 'unauthorized',
          },
        });
      }

      const query = req.query;
      const { text, limit, ...entityParams } = query;

      if (!text || typeof text !== 'string') {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: {
            message: 'Invalid request: text is required',
            type: 'invalid_request',
          },
        });
      }

      const filters: Entity = { userId };

      const entityParamKeys: (keyof Entity)[] = [
        'conversationId',
        'agentId',
        'runId',
        'taskId',
        'workflowId',
        'hostname',
        'memoryType',
      ];

      for (const key of entityParamKeys) {
        if (entityParams[key]) {
          if (key === 'memoryType') {
            filters[key] = entityParams[key] as MemoryType;
          } else {
            filters[key] = entityParams[key] as string;
          }
        }
      }

      const result = await searchMemory(text, {
        limit: parseInt((limit as string) ?? '3'),
        filters,
      });

      return res.status(HTTP_STATUS.OK).json({
        results: result.results,
        relations: result.relations,
      });
    } catch (error) {
      console.error('Search memory error:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: {
          message: ERROR_MESSAGES.DATABASE_ERROR,
          type: 'database_error',
        },
      });
    }
  }

  async addMemory(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: {
            message: 'Unauthorized',
            type: 'unauthorized',
          },
        });
      }

      const { messages, config } = req.body;

      if (!messages || !Array.isArray(messages)) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: {
            message: 'Invalid request',
            type: 'invalid_request',
          },
        });
      }

      const validMessages: Message[] = messages.filter((message: Message) => {
        const content = extractTextFromContent(message.content);
        if (content && content.length > 0) {
          message.content = content;
          return true;
        }
        return false;
      });

      // For site memory, don't add user-specific metadata
      const isSiteMemory = config?.metadata?.memoryType === 'site';

      const memoryConfig: AddMemoryOptions = {
        metadata: {
          ...config?.metadata,
          // Only add userId for non-site memory
          ...(isSiteMemory ? {} : { userId }),
        },
        filters: {
          ...config?.filters,
          // Only add userId for non-site memory
          ...(isSiteMemory ? {} : { userId }),
        },
      };

      const result = await addMessagesToMemory(validMessages, memoryConfig);

      return res.status(HTTP_STATUS.OK).json(result);
    } catch (error) {
      console.error('Add memory error:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: {
          message: ERROR_MESSAGES.DATABASE_ERROR,
          type: 'database_error',
        },
      });
    }
  }

  async searchMemoryV2(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: {
            message: 'Unauthorized',
            type: 'unauthorized',
          },
        });
      }

      const body = req.body;
      const {
        text,
        config: { limit, filters },
      } = body;

      if (!text || typeof text !== 'string') {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: {
            message: 'Invalid request: text is required',
            type: 'invalid_request',
          },
        });
      }

      // For site memory searches, don't add userId
      const isSiteMemory = filters.memoryType === 'site';

      const newFilters: Entity = {};
      const entityParamKeys: (keyof Entity)[] = [
        'conversationId',
        'agentId',
        'runId',
        'taskId',
        'workflowId',
        'hostname',
        'memoryType',
      ];

      // Only add userId for non-site memory
      if (!isSiteMemory) {
        newFilters.userId = userId;
      }

      for (const key of entityParamKeys) {
        if (filters[key]) {
          if (key === 'memoryType') {
            newFilters[key] = filters[key] as MemoryType;
          } else {
            newFilters[key] = filters[key] as string;
          }
        }
      }

      const result = await searchMemory(text, {
        limit,
        filters: newFilters,
      });

      return res.status(HTTP_STATUS.OK).json({
        results: result.results,
        relations: result.relations,
      });
    } catch (error) {
      console.error('Search memory V2 error:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: {
          message: ERROR_MESSAGES.DATABASE_ERROR,
          type: 'database_error',
        },
      });
    }
  }
}

export const memoryHandler = new MemoryHandler();
