import { Message, SearchMemoryOptions, AddMemoryOptions, SearchResult } from '@the-agent/shared';
import env from '../config';

export async function searchMemory(
  text: string,
  config: SearchMemoryOptions
): Promise<SearchResult> {
  if (!config.limit || config.limit <= 0) {
    return {
      results: [],
      relations: [],
    };
  }

  const response = await fetch(`${env.MEMORY_SERVICE_URL}/memory/search`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${env.MEMORY_SERVICE_API_KEY}`,
    },
    body: JSON.stringify({
      text,
      config,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Memory server search failed:', {
      status: response.status,
      error: errorText,
      config,
      text,
    });
    throw new Error(`Memory server search failed: ${response.status} - ${errorText}`);
  }
  return response.json() as Promise<SearchResult>;
}

export async function addMessagesToMemory(
  messages: Message[],
  config: AddMemoryOptions
): Promise<SearchResult> {
  const response = await fetch(`${env.MEMORY_SERVICE_URL}/memory/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${env.MEMORY_SERVICE_API_KEY}`,
    },
    body: JSON.stringify({
      messages,
      config,
    }),
  });
  if (!response.ok) {
    const errorText = await response.text();
    console.error('Memory server add failed:', {
      status: response.status,
      error: errorText,
      config,
      messages,
    });
    return {
      results: [],
      relations: [],
    };
  }
  return (await response.json()) as SearchResult;
}

export function extractSiteIdFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (_error) {
    return url;
  }
}
