// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  INVALID_API_KEY: 'Invalid API key',
  API_KEY_REQUIRED: 'API key required',
  INVALID_JWT_TOKEN: 'Invalid JWT token',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions',
  DATABASE_ERROR: 'Database operation failed',
  OPENAI_ERROR: 'OpenAI API error',
  SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
  INVALID_REQUEST: 'Invalid request',
  USER_NOT_FOUND: 'User not found',
  CONVERSATION_NOT_FOUND: 'Conversation not found',
  MESSAGE_NOT_FOUND: 'Message not found',
} as const;

// Content Types
export const CONTENT_TYPES = {
  JSON: 'application/json',
  TEXT: 'text/plain',
  STREAM: 'text/event-stream',
} as const;

// Authentication Types
export const AUTH_TYPES = {
  API_KEY: 'api_key',
  JWT: 'jwt',
  BOTH: 'both',
} as const;
