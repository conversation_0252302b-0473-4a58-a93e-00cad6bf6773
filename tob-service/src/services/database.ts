import {
  createMessageStore,
  createConversationStore,
  createMiniAppStore,
} from '@the-agent/chat-store';
import env from '../config';

export class DatabaseService {
  public messageStore;
  public conversationStore;
  public miniappStore;

  constructor() {
    if (env.DATABASE_TYPE === 'supabase') {
      if (!env.SUPABASE_URL || !env.SUPABASE_KEY) {
        throw new Error('Supabase URL and key are required for Supabase database type');
      }

      this.messageStore = createMessageStore({
        type: 'supabase',
        supabaseUrl: env.SUPABASE_URL,
        supabaseKey: env.SUPABASE_KEY,
      });

      this.conversationStore = createConversationStore({
        type: 'supabase',
        supabaseUrl: env.SUPABASE_URL,
        supabaseKey: env.SUPABASE_KEY,
      });
      this.miniappStore = createMiniAppStore({
        type: 'supabase',
        supabaseUrl: env.SUPABASE_URL,
        supabaseKey: env.SUPABASE_KEY,
      });
    } else if (env.DATABASE_TYPE === 'postgresql') {
      if (!env.DATABASE_URL) {
        throw new Error('DATABASE_URL is required for PostgreSQL database type');
      }

      const url = new URL(env.DATABASE_URL);

      this.messageStore = createMessageStore({
        type: 'postgresql',
        host: url.hostname,
        port: parseInt(url.port, 10),
        database: url.pathname.slice(1),
        username: url.username,
        password: url.password,
        ssl: env.NODE_ENV === 'production',
      });

      this.conversationStore = createConversationStore({
        type: 'postgresql',
        host: url.hostname,
        port: parseInt(url.port, 10),
        database: url.pathname.slice(1),
        username: url.username,
        password: url.password,
        ssl: env.NODE_ENV === 'production',
      });
      this.miniappStore = createMiniAppStore({
        type: 'postgresql',
        host: url.hostname,
        port: parseInt(url.port, 10),
        database: url.pathname.slice(1),
        username: url.username,
        password: url.password,
        ssl: env.NODE_ENV === 'production',
      });
    } else {
      throw new Error(`Unsupported database type: ${env.DATABASE_TYPE}`);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.conversationStore.listConversations('test-user', 0);
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }
}

export const databaseService = new DatabaseService();
