import { Request, Response, NextFunction } from 'express';
import { HTTP_STATUS, ERROR_MESSAGES } from '../utils/constants';
import env from '../config';

export interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
  };
}

function validateApiKey(apiKey: string): string | null {
  try {
    const validApiKeys = env.API_KEYS.split(',').map(key => key.trim());
    if (validApiKeys.includes(apiKey)) {
      return Buffer.from(apiKey).toString('base64').slice(0, 16);
    }
    return null;
  } catch (_error) {
    console.error('API key authentication error:', _error);
    return null;
  }
}

function validateJWT(token: string): string | null {
  try {
    const validTokens = env.JWT_SECRET.split(',').map(t => t.trim());
    if (validTokens.includes(token)) {
      return Buffer.from(token).toString('base64').slice(0, 16);
    }
    return null;
  } catch (_error) {
    return null;
  }
}

export function authMiddleware(req: AuthenticatedRequest, res: Response, next: NextFunction): void {
  const apiKey = req.headers['x-api-key'] as string;
  const authHeader = req.headers['authorization'] as string;

  let userId: string | null = null;

  if (env.AUTH_TYPE === 'api_key' || env.AUTH_TYPE === 'both') {
    if (apiKey) {
      userId = validateApiKey(apiKey);
    }
  }

  if (!userId && (env.AUTH_TYPE === 'jwt' || env.AUTH_TYPE === 'both')) {
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.slice(7);
      userId = validateJWT(token);
    }
  }

  if (!userId) {
    res.status(HTTP_STATUS.UNAUTHORIZED).json({
      error: ERROR_MESSAGES.API_KEY_REQUIRED,
      message: `Authentication required. Use ${env.AUTH_TYPE === 'api_key' ? 'X-API-Key header' : env.AUTH_TYPE === 'jwt' ? 'Bearer token' : 'either X-API-Key header or Bearer token'}`,
    });
    return;
  }

  req.user = { userId };
  next();
}

export function optionalAuthMiddleware(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void {
  const apiKey = req.headers['x-api-key'] as string;
  const authHeader = req.headers['authorization'] as string;

  let userId: string | null = null;

  if (env.AUTH_TYPE === 'api_key' || env.AUTH_TYPE === 'both') {
    if (apiKey) {
      userId = validateApiKey(apiKey);
    }
  }

  if (!userId && (env.AUTH_TYPE === 'jwt' || env.AUTH_TYPE === 'both')) {
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.slice(7);
      userId = validateJWT(token);
    }
  }

  if (userId) {
    req.user = { userId };
  }

  next();
}
