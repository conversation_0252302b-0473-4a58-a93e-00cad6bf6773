import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config();

const envSchema = z.object({
  PORT: z.string().default('3000'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

  // Database Configuration
  DATABASE_TYPE: z.enum(['supabase', 'postgresql']).default('supabase'),
  SUPABASE_URL: z.string().optional(),
  SUPABASE_KEY: z.string().optional(),
  DATABASE_URL: z.string().optional(),

  // OpenAI Configuration
  OPENAI_API_KEY: z.string().default('sk-test-key-for-development'),
  OPENAI_MODEL: z.string().default('gpt-4'),

  // Memory Server Configuration
  MEMORY_SERVICE_URL: z.string(),
  MEMORY_SERVICE_API_KEY: z.string(),

  // CORS Configuration
  CORS_ORIGIN: z.string().default('*'),

  // Authentication
  API_KEYS: z.string(),
  JWT_SECRET: z.string(),

  AUTH_TYPE: z.enum(['api_key', 'jwt', 'both']).default('both'),
});

// Parse and validate environment variables
const env = envSchema.parse(process.env);

export default env;
