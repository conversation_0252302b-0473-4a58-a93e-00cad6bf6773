# The Agent - AI Agent Project

A comprehensive AI agent system featuring browser extensions, web applications, Telegram bots, and multiple components that provide intelligent conversation, tool calling, and automation capabilities.

## Project Structure

```
the-agent/
├── api-cf/                 # Cloudflare Workers API Backend
│   ├── src/
│   │   ├── handlers/       # API Handlers
│   │   ├── services/       # Service Layer
│   │   ├── d1/            # D1 Database Operations
│   │   └── types/         # Type Definitions
│   └── wrangler.toml      # Cloudflare Configuration
│
├── extension/              # Browser Extension
│   ├── src/
│   │   ├── sidepanel/     # Sidebar Interface
│   │   ├── background/    # Background Scripts
│   │   ├── agents/        # Agent Implementations
│   │   ├── tools/         # Tool Executors
│   │   └── storages/      # IndexedDB Storage
│   └── package.json
│
├── web/                    # Web Application
│   ├── src/
│   │   ├── app/           # Next.js Application
│   │   ├── components/    # React Components
│   │   └── lib/           # Utility Libraries
│   └── package.json
│
├── landing/                # Landing Page
│   ├── app/               # Next.js Pages
│   ├── components/        # Components
│   └── package.json
│
├── memory-server/          # Memory Service
│   ├── src/
│   │   ├── agents/        # Agents
│   │   ├── api/           # API Routes
│   │   └── storage/       # Vector Storage
│   └── package.json
│
├── tg-bot/                 # Telegram Bot
│   ├── src/
│   └── package.json
│
├── tob-service/            # Service Component
│   ├── src/
│   └── package.json
│
└── packages/               # Shared Packages
    ├── shared/            # Shared Types and Utilities
    ├── chat-store/        # Chat Storage
    └── telegram/          # Telegram Related
```

## Core Features

### 🤖 Multi-Agent Architecture

- **Planner Agent**: Task planning and decomposition
- **Simple Agent**: Simple task execution
- **Foreach Agent**: Loop task processing
- **Loop Agent**: Conditional loop control
- **Condition Agent**: Conditional logic
- **Browser Agent**: Browser automation

### 🌐 Browser Extension

- Intelligent conversation interface
- Web content analysis
- Tool calling capabilities
- Local data storage (IndexedDB)
- Multi-language support

### 💬 Chat Functionality

- Integration with OpenAI/DeepSeek/Claude and other LLM models
- Tool calling support
- Multi-modal content processing (text + images)
- Conversation history management
- Real-time streaming responses

### 🛠️ Tool Integration

- Web browsing and screenshots
- File upload/download
- Code execution
- Website memory storage
- Custom tool extensions

### 📱 Multi-Platform Support

- Browser extensions (Chrome/Firefox)
- Web applications
- Telegram bots
- Mobile adaptation

## Quick Start

### Requirements

- Node.js 18+
- pnpm
- Chrome browser (for extension development)

### Installation

```bash
# Install pnpm (if not installed)
npm install -g pnpm

# Clone the project
git clone https://github.com/yourusername/the-agent.git
cd the-agent

# Install all dependencies
pnpm install
```

> **Note**: The `pnpm install` command automatically runs a `postinstall` script that builds the shared packages (`@the-agent/shared` and `@the-agent/chat-store`) after dependency installation. This ensures that all dependent packages have access to the compiled TypeScript definitions and JavaScript files.

### Development Setup

1. **Configure Environment Variables**

Create a `.env` file:

```bash
# API Keys
OPENAI_API_KEY=your_openai_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Database
DATABASE_URL=your_database_url

# Other Configuration
BACKEND_URL=http://localhost:8787
WEB_URL=http://localhost:3000
```

2. **Start Development Services**

```bash
# Start all services
pnpm dev

# Or start individually
pnpm dev:extension    # Browser extension
pnpm dev:web         # Web application
pnpm dev:landing     # Landing page
pnpm dev:memory      # Memory service
```

### Build and Deploy

```bash
# Build all projects
pnpm build

# Deploy to Cloudflare
pnpm deploy:cf

# Package extension
pnpm build:extension
```

### Package Management

This project uses **pnpm workspaces** for monorepo management. The workspace includes:

- **Shared packages** (`packages/shared`, `packages/chat-store`): Common utilities and types
- **Applications** (`web`, `extension`, `landing`, etc.): Individual applications
- **Services** (`api-cf`, `memory-server`, `tg-bot`, etc.): Backend services

#### Postinstall Script

The `postinstall` script in the root `package.json` automatically runs after `pnpm install`:

```json
{
  "scripts": {
    "postinstall": "pnpm --filter @the-agent/shared build && pnpm --filter @the-agent/chat-store build"
  }
}
```

This script ensures that:

1. **Shared packages are built** after dependency installation
2. **TypeScript definitions** are available to dependent packages
3. **Workspace dependencies** are properly linked
4. **Development environment** is ready immediately after installation

#### Workspace Commands

```bash
# Build specific packages
pnpm --filter @the-agent/shared build
pnpm --filter @the-agent/chat-store build

# Run dev mode for specific packages
pnpm --filter web dev
pnpm --filter extension dev

# Install dependencies for specific package
pnpm --filter web install
```

## Development Guide

### Extension Development

Browser extension uses the Plasmo framework:

```typescript
// Sidebar component
export const SidePanel = () => {
  return (
    <div>
      <ConversationList />
      <ChatInterface />
    </div>
  );
};
```

### API Development

Cloudflare Workers API:

```typescript
// API handler
export class ChatHandler extends OpenAPIRoute {
  async handle(c: Context) {
    const { message } = await c.req.json();
    // Handle chat request
  }
}
```

### Agent Development

```typescript
// Custom agent
export class CustomAgent extends BaseAgent {
  async execute(task: Task): Promise<Result> {
    // Implement task logic
  }
}
```

## Architecture Design

### Multi-Agent System

- **Task Decomposition**: Complex tasks automatically decomposed into subtasks
- **Recursive Execution**: Support for nested tasks and conditional control
- **Context Management**: Flexible message and state passing
- **Tool Integration**: Unified tool calling interface

### Data Flow

createNewConversationWithType1. User Input → Task Parsing 2. Agent Selection → Task Execution 3. Tool Calling → Result Processing 4. Context Update → Response Generation

## Contributing

1. Fork the project
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/example)
- 📖 Documentation: [View complete docs](https://docs.example.com)
