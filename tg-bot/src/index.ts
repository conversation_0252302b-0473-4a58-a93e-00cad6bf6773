import { CouponBot } from './bot';
import { loadConfig } from './config';
import { logger } from './logger';

async function main() {
  try {
    // Load configuration
    const config = loadConfig();
    logger.info('Configuration loaded successfully');

    // Create and start the bot
    const bot = new CouponBot(config);

    // Graceful shutdown handling
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      await bot.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      await bot.stop();
      process.exit(0);
    });

    // Start the bot
    await bot.start();
  } catch (error) {
    logger.error(
      `Failed to start application: ${error instanceof Error ? error.message : String(error)}`
    );
    process.exit(1);
  }
}

// Start the application
main().catch(error => {
  logger.error(`Unhandled error: ${error instanceof Error ? error.message : String(error)}`);
  process.exit(1);
});
