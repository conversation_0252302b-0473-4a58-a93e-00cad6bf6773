import { config as dotenvConfig } from 'dotenv';

// Load environment variables from .env file
dotenvConfig();

export interface Config {
  botToken: string;
  apiBaseUrl: string;
  apiKey: string;
  campaign: string;
  credits: number;
  dailySchedule: string;
  chatId?: number;
}

function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    throw new Error(`Required environment variable ${name} is not set`);
  }
  return value;
}

function getOptionalEnvVar(name: string, defaultValue?: string): string | undefined {
  return process.env[name] || defaultValue;
}

function getNumberEnvVar(name: string, defaultValue?: number): number {
  const value = process.env[name];
  if (!value) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Required environment variable ${name} is not set`);
  }

  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${name} must be a valid number`);
  }

  return parsed;
}

export function loadConfig(): Config {
  const config: Config = {
    botToken: getRequiredEnvVar('BOT_TOKEN'),
    apiBaseUrl: getRequiredEnvVar('API_BASE_URL'),
    apiKey: getRequiredEnvVar('API_KEY'),
    campaign: getOptionalEnvVar('CAMPAIGN', 'daily') || 'daily',
    credits: getNumberEnvVar('CREDITS', 100),
    dailySchedule: getOptionalEnvVar('DAILY_SCHEDULE', '0 9 * * *') || '0 9 * * *', // Default: 9 AM daily
  };

  if (process.env.CHAT_ID) {
    config.chatId = parseInt(process.env.CHAT_ID, 10);
  }

  return config;
}
