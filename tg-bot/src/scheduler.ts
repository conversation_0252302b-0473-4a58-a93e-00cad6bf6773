import * as cron from 'node-cron';
import { ApiClient, CouponRequest } from './api-client';
import { logger } from './logger';

export interface SchedulerConfig {
  apiClient: ApiClient;
  apiKey: string;
  campaign: string;
  credits: number;
  schedule: string; // cron schedule expression
  onCouponGenerated?: (couponCode: string) => Promise<void>;
}

export class CouponScheduler {
  private config: SchedulerConfig;
  private task: cron.ScheduledTask | undefined;

  constructor(config: SchedulerConfig) {
    this.config = config;
  }

  start(): void {
    if (this.task) {
      logger.warn('Scheduler is already running');
      return;
    }

    logger.info(`Starting coupon scheduler with cron: ${this.config.schedule}`);

    this.task = cron.schedule(
      this.config.schedule,
      async () => {
        await this.generateDailyCoupon();
      },
      {
        scheduled: true,
        timezone: 'UTC',
      }
    );

    logger.info('Coupon scheduler started successfully');
  }

  stop(): void {
    if (this.task) {
      this.task.stop();
      this.task = undefined;
      logger.info('Coupon scheduler stopped');
    }
  }

  private async generateDailyCoupon(): Promise<void> {
    try {
      logger.info('Generating daily coupon code...');

      const request: CouponRequest = {
        campaign: this.config.campaign,
        credits: this.config.credits,
      };

      const response = await this.config.apiClient.generateCouponCode(request, this.config.apiKey);

      if (response.coupon_code) {
        logger.info(`Coupon code generated successfully: ${response.coupon_code}`);

        // Call the callback if provided
        if (this.config.onCouponGenerated) {
          await this.config.onCouponGenerated(response.coupon_code);
        }
      } else {
        logger.error(`Failed to generate coupon code: ${response.message || 'Unknown error'}`);
      }
    } catch (error) {
      logger.error(
        `Error generating daily coupon: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  // Method to manually trigger coupon generation (for testing)
  async generateNow(): Promise<void> {
    await this.generateDailyCoupon();
  }
}
