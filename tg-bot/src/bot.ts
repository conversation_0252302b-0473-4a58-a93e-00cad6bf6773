import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HttpError } from 'grammy';
import { promises as fs } from 'fs';
import path from 'path';
import { ApiClient } from './api-client';
import { CouponScheduler, SchedulerConfig } from './scheduler';
import { logger } from './logger';

export interface BotConfig {
  botToken: string;
  apiBaseUrl: string;
  apiKey: string;
  campaign: string;
  credits: number;
  dailySchedule: string; // cron expression for daily generation
  chatId?: number; // specific chat ID to pin messages (optional)
}

interface BotState {
  lastPinnedMessageId?: number;
  chatId?: number;
}

export class CouponBot {
  private bot: Bot;
  private apiClient: ApiClient;
  private scheduler!: CouponScheduler;
  private config: BotConfig;
  private lastPinnedMessageId?: number; // Track the last pinned message by bot
  private readonly stateFilePath: string;

  constructor(config: BotConfig) {
    this.config = config;
    this.bot = new Bot(config.botToken);
    this.apiClient = new ApiClient(config.apiBaseUrl);
    this.stateFilePath = path.join(process.cwd(), 'bot-state.json');

    this.setupScheduler();
    this.setupBotCommands();
    this.setupErrorHandling();
  }

  private async loadState(): Promise<void> {
    try {
      const stateData = await fs.readFile(this.stateFilePath, 'utf-8');
      const state: BotState = JSON.parse(stateData);

      // Only restore state if it's for the same chat
      if (state.chatId === this.config.chatId && state.lastPinnedMessageId !== undefined) {
        this.lastPinnedMessageId = state.lastPinnedMessageId;
        logger.info(`State restored: lastPinnedMessageId=${this.lastPinnedMessageId}`);
      } else {
        logger.info('Chat ID changed or no previous message ID, not restoring previous state');
      }
    } catch {
      logger.info('No previous state found or failed to load state, starting fresh');
    }
  }

  private async saveState(): Promise<void> {
    try {
      const state: BotState = {};

      if (this.lastPinnedMessageId !== undefined) {
        state.lastPinnedMessageId = this.lastPinnedMessageId;
      }

      if (this.config.chatId !== undefined) {
        state.chatId = this.config.chatId;
      }

      await fs.writeFile(this.stateFilePath, JSON.stringify(state, null, 2));
      logger.debug(`State saved: lastPinnedMessageId=${this.lastPinnedMessageId}`);
    } catch (error) {
      logger.warn(
        `Failed to save state: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private setupScheduler(): void {
    const schedulerConfig: SchedulerConfig = {
      apiClient: this.apiClient,
      apiKey: this.config.apiKey,
      campaign: this.config.campaign,
      credits: this.config.credits,
      schedule: this.config.dailySchedule,
      onCouponGenerated: async (couponCode: string) => {
        await this.handleCouponGenerated(couponCode);
      },
    };

    this.scheduler = new CouponScheduler(schedulerConfig);
  }

  private setupBotCommands(): void {
    // Start command
    this.bot.command('start', async ctx => {
      const welcomeMessage = `
🎉 Welcome to the MIZU Coupon Bot!

This bot automatically generates daily coupon codes for you.
      `;

      await ctx.reply(welcomeMessage);
    });
  }

  private setupErrorHandling(): void {
    this.bot.catch(err => {
      const ctx = err.ctx;
      logger.error(`Error while handling update ${ctx.update.update_id}:`);

      const e = err.error;
      if (e instanceof GrammyError) {
        logger.error(`Error in request: ${e.description}`);
      } else if (e instanceof HttpError) {
        logger.error(`Could not contact Telegram: ${e}`);
      } else {
        logger.error(`Unknown error: ${e}`);
      }
    });
  }

  private async handleCouponGenerated(couponCode: string): Promise<void> {
    try {
      const dollarAmount = this.config.credits / 1_000_000;
      const message = `
🎁 Daily Coupon Code Generated!

🎫 Coupon: \`${couponCode}\`
💰 Credits: $${dollarAmount}

Use this code to get $${dollarAmount} credits!
      `;

      // If specific chat ID is provided, send to that chat
      if (this.config.chatId) {
        // First, unpin the previous bot message if exists
        if (this.lastPinnedMessageId) {
          try {
            await this.bot.api.unpinChatMessage(this.config.chatId, this.lastPinnedMessageId);
            logger.info(`Previous bot message ${this.lastPinnedMessageId} unpinned`);
          } catch (unpinError) {
            logger.warn(
              `Failed to unpin previous bot message: ${unpinError instanceof Error ? unpinError.message : String(unpinError)}`
            );
          }
        }

        // Send the new coupon message
        const sentMessage = await this.bot.api.sendMessage(this.config.chatId, message, {
          parse_mode: 'Markdown',
        });

        // Pin the new message and store its ID
        try {
          await this.bot.api.pinChatMessage(this.config.chatId, sentMessage.message_id, {
            disable_notification: false,
          });
          this.lastPinnedMessageId = sentMessage.message_id;
          await this.saveState(); // Persist the state
          logger.info(`Coupon message sent and pinned to chat ${this.config.chatId}`);
        } catch (pinError) {
          logger.warn(
            `Coupon message sent but failed to pin: ${pinError instanceof Error ? pinError.message : String(pinError)}`
          );
          logger.info(`Coupon message sent to chat ${this.config.chatId} (pinning failed)`);
        }
      } else {
        logger.warn('No chat ID specified, coupon generated but not sent');
      }
    } catch (error) {
      logger.error(
        `Error handling coupon generation: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  async start(): Promise<void> {
    try {
      logger.info('Starting MIZU Coupon Bot...');

      // Load previous state before starting
      await this.loadState();

      // Start the scheduler
      this.scheduler.start();

      // Start the bot
      await this.bot.start();

      logger.info('Bot started successfully');
    } catch (error) {
      logger.error(
        `Failed to start bot: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  async stop(): Promise<void> {
    try {
      logger.info('Stopping MIZU Coupon Bot...');

      // Stop the scheduler
      this.scheduler.stop();

      // Stop the bot
      await this.bot.stop();

      logger.info('Bot stopped successfully');
    } catch (error) {
      logger.error(`Error stopping bot: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
