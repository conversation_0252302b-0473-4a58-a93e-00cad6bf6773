import axios, { AxiosError } from 'axios';

export interface CouponRequest {
  campaign: string;
  credits: number;
}

export interface CouponResponse {
  coupon_code: string;
  success?: boolean;
  message?: string;
}

export class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  async generateCouponCode(request: CouponRequest, apiKey: string): Promise<CouponResponse> {
    try {
      const response = await axios.post<CouponResponse>(
        `${this.baseUrl}/v1/gen_coupon_code`,
        request,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': apiKey,
          },
          timeout: 10000, // 10 second timeout
        }
      );

      return response.data;
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        throw new Error(
          `API Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`
        );
      }
      throw new Error(
        `Unexpected error: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}
