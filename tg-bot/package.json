{"name": "tg-bot", "version": "1.0.0", "description": "Telegram bot for daily coupon code generation", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "clean": "rm -rf dist"}, "keywords": ["telegram", "bot", "grammy", "typescript"], "author": "MIZU", "license": "MIT", "dependencies": {"grammy": "^1.21.1", "node-cron": "^3.0.3", "dotenv": "^16.3.1", "axios": "^1.6.2"}, "devDependencies": {"@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "typescript": "^5.3.3", "tsx": "^4.6.2"}}