# MIZU Telegram Coupon Bot

A TypeScript-based Telegram bot that automatically generates daily coupon codes using the Grammy framework.

## Features

- **Daily Scheduled Coupon Generation**: Automatically generates coupon codes at specified times
- **Message Pinning**: Pins generated coupon codes to the chat for visibility
- **Manual Generation**: Allows manual coupon generation via `/generate` command
- **Comprehensive Commands**: Includes `/start`, `/status`, `/help`, and `/generate` commands
- **Error Handling**: Robust error handling and logging
- **TypeScript**: Fully typed with strict TypeScript configuration

## Installation

1. Clone the repository and navigate to the `tg-bot` directory:

```bash
cd tg-bot
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file based on the example below:

```env
# Telegram Bot Configuration
BOT_TOKEN=your_telegram_bot_token_here

# API Configuration
API_BASE_URL=https://your-api-domain.com
API_KEY=mysta_bot_api_key

# Coupon Configuration
CAMPAIGN=summer2024
CREDITS=500

# Schedule Configuration (cron format)
# Default: 0 9 * * * (9 AM daily)
DAILY_SCHEDULE=0 9 * * *

# Optional: Specific chat ID to send messages to
# If not set, the bot will only generate coupons but not send them
CHAT_ID=your_chat_id_here
```

## Configuration

### Environment Variables

| Variable         | Required | Default     | Description                            |
| ---------------- | -------- | ----------- | -------------------------------------- |
| `BOT_TOKEN`      | Yes      | -           | Telegram bot token from @BotFather     |
| `API_BASE_URL`   | Yes      | -           | Base URL for the coupon generation API |
| `API_KEY`        | Yes      | -           | Mysta API key for the bot              |
| `CAMPAIGN`       | No       | `daily`     | Campaign name for coupon generation    |
| `CREDITS`        | No       | `100`       | Number of credits per coupon           |
| `DAILY_SCHEDULE` | No       | `0 9 * * *` | Cron expression for daily generation   |
| `CHAT_ID`        | No       | -           | Specific chat ID to send messages to   |

### Cron Schedule Examples

- `0 9 * * *` - Daily at 9:00 AM
- `0 12 * * *` - Daily at 12:00 PM
- `0 18 * * 1-5` - Weekdays at 6:00 PM
- `0 10 * * 1` - Weekly on Monday at 10:00 AM

## Usage

### Development Mode

```bash
npm run dev
```

### Production Mode

```bash
npm run build
npm start
```

### Bot Commands

- `/start` - Initialize the bot and show welcome message
- `/generate` - Generate a coupon code immediately
- `/status` - Check current bot status and configuration
- `/help` - Show help information

## API Integration

The bot integrates with the coupon generation API endpoint:

```
POST /v1/gen_coupon_code
Content-Type: application/json

{
  "bot_token": "telegram_bot_token",
  "campaign": "campaign_name",
  "credits": 500
}
```

**Response Format:**

```json
{
  "coupon_code": "SUMMER2025-A63FB603"
}
```

## Architecture

The bot consists of several key components:

- **`CouponBot`**: Main bot class that handles Telegram interactions
- **`CouponScheduler`**: Handles scheduled coupon generation
- **`ApiClient`**: Manages API communication
- **`logger`**: Centralized logging utility

## Error Handling

The bot includes comprehensive error handling:

- API request failures
- Telegram API errors
- Scheduled task failures
- Graceful shutdown on SIGINT/SIGTERM

## Development

### File Structure

```
src/
├── index.ts          # Main entry point
├── bot.ts            # Telegram bot implementation
├── scheduler.ts      # Coupon scheduling logic
├── api-client.ts     # API communication
├── config.ts         # Configuration management
└── logger.ts         # Logging utility
```

### Scripts

- `npm run build` - Build TypeScript to JavaScript
- `npm run start` - Start the production build
- `npm run dev` - Start in development mode with hot reload
- `npm run clean` - Remove build artifacts

## License

MIT License
